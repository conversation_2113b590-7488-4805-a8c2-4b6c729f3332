package com.iflytek.lynxiao.data.dto.action.dedup;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 索引去重消息体
 * 
 * <AUTHOR>
 * @date 2025/09/02
 */
@Getter
@Setter
public class BucketIdxDedupMessage {

    /**
     * 索引数据库代码
     */
    private String bucketCode;

    /**
     * 召回配置
     */
    private RecallConfig recallConfig;

    @Data
    public static class RecallConfig {

        /**
         * 向量相似度搜索返回的最大结果数
         */
        private Integer topK = 50;

        /**
         * 内部命中结果大小
         */
        private Integer innerHitSize = 5;

        /**
         * 相似度分数阈值
         */
        private Double scoreLimit = 0.8D;

        /**
         * 向量搜索候选数量
         */
        private Integer numCandidates = 256;

        private Boolean logTrace = false;
    }
}