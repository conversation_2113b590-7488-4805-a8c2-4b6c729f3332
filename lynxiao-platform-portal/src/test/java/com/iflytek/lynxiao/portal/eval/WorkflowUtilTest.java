package com.iflytek.lynxiao.portal.eval;


import com.iflytek.lynxiao.portal.PortalTestBoot;
import com.iflytek.lynxiao.portal.eval.domain.WorkflowNode;
import com.iflytek.lynxiao.portal.eval.utils.EvalUtils;
import com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Set;

@Slf4j
@ExtendWith(SpringExtension.class)
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = PortalTestBoot.class)
@ActiveProfiles("dev")
public class WorkflowUtilTest {

    @Autowired
    private WorkflowProcessService workflowProcessService;

    @Test
    public void buildCodeNodeIdMap() {
        String processId = "67f3932e6f8ea1acaa2a0b9d";

        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(processId);

        List<WorkflowNode> workflowNodes = EvalUtils.buildNeedAnalysisNodes(workflowProcess);

        log.info("result :{}", workflowNodes);
    }

    @Test
    public void testOpenRankNodeFullDocs(){
        String processId = "68b0fc7bcd0c8217ec5c52b8";

        WorkflowProcess workflowProcess = WorkflowUtil.openRankNodeFullDocs( workflowProcessService.getWorkflowProcess(processId));

        log.info("workflowProcess :{}", workflowProcess);

    }

    @Test
    public void testFindIdxCodeFromWorkflowProcess(){
        String processId = "68c0d5a5e84e1fe0bc0c1fe8";
        Set<String> indexCodes = WorkflowUtil.findIndexCodes(workflowProcessService.getWorkflowProcess(processId, false));
        System.out.println(indexCodes);
    }
}
