package com.iflytek.lynxiao.portal.eval.component.core.impl;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.common.feign.region.WorkflowTraceFeign;
import com.iflytek.lynxiao.data.dto.MetaLabelDTO;
import com.iflytek.lynxiao.portal.document.service.meta.MetaLabelService;
import com.iflytek.lynxiao.portal.eval.component.core.ProcTraceFetcher;
import com.iflytek.lynxiao.portal.eval.component.domain.ProcTraceFetcherParam;
import com.iflytek.lynxiao.portal.eval.component.domain.ProcTraceFetcherResult;
import com.iflytek.lynxiao.portal.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.portal.eval.component.domain.WorkflowInvokeParseResult;
import com.iflytek.lynxiao.portal.eval.config.EvalProperties;
import com.iflytek.lynxiao.portal.eval.domain.WorkflowNode;
import com.iflytek.lynxiao.portal.eval.utils.EvalUtils;
import com.iflytek.lynxiao.portal.eval.utils.TraceLogUtils;
import com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil;
import com.iflytek.lynxiao.portal.flow.service.core.api.RegionWorkflowApi;
import com.iflytek.turing.astrolink.domain.WorkflowTrace;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessCallDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程id 全链路获取器
 * 通过执行流程, 获取星链session记录中组件的执行结果，不通过elk去取轨迹是因为elk数据保存是异步的，不能及时拿到结果
 */
@Slf4j
@Component
public class ProcTraceFetcher4ProcessId implements ProcTraceFetcher {
    private final EvalProperties evalProperties;
    private final MetaLabelService metaLabelService;
    private final RegionWorkflowApi regionWorkflowApi;
    private final WorkflowProcessService workflowProcessService;
    private final LynxiaoFeignClientManager lynxiaoFeignClientManager;

    public ProcTraceFetcher4ProcessId(EvalProperties evalProperties, MetaLabelService metaLabelService,
                                      RegionWorkflowApi regionWorkflowApi, WorkflowProcessService workflowProcessService,
                                      LynxiaoFeignClientManager lynxiaoFeignClientManager) {
        this.evalProperties = evalProperties;
        this.metaLabelService = metaLabelService;
        this.regionWorkflowApi = regionWorkflowApi;
        this.workflowProcessService = workflowProcessService;
        this.lynxiaoFeignClientManager = lynxiaoFeignClientManager;
    }

    @Override
    public ProcTraceFetcherResult process(ProcTraceFetcherParam fetcherParam) {
        log.debug("trace fetch for processId start, processId:{}, region:{}", fetcherParam.getProcessId(), fetcherParam.getRegionCode());
        long cost = System.currentTimeMillis();

        //1. 获取流程信息
        WorkflowProcess workflowProcess = fetcherParam.getWorkflowProcess() == null ?
                this.workflowProcessService.getWorkflowProcess(fetcherParam.getProcessId(), false) :
                fetcherParam.getWorkflowProcess();
        if (workflowProcess == null) {
            log.error("workflowProcess is null. param:{}", fetcherParam);
            throw new LynxiaoException("流程不存在");
        }
        // 1.1 开启排序组件debug模式
        workflowProcess = WorkflowUtil.openRankNodeFullDocs(workflowProcess);

        //2.执行流程
        List<JSONObject> docList = executeWorkflow(fetcherParam, workflowProcess);

        // 处理q_level和q_score
        Map<String, MetaLabelDTO> metaLabelDTOMap = metaLabelService.findAll(Sort.unsorted()).stream()
                .collect(Collectors.toMap(MetaLabelDTO::getCode, metaLabelDTO -> metaLabelDTO));
        TraceLogUtils.buildEvalReRankProps(docList, metaLabelDTOMap);

        //3. 获取关键流程节点执行结果
        WorkflowTraceFeign workflowTraceFeign = this.lynxiaoFeignClientManager.buildWithRegion(WorkflowTraceFeign.class, fetcherParam.getRegionCode());
        try {
            //获取需要分析的关键组件执行结果
            List<TraceLogItem> traces = fetchTraceLogItems(fetcherParam.getSessionId(), workflowTraceFeign, workflowProcess);
            TraceLogUtils.traceLogsProcess(traces, metaLabelDTOMap);
            log.info("trace fetch for processId end, processId:{}, region:{} cost:{}", fetcherParam.getProcessId(), fetcherParam.getRegionCode(),
                    System.currentTimeMillis() - cost);

            return new ProcTraceFetcherResult(traces, docList, workflowProcess);

        } finally {
            workflowTraceFeign.sessionDelete(fetcherParam.getHistoryTraceId());
        }
    }


    private List<JSONObject> executeWorkflow(ProcTraceFetcherParam fetcherParam, WorkflowProcess workflowProcess) {
        // 执行流程，获取召回文档列表

        WorkflowProcessCallDTO workflowProcessCallDTO = new WorkflowProcessCallDTO();
        workflowProcessCallDTO.setDebug(true);
        workflowProcessCallDTO.setId(fetcherParam.getProcessId());
        workflowProcessCallDTO.setWorkflowProcess(workflowProcess);
        workflowProcessCallDTO.setPayload(new JSONObject().fluentPut(WorkflowUtil.findStartNodeId(workflowProcess), fetcherParam.getPayload()));
        workflowProcessCallDTO.setTraceId(fetcherParam.getTraceId());
        workflowProcessCallDTO.setSessionId(fetcherParam.getSessionId());
        try {
            List<JSONObject> docs = this.regionWorkflowApi.call(fetcherParam.getRegionCode(), workflowProcessCallDTO, workflowProcess);
            normalizeDocFields(docs);
            return docs;
        } catch (Exception e) {
            log.error("执行流程失败,msg:{}", e.getMessage(), e);
            throw new LynxiaoException("执行流程失败, msg:%s".formatted(e.getMessage()));
        }
    }

    private void normalizeDocFields(List<JSONObject> docList) {
        // 统一处理doc字段，医疗产品方案中召回doc中的indexCode转为_indexCode, indexName转为_indexName
        EvalProperties.EvalNormalDocSetting normalDocSetting = this.evalProperties.getNormalDocSetting();
        Map<String, String> fieldMappings = normalDocSetting.getDocFieldMappings();
        if (fieldMappings == null || fieldMappings.isEmpty()) {
            return;
        }

        // 使用配置的字段映射进行转换
        docList.forEach(doc -> {
            fieldMappings.forEach((sourceField, targetField) -> {
                if (doc.containsKey(sourceField)) {
                    doc.put(targetField, doc.get(sourceField));
                }
            });
        });
    }


    private List<TraceLogItem> fetchTraceLogItems(String sessionId, WorkflowTraceFeign workflowTraceFeign, WorkflowProcess workflowProcess) {
        log.info("fetch trace logs start.");
        List<WorkflowNode> workflowNodes = EvalUtils.buildNeedAnalysisNodes(workflowProcess);
        log.debug("build need analysis workflowNodes :{}", workflowNodes);
        List<TraceLogItem> traces = new ArrayList<>();
        // todo 调成成并行，提高效率
        for (WorkflowNode workflowNode : workflowNodes) {
            Optional<TraceLogItem> traceLogItem = fetchNodeOutput(sessionId, workflowTraceFeign, workflowNode);
            traceLogItem.ifPresent(traces::add);
        }
        log.info("fetch trace logs end.");
        return traces;
    }

    private Optional<TraceLogItem> fetchNodeOutput(String sessionId, WorkflowTraceFeign workflowTraceFeign, WorkflowNode workflowNode) {
        // 从星链session记录中，获取指定组件执行的结果
        List<String> nodeIds = workflowNode.getIds();

        TraceLogItem traceLogItem = new TraceLogItem();
        traceLogItem.setCode(workflowNode.getCode());
        traceLogItem.setName(workflowNode.getName());

        WorkflowTrace workflowTrace = null;
        for (String nodeId : nodeIds) {
            traceLogItem.setNodeId(nodeId);
            ApiResponse workflowLogResponse = workflowTraceFeign.detail(sessionId, workflowNode.getParentNodeId(), nodeId);
            if (workflowLogResponse == null || workflowLogResponse.getPayload() == null ||
                    workflowLogResponse.getPayload().getJSONObject("data") == null
                    || workflowLogResponse.getPayload().getJSONObject("data").getJSONArray("output") == null) {
                continue;
            }

            if (workflowLogResponse.getHeader() == null || workflowLogResponse.getHeader().getInteger("code") != 0) {
                log.warn("sessionId:{} nodeId:{} component exec error. response:{} ", sessionId, nodeId, workflowLogResponse);
            } else {
                workflowTrace = workflowLogResponse.getPayload().getJSONObject("data").to(WorkflowTrace.class);
            }

            //找到第一个不为null的WorkflowTrace，当前认为产品方案中相同的组件只会被执行一次！
            break;
        }

        if (workflowTrace == null) {
            log.warn("workflowTrace is null. node:{}", workflowNode);
            return Optional.empty();
        }

        if (!CollectionUtils.isEmpty(workflowTrace.getOutput())) {
            ApiResponse apiResponse = workflowTrace.getOutput().getJSONObject(0).to(ApiResponse.class);
            WorkflowInvokeParseResult workflowInvokeParseResult = WorkflowUtil.parseDocs(workflowNode.getCode(), apiResponse.getPayload(), WorkflowUtil.RESPONSE);
            traceLogItem.setDocs(workflowInvokeParseResult.getDocs());
            traceLogItem.setDocSize(workflowInvokeParseResult.getDocSize());
        }

        if (!CollectionUtils.isEmpty(workflowTrace.getInput())) {
            ApiRequest apiRequest = workflowTrace.getInput().getJSONObject(0).to(ApiRequest.class);
            WorkflowInvokeParseResult workflowInvokeParseResult = WorkflowUtil.parseDocs(workflowNode.getCode(), apiRequest.getPayload(), WorkflowUtil.REQUEST);
            traceLogItem.setReqData(workflowInvokeParseResult);
        }

        return Optional.of(traceLogItem);
    }

}
