package com.iflytek.lynxiao.portal.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.portal.eval.component.domain.*;
import com.iflytek.lynxiao.portal.eval.utils.EvalUtils;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.exception.PandoraException;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.iflytek.lynxiao.portal.eval.utils.TraceLogUtils.castLong2String;

/**
 * 竞品分析结果上下文：贯穿整个分析过程
 *
 * <AUTHOR>  2025/3/6 10:28
 */
public class GoodCaseContext {

    /**
     * goodCase 入参
     */
    @Getter
    private final AscribeInputGoodCase input;

    /**
     * goodId 或者goodUrl 对应的文档, 在mock环节， 如果doc为空，不可进行节点mock
     */
    @Getter
    @Setter
    private JSONObject doc = null;

    /**
     * 全链路列表
     */
    @Getter
    @Setter
    private List<TraceLogItem> traceLogs = new ArrayList<>();

    /**
     * url存在性分析结果
     */
    @Getter
    @Setter
    private ExistCheckResult existCheckResult;

    public GoodCaseContext(AscribeInputGoodCase ascribeInputGoodCase) {
        this.input = ascribeInputGoodCase;
    }


    /**
     * 获取doc id , 在doc非空判断后执行
     *
     * @return
     */
    public String getDocId() {
        return this.getDoc().getString("_id");
    }


    /**
     * 添加预评估组件（mock）执行结果
     *
     * @param nodeId 预评估组件Id
     * @param docs   执行结果
     */
    public void addCompResult(String nodeId, List<JSONObject> docs) {
        if (traceLogs == null) {
            throw new PandoraException("good case traceLogs is null.");
        }

        for (TraceLogItem traceLog : traceLogs) {
            if (traceLog.getNodeId().equals(nodeId)) {
                traceLog.setMockDocs(docs);
            }
        }
    }

    /**
     * 获取指定组件的执行结果
     *
     * @param nodeId 评估组件节点id
     */
    public TraceLogItem getTraceItemByNodeId(String nodeId) {
        if (traceLogs == null) {
            throw new PandoraException("good case traceLogs is null.");
        }
        return traceLogs.stream().filter(item -> item.getNodeId().equals(nodeId)).toList().getFirst();
    }

    /**
     * 分析组件执行轨迹，返回分析结果
     */
    public AscribeOutputGoodCase computeOutput() {
        if (existCheckResult == null) {
            throw new PandoraException("url exist result is null.");
        }

        boolean isCrawledFailed = false;
        Optional<EvalResultItem> isCrawledFailedOp = existCheckResult.getResult().stream().filter(item -> item.getName().equals("爬取失败")).findFirst();
        if (isCrawledFailedOp.isPresent()) {
            isCrawledFailed = isCrawledFailedOp.get().getValue() == 1;
        }


        List<EvalResultItem> evalResult = new ArrayList<>(existCheckResult.getResult());
        for (TraceLogItem traceLog : traceLogs) {
            boolean exist;
            if (traceLog.getMockDocs() != null) {
                exist = EvalUtils.isExist(traceLog.getMockDocs(), input.getGoodId(), input.getGoodUrl());
            } else {
                exist = EvalUtils.isExist(traceLog.getDocs(), input.getGoodId(), input.getGoodUrl());
            }

            //爬取失败后无需再归因后续节点  全部默认无问题
            if (isCrawledFailed){
                exist = true;
            }

            evalResult.add(new EvalResultItem(traceLog.getName(), exist ? 0 : 1));
            castLong2String(traceLog.getMockDocs());
        }

        AscribeOutputGoodCase ascribeOutputGoodCase = new AscribeOutputGoodCase();
        ascribeOutputGoodCase.setTraceResult(traceLogs);
        ascribeOutputGoodCase.setEvalResult(evalResult);

        return ascribeOutputGoodCase;
    }
}
