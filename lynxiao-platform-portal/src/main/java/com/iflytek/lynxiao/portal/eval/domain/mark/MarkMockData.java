package com.iflytek.lynxiao.portal.eval.domain.mark;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;


/**
 * 召回文档 - mock数据
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MarkMockData extends Jsonable {

    /**
     * 召回文档列表
     */
    private List<JSONObject> docs = new ArrayList<>();

    public static MarkMockData of(List<JSONObject> docs) {
        MarkMockData data = new MarkMockData();
        data.setDocs(docs);
        return data;
    }
}