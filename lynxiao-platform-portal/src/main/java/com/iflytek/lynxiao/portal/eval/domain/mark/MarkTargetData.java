package com.iflytek.lynxiao.portal.eval.domain.mark;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.portal.eval.component.domain.TraceLogItem;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;


/**
 * 测评对象数据, 搜索问题返回的需要标注的数据包括：最终召回结果、全链路结果、大模型推理结果
 *
 * <AUTHOR>  2025/5/19 10:43
 */
@Getter
@Setter
public class MarkTargetData extends Jsonable {

    /**
     * 召回docs
     */
    private List<MarkTargetRecall> recallList = new ArrayList<>();

    /**
     * 结果召回全链路集合
     */
    private List<MarkTargetTrace> traceList = new ArrayList<>();

    /**
     * 归因分析全链路集合
     */
    private List<MarkTargetTrace> ascribeTraceList = new ArrayList<>();


    /**
     * 大模型验证结果
     */
    private MarkTargetChat chat;


    public static MarkTargetData of(List<JSONObject> recallDocs) {

        if (CollectionUtils.isEmpty(recallDocs)) {
            return new MarkTargetData();
        }

        // 只包含最终召回文档
        MarkTargetData markTargetData = new MarkTargetData();
        for (JSONObject recallDoc : recallDocs) {
            MarkTargetRecall markTargetRecall = new MarkTargetRecall();
            markTargetRecall.setDoc(recallDoc);
            markTargetData.getRecallList().add(markTargetRecall);
        }
        return markTargetData;
    }

    /**
     * 根据召回文档和全链路调用轨迹结果，构建MarkTargetData
     *
     * @param recallDocs    召回文档
     * @param traceLogItems 全链路调用轨迹
     * @return 标注对象
     */
    public static MarkTargetData of(List<JSONObject> recallDocs, List<TraceLogItem> traceLogItems) {

        if (CollectionUtils.isEmpty(recallDocs) && CollectionUtils.isEmpty(traceLogItems)) {
            return new MarkTargetData();
        }

        MarkTargetData markTargetData = new MarkTargetData();
        markTargetData.setTraceList(MarkTargetTrace.of(traceLogItems));
        markTargetData.setRecallList(MarkTargetRecall.of(recallDocs, traceLogItems));
        return markTargetData;
    }


}