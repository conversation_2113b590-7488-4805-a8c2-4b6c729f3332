package com.iflytek.lynxiao.portal.eval.dto.mission;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 测评任务编辑
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class EvalMissionUpdateDTO extends Jsonable {

    @NotBlank(message = "名称不能为空")
    @Schema(title = "名称")
    private String name;

    @Schema(title = "描述")
    private String description;
}
