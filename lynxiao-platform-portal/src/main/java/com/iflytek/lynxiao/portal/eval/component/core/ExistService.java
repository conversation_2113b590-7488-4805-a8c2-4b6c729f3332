package com.iflytek.lynxiao.portal.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.datashard.DatashardDecoder;
import com.iflytek.lynxiao.portal.document.dto.site.SiteVersionGroupDTO;
import com.iflytek.lynxiao.portal.document.service.intervene.DomainBlackService;
import com.iflytek.lynxiao.portal.document.service.site.SiteVersionService;
import com.iflytek.lynxiao.portal.eval.component.checker.ExistChecker;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Dataset;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Idx;
import com.iflytek.lynxiao.portal.eval.component.checker.impl.ExistChecker4Site;
import com.iflytek.lynxiao.portal.eval.component.domain.ExistCheckResult;
import com.iflytek.lynxiao.portal.eval.component.domain.IdxWithSiteDTO;
import com.iflytek.lynxiao.portal.eval.component.domain.PreEvalDoc;
import com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedIdxDbInst;
import com.iflytek.lynxiao.resource.repository.DatasetVersionSiteVersionRepository;
import com.iflytek.lynxiao.resource.repository.IdxDbInstRepository;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 竞品分析Url是否存在特征库中
 *
 * <AUTHOR>  2025/3/6 09:12
 */

@Slf4j
@Service
public class ExistService {

    private final DatasetVersionSiteVersionRepository datasetVersionSiteVersionRepository;
    private final SiteVersionService siteVersionService;
    private final IdxDbInstRepository idxDbInstRepository;
    private final UrlDocConverter urlDocConverter;
    private final List<ExistChecker> checkers = new ArrayList<>();

    public ExistService(DatasetVersionSiteVersionRepository datasetVersionSiteVersionRepository,
                        SiteVersionService siteVersionService, DocumentFetchService documentFetchService,
                        DomainBlackService domainBlackService, IdxDbInstRepository idxDbInstRepository,
                        UrlDocConverter urlDocConverter, DatashardDecoder datashardDecoder) {
        this.datasetVersionSiteVersionRepository = datasetVersionSiteVersionRepository;
        this.siteVersionService = siteVersionService;
        this.idxDbInstRepository = idxDbInstRepository;
        this.urlDocConverter = urlDocConverter;
        this.checkers.add(new ExistChecker4Idx(domainBlackService, documentFetchService));
        this.checkers.add(new ExistChecker4Dataset(documentFetchService, datashardDecoder));
        this.checkers.add(new ExistChecker4Site(documentFetchService, datashardDecoder));
    }

    public ExistCheckResult process(String id, String url, WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        ExistCheckResult respPayload = new ExistCheckResult();

        // 获取索引库
        Set<String> indexCodes = WorkflowUtil.findIndexCodes(workflowProcess);
        log.debug("indexCodes:{}", indexCodes);

        // 获取关联的数据集、站点信息
        List<IdxWithSiteDTO> idxWithSiteDTOList = parseSiteList(indexCodes);
        if (CollectionUtils.isEmpty(idxWithSiteDTOList)) {
            return respPayload;
        }
        respPayload.setIdxWithSiteDTOList(idxWithSiteDTOList);

        for (ExistChecker checker : checkers) {
            long startCost = System.currentTimeMillis();
            boolean isStop = checker.check(respPayload, id, url, idxWithSiteDTOList, goodCaseContext);
            log.info("checker:{} check id:{} or url:{} cost:{}ms isStop:{}", checker.getClass().getSimpleName(), id, url, System.currentTimeMillis() - startCost, isStop);
            if (isStop) {
                break;
            }
        }

        if (goodCaseContext.getDoc() == null && StringUtils.isNotBlank(url)) {
            Optional<PreEvalDoc> crawledDoc = urlDocConverter.fetchDoc(url, false, null);
            goodCaseContext.setDoc(JSONObject.from(crawledDoc.orElse(null)));
        }

        log.debug("id:{}, url:{} url exist check result:{}", id, url, respPayload);
        return respPayload;
    }

    /**
     * 根据索引库名 获取关联的数据集、站点信息
     */
    public List<IdxWithSiteDTO> parseSiteList(Set<String> indexCodes) {

        List<IdxWithSiteDTO> idxWithSiteDTOList = new ArrayList<>();
        for (String idxCode : indexCodes) {
            Optional<GeneratedIdxDbInst> idxDbInst = this.idxDbInstRepository.findByCode(idxCode);
            if (idxDbInst.isEmpty()) {
                log.debug("can not found idxCode:{} in idxDbInstRepository", idxCode);
                continue;
            }
            GeneratedIdxDbInst idxDbInstDTO = idxDbInst.get();
            IdxWithSiteDTO idxDbContext = new IdxWithSiteDTO().setId(String.valueOf(idxDbInstDTO.getId())).setCode(idxCode)
                    .setType(idxDbInstDTO.getType())
                    .setIdRegion(idxDbInstDTO.getIdInRegion())
                    .setDatasetId(String.valueOf(idxDbInstDTO.getDatasetId()))
                    .setRegion(idxDbInstDTO.getRegion())
                    .setDatasetVersionId(String.valueOf(idxDbInstDTO.getDatasetVersionId()));
            idxWithSiteDTOList.add(idxDbContext);
        }
        log.debug("idxDbContextList:{}", idxWithSiteDTOList);

        //完善站点信息
        for (IdxWithSiteDTO checkContext : idxWithSiteDTOList) {
            List<SiteVersionGroupDTO> siteList = siteVersionService.findByDatasetVersionId(checkContext.getDatasetVersionId());
            checkContext.setSiteIds(siteList.stream().map(SiteVersionGroupDTO::getSiteDTO).collect(Collectors.toList()));

            List<Long> siteVersionIds = datasetVersionSiteVersionRepository.findSiteVersionIds(Long.parseLong(checkContext.getDatasetVersionId()));
            checkContext.setSiteVersionIds(siteVersionIds);
        }

        return idxWithSiteDTOList;
    }
}
