package com.iflytek.lynxiao.portal.eval.entity;


import com.iflytek.lynxiao.portal.eval.domain.mark.MarkMockData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import skynet.boot.mongo.domain.AbstractAuditingEntity;

@Getter
@Setter
@Document("eval_mark_mock")
public class MarkMockEntity extends AbstractAuditingEntity<String> {

    @Field("trace_id")
    private String traceId;

    /**
     * 测评记录表id
     */
    @Field("mark_record_id")
    private String markRecordId;

    /**
     * 标注目标id
     */
    @Field("target_id")
    private String targetId;


    /**
     * 策略配置id
     */
    @Field("strategy_id")
    private String strategyId;

    /**
     * 参与mock的doc url
     */
    @Field("url")
    private String url;

    /**
     * 参与mock的节点id
     */
    @Field("node_id")
    private String nodeId;

    /**
     * 参与mock的组件code
     */
    @Field("code")
    private String code;

    /**
     * 标注目标
     */
    @Field("data")
    private MarkMockData data;

}
