package com.iflytek.lynxiao.portal.eval.repository;


import com.iflytek.lynxiao.portal.eval.entity.MarkMockEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class)
public class MarkMockRepository {

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;

    public void save(MarkMockEntity entity) {
        platformMongoTemplate.save(entity);
    }

    public void saveAll(List<MarkMockEntity> entities) {
        platformMongoTemplate.insertAll(entities);
    }

    /**
     * 使用targetId nodeId 或者 goodId goodUrl 查找mock数据
     */
    public MarkMockEntity findMockEntity(String targetId, String nodeId, String goodId, String goodUrl) {
        Query query = Query.query(Criteria.where("target_id").is(targetId).and("node_id").is(nodeId));
        if (goodId != null) {
            query.addCriteria(Criteria.where("id").is(goodId));
        }
        if (goodUrl != null) {
            query.addCriteria(Criteria.where("url").is(goodUrl));
        }
        return platformMongoTemplate.findOne(query, MarkMockEntity.class);
    }
}
