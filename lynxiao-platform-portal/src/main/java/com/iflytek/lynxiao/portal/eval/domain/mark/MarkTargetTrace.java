package com.iflytek.lynxiao.portal.eval.domain.mark;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.portal.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.portal.eval.component.domain.WorkflowInvokeParseResult;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;

/**
 * 标注目标- 全链路中某个组件召回结果
 */
@Getter
@Setter
public class MarkTargetTrace extends Jsonable {

    /**
     * 组件code
     */
    private String code;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 位次字段
     */
    private String indexField;

    /**
     * 得分字段
     */
    private String scoreField;

    /**
     * 请求参数文档列表
     * 初始验证请求完整结构
     */
    private WorkflowInvokeParseResult reqData;

    /**
     * 召回文档列表
     */
    private List<JSONObject> docs = new ArrayList<>();

    /**
     * 组件正常返回的文档数量
     */
    private Integer docSize;


    public static List<MarkTargetTrace> of(List<TraceLogItem> traceLogItems) {
        //全链路对象转换
        if (CollectionUtils.isEmpty(traceLogItems)) {
            return new ArrayList<>(0);
        }
        List<MarkTargetTrace> markTargetTraces = new ArrayList<>();
        for (TraceLogItem traceLogItem : traceLogItems) {
            markTargetTraces.add(MarkTargetTrace.of(traceLogItem));
        }
        return markTargetTraces;
    }

    public static MarkTargetTrace of(TraceLogItem traceLogItem) {
        if (traceLogItem == null) {
            return null;
        }
        return BeanUtil.copyProperties(traceLogItem, MarkTargetTrace.class);
    }
}