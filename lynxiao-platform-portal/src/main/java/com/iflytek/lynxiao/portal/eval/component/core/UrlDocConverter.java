package com.iflytek.lynxiao.portal.eval.component.core;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.content.ContentApi;
import com.iflytek.lynxiao.portal.document.dto.site.SiteDTO;
import com.iflytek.lynxiao.portal.eval.component.domain.IdxWithSiteDTO;
import com.iflytek.lynxiao.portal.eval.component.domain.PreEvalDoc;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据url获取文档内容
 *
 * <AUTHOR>  2025/3/6 09:20
 */
@Slf4j
@Component
public class UrlDocConverter {
    private final DocumentFetchService documentFetchService;
    private final ContentApi contentApi;

    public UrlDocConverter(DocumentFetchService documentFetchService, ContentApi contentApi) {
        this.documentFetchService = documentFetchService;
        this.contentApi = contentApi;
    }

    /**
     * 获取文档内容
     */
    public Optional<PreEvalDoc> fetchDoc(String url, boolean isCrawled, List<IdxWithSiteDTO> idxWithSiteDTOList) {
        long cost = System.currentTimeMillis();
        try {
            if (!isCrawled || CollectionUtils.isEmpty(idxWithSiteDTOList)) {
                // 爬取文档内容
                return crawlDocContent(url);
            } else {
                // 从数据库中获取文档内容
                return fetchDocFromDatabase(url, idxWithSiteDTOList);
            }
        } catch (Exception e) {
            log.error(String.format("爬取文档内容失败. url=%s msg:%s", url, e.getMessage()), e.getMessage());
            return Optional.empty();
        } finally {
            log.info("获取文档内容耗时: {} ms, url: {}", System.currentTimeMillis() - cost, url);
        }
    }

    private Optional<PreEvalDoc> crawlDocContent(String url) {
        log.debug("开始爬取文档内容，url: {}", url);

        Optional<JSONObject> dataOp = this.contentApi.urlDocConvert(url);
        if (dataOp.isEmpty()) {
            log.error("调用内容服务获取文档内容失败，url: {}", url);
            return Optional.empty();
        }
        JSONObject data = dataOp.get();
        JSONArray magazines = data.getJSONArray("magazines");
        if (CollectionUtils.isEmpty(magazines) || magazines.getJSONObject(0) == null) {
            return Optional.empty();
        }
        JSONObject result = magazines.getJSONObject(0);
        Long id = result.getLong("id");
        String title = result.getString("title");
        String content = result.getJSONObject("content").getString("readableText");
        PreEvalDoc preEvalDoc = parseURL(url);
        preEvalDoc.set_id(id).setTitle(title).setContent(content);
        return Optional.of(preEvalDoc);
    }

    private Optional<PreEvalDoc> fetchDocFromDatabase(String url, List<IdxWithSiteDTO> idxWithSiteDTOS) {
        log.info("从数据库中获取文档内容，url: {}", url);
        Set<String> siteIds = new HashSet<>();
        //判断当前url是否存在于站点库中
        for (IdxWithSiteDTO idxDbContext : idxWithSiteDTOS) {
            for (SiteDTO site : idxDbContext.getSiteIds()) {
                if (!siteIds.contains(site.getId())) {
                    List<Document> documents = documentFetchService.fetchDocsFromSite("", url, site.getId());
                    if (CollectionUtils.isEmpty(documents)) {
                        log.debug("site:{} does not contain url:{}", site.getId(), url);
                        siteIds.add(site.getId());
                        continue;
                    }
                    Long id = documents.getFirst().getLong("_id");
                    String title = documents.getFirst().getString("title");
                    String content = documents.getFirst().getString("content");
                    PreEvalDoc preEvalDoc = parseURL(url);
                    preEvalDoc.set_id(id).setTitle(title).setContent(content);
                    return Optional.of(preEvalDoc);
                }
            }
        }
        log.error("url:{} url不存在于站点库中", url);
        return Optional.empty();
    }

    private PreEvalDoc parseURL(String url) {
        String regex = "^(https?)://([^/]+)(/.*)$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        PreEvalDoc preEvalDoc = new PreEvalDoc();
        if (matcher.matches()) {
            // 解析出协议
            preEvalDoc.setProtocol(matcher.group(1));
            preEvalDoc.setDomain(matcher.group(2));
            preEvalDoc.setPath(matcher.group(3));
            return preEvalDoc;
        } else {
            throw new LynxiaoException("url解析失败");
        }
    }
}


