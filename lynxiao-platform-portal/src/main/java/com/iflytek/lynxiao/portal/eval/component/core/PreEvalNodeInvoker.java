package com.iflytek.lynxiao.portal.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.portal.eval.component.domain.Docs;
import com.iflytek.lynxiao.portal.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.portal.eval.component.domain.WorkflowInvokeParseResult;
import com.iflytek.lynxiao.portal.eval.dto.standard.EvalConcernNodeDTO;
import com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.dto.WorkflowComponentParameter;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessNode;
import com.iflytek.turing.astrolink.service.impl.WorkflowFlatInvokerImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 预估节点执行器
 *
 * <AUTHOR>  2025/3/6 11:33
 */

@Slf4j
@Service
public class PreEvalNodeInvoker {

    private final WorkflowFlatInvokerImpl workflowFlatInvokerImpl;

    public PreEvalNodeInvoker(WorkflowFlatInvokerImpl workflowFlatInvokerImpl) {
        this.workflowFlatInvokerImpl = workflowFlatInvokerImpl;
    }


    /**
     * 执行流程中指定某个节点
     *
     * @param workflow        完整流程
     * @param goodCaseContext 分析上下文
     * @param preEvalNode     待mock节点
     * @return 执行结果
     */
    public List<JSONObject> process(WorkflowProcess workflow, GoodCaseContext goodCaseContext, PreEvalNode preEvalNode) {
        // 拷贝对象
        WorkflowProcess workflowProcess = JSONObject.parseObject(JSONObject.toJSONString(workflow), WorkflowProcess.class);

        // 1. 根据nodeId 获取目标节点 todo 不能兼容产品方案
        List<WorkflowProcessNode> filteredNodeList = workflowProcess.getNodeList().stream().filter(item -> item.getId().equals(preEvalNode.getNodeId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredNodeList)) {
            throw new PandoraException(String.format("can not find node. nodeId:%s", preEvalNode.getNodeId()));
        }
        WorkflowProcessNode targetNode = filteredNodeList.getFirst();

        // 2. 处理入参：只覆盖文档数据参数，其他参数采用流程中嵌入的参数
        EvalConcernNodeDTO concernNodeDTO = WorkflowUtil.findByCode(preEvalNode.getCode()).orElseThrow(() -> new PandoraException(String.format("找不到关注节点. code:%s", preEvalNode.getCode())));

        //将inputArgs 和 outputArgs中的参数show改为true
        List<WorkflowComponentParameter> inputArgs = targetNode.getComponent().getInputArgs();
        for (WorkflowComponentParameter inputArg : inputArgs) {
            if (inputArg.getKey().equals(concernNodeDTO.getInputFiled())) {
                inputArg.setShow(true);
                break;
            }
        }
        List<WorkflowComponentParameter> outputArgs = targetNode.getComponent().getOutputArgs();
        for (WorkflowComponentParameter outputArg : outputArgs) {
            if (outputArg.getKey().equals(concernNodeDTO.getOutputField())) {
                outputArg.setShow(true);
                break;
            }
        }

        // 将目标节点构造成一个新的流程
        workflowProcess.setNodeList(filteredNodeList);
        workflowProcess.setLinkList(new ArrayList<>());

        // 使用当前节点的原始入参添加需要mock的doc组成即将mock的入参
        TraceLogItem traceLog = goodCaseContext.getTraceItemByNodeId(preEvalNode.getNodeId());

        WorkflowInvokeParseResult reqData = traceLog.getReqData();
        if (reqData == null) {
            log.warn("traceLog reqData is null, nodeId:{} mock can not exec.", preEvalNode.getNodeId());
            return new ArrayList<>();
        }

        for (Docs dataItem : reqData.getData()) {
            if (dataItem.getDocs() == null) {
                dataItem.setDocs(new ArrayList<>());
            }
            dataItem.getDocs().add(goodCaseContext.getDoc());
        }
        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setPayload(new JSONObject().fluentPut(concernNodeDTO.getInputFiled(), reqData.getData()));
        if (log.isTraceEnabled()) {
            log.trace("preEval invoke apiRequest:{}", apiRequest);
        }

        ApiResponse invokeResp = workflowFlatInvokerImpl.invoke(workflowProcess, apiRequest);
        if (log.isTraceEnabled()) {
            log.trace("preEval invoke response:{}", invokeResp);
        }

        JSONObject responseOutput = invokeResp.getPayload().getJSONObject("output");
        WorkflowInvokeParseResult parseResult = WorkflowUtil.parseDocs(targetNode.getComponent().getCode(), responseOutput, WorkflowUtil.RESPONSE);
        return parseResult.getDocs();
    }
}
