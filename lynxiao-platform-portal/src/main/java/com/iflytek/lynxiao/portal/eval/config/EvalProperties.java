package com.iflytek.lynxiao.portal.eval.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.convert.DurationUnit;
import skynet.boot.common.domain.Jsonable;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class EvalProperties extends Jsonable {

    /**
     * 分析计划配置
     */
    private EvalPlanSetting planSetting = new EvalPlanSetting();

    /**
     * 组件配置
     */
    private EvalComponentSetting componentSetting = new EvalComponentSetting();


    private EvalMarkSetting markSetting = new EvalMarkSetting();

    private EvalEsSetting es = new EvalEsSetting();

    private EvalChatSetting chatSetting = new EvalChatSetting();

    private EvalAscribeSetting ascribeSetting = new EvalAscribeSetting();

    private EvalNormalDocSetting normalDocSetting = new EvalNormalDocSetting();

    private UapSetting uapSetting = new UapSetting();


    @Setter
    @Getter
    public static class UapSetting {

        /**
         * 测评任务标注者角色code
         */
        private String markRoleCode = "lynxiao_eval_mark";
    }

    @Setter
    @Getter
    public static class EvalPlanSetting {
        /**
         * 分析计划执行的切分组数
         */
        private int partition = 4;

        /**
         * 分析计划执行的等待数量限制
         */
        private int limitCount = 10;
    }


    @Setter
    @Getter
    public static class EvalComponentSetting {
        /**
         * 流程执行超时时间 默认 60s
         */
        @DurationUnit(ChronoUnit.SECONDS)
        private Duration timeout = Duration.ofSeconds(60L);

        /**
         * 验证流程关心的回调状态列表  默认 RESPONSE
         */
        private List<String> stateTypes = Arrays.asList("REQUEST", "RESPONSE");
    }

    @Setter
    @Getter
    public static class EvalMarkSetting {
        @DurationUnit(ChronoUnit.SECONDS)
        private Duration chatTimeout = Duration.ofSeconds(60L);

        private String sparkChatUrl = "https://spark-api-open.xf-yun.com/v1/chat/completions";

        /**
         * 验证流程关心的回调状态列表  默认 RESPONSE
         */
        private String domain = "4.0Ultra";

        private String apiPwd;

        private int chatLimitCount = 8;
    }

    @Setter
    @Getter
    public static class EvalEsSetting {

        private Map<String, EsResource> endpoints = new HashMap<>();
    }

    @Setter
    @Getter
    public static class EsResource extends Jsonable {
        private String host;
        private int port;
        private String userName;
        private String password;
    }

    @Setter
    @Getter
    public static class EvalChatSetting {
        //默认大模型domain
        private String defaultDomain = "4.0Ultra";
    }

    @Setter
    @Getter
    public static class EvalAscribeSetting {
        /**
         * 自动归因并发数  默认4；
         */
        private int concurrency = 4;

        /**
         * 测评任务自动归因并发数
         */
        private int missionAscribeLimit = 4;
    }

    @Setter
    @Getter
    public static class EvalNormalDocSetting {
        private Map<String, String> docFieldMappings = new HashMap<>();

        // 初始化默认映射
        {
            docFieldMappings.put("indexCode", "_indexCode");
            docFieldMappings.put("indexName", "_indexName");
        }
    }
}
