package com.iflytek.lynxiao.portal.eval.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;

/**
 * <AUTHOR>
 */
@Configuration
public class EvalConfig {

    @Bean(name = "platformGridFsTemplate")
    public GridFsTemplate platformGridFsTemplate(@Qualifier("platformMongoTemplate") MongoTemplate platformMongoTemplate,
                                                 MongoConverter mongoConverter) {
        MongoDatabaseFactory factory = platformMongoTemplate.getMongoDatabaseFactory();
        return new GridFsTemplate(factory, mongoConverter);
    }

}
