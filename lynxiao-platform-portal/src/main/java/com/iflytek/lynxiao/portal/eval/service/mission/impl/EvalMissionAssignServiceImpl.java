package com.iflytek.lynxiao.portal.eval.service.mission.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.portal.eval.config.EvalProperties;
import com.iflytek.lynxiao.portal.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.portal.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.portal.eval.dto.mission.EvalMissionQuerySwitchDTO;
import com.iflytek.lynxiao.portal.eval.dto.mission.EvalUserMissionDTO;
import com.iflytek.lynxiao.portal.eval.dto.mission.StandardConfig;
import com.iflytek.lynxiao.portal.eval.feign.skybox.BoxUserApi;
import com.iflytek.lynxiao.portal.eval.service.mission.EvalMissionAssignService;
import com.iflytek.lynxiao.portal.eval.utils.EvalUtils;
import com.iflytek.lynxiao.portal.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.portal.utils.CurrentUserUtils;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedEvalMarkRecord;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedEvalMission;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedEvalMissionAssign;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedEvalQueryGroupDetail;
import com.iflytek.lynxiao.resource.repository.EvalMarkRecordRepository;
import com.iflytek.lynxiao.resource.repository.EvalMissionAssignRepository;
import com.iflytek.lynxiao.resource.repository.EvalMissionRepository;
import com.iflytek.lynxiao.resource.repository.EvalQueryGroupDetailRepository;
import com.iflytek.skybox.contract.domain.BoxUserBase;
import com.iflytek.skybox.contract.domain.PageInfo;
import com.iflytek.skybox.contract.request.RoleUserRequest;
import com.iflytek.skybox.contract.service.BoxUserContextHolder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


@Log4j2
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalMissionAssignServiceImpl implements EvalMissionAssignService {

    private final EvalProperties evalProperties;
    private final EvalMissionRepository missionRepository;
    private final EvalQueryGroupDetailRepository queryGroupDetailRepository;
    private final EvalMarkRecordRepository markRecordRepository;
    private final EvalMissionAssignRepository missionAssignRepository;
    private final LynxiaoFeignClientManager lynxiaoFeignClientManager;

    public EvalMissionAssignServiceImpl(EvalProperties evalProperties, EvalMissionRepository evalMissionRepository,
                                        EvalQueryGroupDetailRepository evalQueryGroupDetailRepository,
                                        EvalMarkRecordRepository markRecordRepository, EvalMissionAssignRepository missionAssignRepository, LynxiaoFeignClientManager lynxiaoFeignClientManager) {
        this.evalProperties = evalProperties;
        this.missionRepository = evalMissionRepository;
        this.queryGroupDetailRepository = evalQueryGroupDetailRepository;
        this.markRecordRepository = markRecordRepository;
        this.missionAssignRepository = missionAssignRepository;
        this.lynxiaoFeignClientManager = lynxiaoFeignClientManager;
    }


    @Override
    public List<BoxUserBase> queryMarkUser() {
        EvalProperties.UapSetting uapSetting = evalProperties.getUapSetting();
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();

        currentUser = new BoxUserBase();
        currentUser.setTenantId("08f03b25-447f-4f55-a958-510d1d4ddbd4");


        log.debug("queryMarkUser, roleCode: {}, tenantId: {}", uapSetting.getMarkRoleCode(), currentUser.getTenantId());

        RoleUserRequest roleUserRequest = new RoleUserRequest();
        roleUserRequest.setRoleCode(uapSetting.getMarkRoleCode());
        roleUserRequest.setTenantId(currentUser.getTenantId());

        BoxUserApi boxUserApi = this.lynxiaoFeignClientManager.buildWithTlbServiceName(BoxUserApi.class, "skybox-gateway");
        PageInfo<BoxUserBase> usersByRole = boxUserApi.getUsersByRole(roleUserRequest);
        if (usersByRole == null || usersByRole.getData() == null) {
            log.error("queryLabelerUser, usersByRole is null");
            throw new LynxiaoException("获取标注人员失败");
        }
        //过滤掉停用的用户 status {0停用 1启用}
        usersByRole.getData().removeIf(user -> user.getStatus() == 0);

        return usersByRole.getData();
    }

    @Override
    public String getMarkUserGroup(String missionId, String account) {
        if (StringUtils.isEmpty(missionId)) {
            return null;
        }
        GeneratedEvalMissionAssign userGroup = missionAssignRepository.findUserGroup(account, Long.valueOf(missionId));
        return userGroup.getUserGroup();
    }


    @Override
    public List<EvalMissionDTO> myList() {
        String account = CurrentUserUtils.getCurrentAccount();
        List<GeneratedEvalMissionAssign> missionAssignList = this.missionAssignRepository.myList(account);
        if (CollectionUtil.isEmpty(missionAssignList)) {
            return new ArrayList<>();
        }

        List<Long> missionIds = missionAssignList.stream().map(GeneratedEvalMissionAssign::getMissionId).distinct().toList();
        List<GeneratedEvalMission> generatedEvalMissions = this.missionRepository.findAllByIdInAndActiveAndWithinTimeRange(missionIds, Instant.now());
        return generatedEvalMissions.stream().map(mission -> {
            EvalMissionDTO dto = BeanUtil.copyProperties(mission, EvalMissionDTO.class, "standardId", "extendFields", "strategyConfig", "standardConfig");
            dto.setId(String.valueOf(mission.getId()));
            dto.setStandardId(String.valueOf(mission.getStandardId()));
            dto.setQueryGroupId(String.valueOf(mission.getQueryGroupId()));
            dto.setStrategyConfig(JSONArray.parseArray(mission.getStrategyConfig(), StrategyConfig.class));
            dto.setExtendFields(Arrays.stream(mission.getExtendFields().split(","))
                    .filter(item-> !item.isBlank())
                    .collect(Collectors.toList()));
            dto.setStandardConfig(JSONObject.parseObject(mission.getStandardConfig(), StandardConfig.class));
            GeneratedEvalMissionAssign missionAssign = getMissionAssign(account, mission.getId(), missionAssignList);
            if (missionAssign != null) {
                dto.setCompleteQueryCount(missionAssign.getCompletedCount());
                dto.setAssignQueryCount(missionAssign.getAssignedCount());
            }
            return dto;
        }).toList();
    }

    @Override
    public EvalUserMissionDTO myDetail(String id) {
        log.debug("获取我的任务详情，参数：{}", id);
        // 1. 获取任务
        GeneratedEvalMission generatedEvalMission = this.missionRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("未获取到测评任务"));
        EvalUserMissionDTO evalUserMissionDTO = new EvalUserMissionDTO();
        evalUserMissionDTO.setMissionId(String.valueOf(generatedEvalMission.getId()));
        evalUserMissionDTO.setMissionName(generatedEvalMission.getName());

        // 2. 获取当前用户所在的组
        GeneratedEvalMissionAssign generatedEvalMissionAssign = this.missionAssignRepository.findUserGroup(CurrentUserUtils.getCurrentAccount(), generatedEvalMission.getId());
        evalUserMissionDTO.setQueryGroupId(String.valueOf(generatedEvalMission.getQueryGroupId()));
        evalUserMissionDTO.setGroup(generatedEvalMissionAssign.getUserGroup());

        // 3. 查询需要标注的query
        List<Integer> todoIdx = convert2IdxList(generatedEvalMissionAssign.getTodoIdxs());
        if (CollectionUtils.isEmpty(todoIdx)) {
            return evalUserMissionDTO;
        }
        Optional<GeneratedEvalQueryGroupDetail> queryDetailOp = queryGroupDetailRepository.findByIdxAndQueryGroupId(todoIdx.getFirst(), generatedEvalMissionAssign.getQueryGroupId());
        queryDetailOp.ifPresent(item -> evalUserMissionDTO.setQuery(item.getQuery()));

        return evalUserMissionDTO;
    }

    /**
     * 查询任务切换
     *
     * @param missionId 测评任务id
     * @param recordId  当前标注记录id
     * @param type      next 下一条， prev 上一条
     * @return
     */
    @Override
    public EvalMissionQuerySwitchDTO querySwitch(String missionId, String recordId, String type) {
        log.info("query switch missionId:{}, recordId:{}, type:{}", missionId, recordId, type);
        Assert.hasText(missionId, "测评任务id不能为空");

        EvalMissionQuerySwitchDTO result = new EvalMissionQuerySwitchDTO();
        String account = CurrentUserUtils.getCurrentAccount();

        //判断当前recordId是否标注完成
        if (StringUtils.isNotBlank(recordId)) {
            GeneratedEvalMarkRecord evalMarkRecord = this.markRecordRepository.findById(Long.valueOf(recordId)).orElseThrow(() -> new LynxiaoException("未获取到标注记录"));
            if (!evalMarkRecord.getFinish()) {
                //当前任务未完成，不能进入下一条
                result.setComplete(false);
                return result;
            }
        } else {
            //如果recordId为空，查找是否还有待标注的query， 有则返回下一条待标注的query
            Optional<String> nextQueryOp = findNeedMarkQuery(missionId, account);
            if (nextQueryOp.isPresent()) {
                result.setQuery(nextQueryOp.get());
                return result;
            }
        }

        //根据missionId 和 用户名 查询所有已完成的任务

        List<GeneratedEvalMarkRecord> allFinishedRecord = this.markRecordRepository.findAllFinishedByMissionIdAndAccount(Long.valueOf(missionId), account);

        Optional<GeneratedEvalMarkRecord> markRecordOpt = findPreOrNextFinishRecord(allFinishedRecord, recordId, type);
        if (markRecordOpt.isPresent()) {
            // 返回已经标注过的记录
            result.setRecordId(String.valueOf(markRecordOpt.get().getId()));
            return result;
        }

        if ("next".equalsIgnoreCase(type)) {
            // 下一条是 未标注的query任务
            //查看当前用户在missionId下是否有未完成的任务
            List<GeneratedEvalMarkRecord> unfinished = this.markRecordRepository.findUnfinishedByAccountAndMissionId(account, Long.valueOf(missionId));
            if (CollectionUtil.isNotEmpty(unfinished)) {
                log.warn("当前任务下有未标注完成的测评任务");
                result.setRecordId(String.valueOf(unfinished.getFirst().getId()));
                return result;
            }

            //查找当前用户在missionId下最新的待标注query
            Optional<String> nextQueryOp = findNeedMarkQuery(missionId, account);
            if (nextQueryOp.isEmpty()) {
                throw new LynxiaoException("没有更多待标注query");
            }
            result.setQuery(nextQueryOp.get());
        }

        return result;
    }


    @Override
    public void updateCompletedQuery(Long missionId, String query) {
        if (missionId == null || StringUtils.isEmpty(query)) {
            return;
        }

        //根据query获取idx
        GeneratedEvalMission mission = missionRepository.findById(missionId).orElseThrow(() -> new LynxiaoException("未获取到测评任务"));
        List<GeneratedEvalQueryGroupDetail> queryList = queryGroupDetailRepository.findAllByQueryGroupIdAndDeletedFalse(mission.getQueryGroupId());
        Optional<GeneratedEvalQueryGroupDetail> queryDetailOp = queryList.stream().filter(item -> item.getQuery().equals(query)).findFirst();
        if (queryDetailOp.isEmpty()) {
            return;
        }

        //更新测评任务分配表中todo_idx
        GeneratedEvalMissionAssign missionAssign = missionAssignRepository.findUserGroup(CurrentUserUtils.getCurrentAccount(), missionId);
        if (missionAssign == null || StringUtils.isEmpty(missionAssign.getTodoIdxs())) {
            log.warn("更新todo_idx失败, missionAssign不存在或todo_idx为空, missionId:{}", missionId);
            return;
        }
        List<Integer> todoIdxList = convert2IdxList(missionAssign.getTodoIdxs());

        if (!Objects.equals(todoIdxList.getFirst(), queryDetailOp.get().getIdx())) {
            // 如果当前queryIdx不是第一个，说明已经标注完成，不需要再更新
            return;
        }
        //将当前任务id从todo_idx中删除
        todoIdxList.removeFirst();
        missionAssignRepository.update(missionAssign.getId(), convert2IdxStr(todoIdxList), missionAssign.getCompletedCount() + 1);
    }

    @Override
    public void createUserAssign(Long missionId, Long queryGroupId, Map<String, List<String>> groupAssignMap) {
        List<GeneratedEvalQueryGroupDetail> queryList = this.queryGroupDetailRepository.findAllByQueryGroupIdAndDeletedFalse(queryGroupId);
        if (CollectionUtil.isEmpty(queryList)) {
            throw new LynxiaoException("未获取到query集数据");
        }
        List<GeneratedEvalMissionAssign> generatedEvalMissionAssignList = assign(missionId, queryGroupId, groupAssignMap, queryList);
        this.missionAssignRepository.saveAll(generatedEvalMissionAssignList);
    }

    @Override
    public void updateUserAssign(GeneratedEvalMission generatedEvalMission, Long queryGroupId, Map<String, List<String>> newGroupAssignMap) {
        // 根据任务id获取历史分配记录
        List<GeneratedEvalMissionAssign> oldAssignList = this.missionAssignRepository.findAllByMissionIdAndDeletedFalse(generatedEvalMission.getId());
        // 人员状态未被移除的,按所属的组  分组
        Map<String, List<GeneratedEvalMissionAssign>> oldGroupAssignMap = oldAssignList.stream().filter(t -> t.getStatus() == 1).collect(Collectors.groupingBy(GeneratedEvalMissionAssign::getUserGroup));

        if (oldGroupAssignMap.size() != newGroupAssignMap.size()) {
            throw new LynxiaoException("组的个数不能变更!");
        }

        List<GeneratedEvalMissionAssign> updateList = new ArrayList<>(); // 记录更新列表，可能存在新增、删除
        // 循环历史每组的分配信息
        for (Map.Entry<String, List<GeneratedEvalMissionAssign>> entry : oldGroupAssignMap.entrySet()) {
            // 组内重新分配
            String group = entry.getKey();
            List<GeneratedEvalMissionAssign> oldGroupAssigns = entry.getValue();
            List<String> newUserList = newGroupAssignMap.get(group);
            List<String> oldUsers = oldGroupAssigns.stream().map(GeneratedEvalMissionAssign::getAccount).toList();
            if (EvalUtils.isSameElement(oldUsers, newUserList)) {
                // 如果人员未变动，不需要要重新分配
                continue;
            }
            // 获取历史待标注的query列表
            List<Integer> todoIdx = getTodoIdxByMissionAndGroup(oldGroupAssigns);
            List<GeneratedEvalQueryGroupDetail> queryGroupDetails = this.queryGroupDetailRepository.findAllByIdxInAndQueryGroupIdIsAndDeletedFalse(todoIdx, queryGroupId);

            // 重新分配该组（包含了新增的和已有的用户）
            List<GeneratedEvalMissionAssign> newAssign = assign(generatedEvalMission.getId(), queryGroupId, Map.of(group, newUserList), queryGroupDetails);
            Map<String, GeneratedEvalMissionAssign> newAssignMap = newAssign.stream().collect(Collectors.toMap(GeneratedEvalMissionAssign::getAccount, item -> item));

            for (GeneratedEvalMissionAssign oldAssign : oldGroupAssigns) {
                if (null != newAssignMap.get(oldAssign.getAccount())) {
                    // 说明当前标注人分组未发生变化，更新分配信息
                    GeneratedEvalMissionAssign newAssignItem = newAssignMap.get(oldAssign.getAccount());
                    oldAssign.setAssignedCount(oldAssign.getCompletedCount() + newAssignItem.getAssignedCount());
                    oldAssign.setTodoIdxs(newAssignItem.getTodoIdxs());
                    updateList.add(oldAssign);
                    // 去掉已经更新的标注人，剩余是新增的用户
                    newAssignMap.remove(oldAssign.getAccount());
                } else {
                    // 说明当前标注人标注任务结束了
                    oldAssign.setAssignedCount(oldAssign.getCompletedCount());
                    oldAssign.setTodoIdxs(null);
                    oldAssign.setStatus(2);
                    updateList.add(oldAssign);
                }
            }
            // newAssignMap中剩余的标注人为需要新增的标注人
            updateList.addAll(newAssignMap.values());
        }
        this.missionAssignRepository.saveAll(updateList);
    }


    private GeneratedEvalMissionAssign getMissionAssign(String account, Long missionId, List<GeneratedEvalMissionAssign> missionAssignList) {
        for (GeneratedEvalMissionAssign missionAssign : missionAssignList) {
            if (missionAssign.getAccount().equals(account) && missionAssign.getMissionId().equals(missionId)) {
                return missionAssign;
            }
        }
        return null;
    }


    private Optional<String> findNeedMarkQuery(String missionId, String account) {
        // 查找当前用户在missionId下最新的待标注query
        GeneratedEvalMission generatedEvalMission = this.missionRepository.findById(Long.valueOf(missionId)).orElseThrow(() -> new LynxiaoException("未获取到测评任务"));
        GeneratedEvalMissionAssign missionAssign = this.missionAssignRepository.findUserGroup(account, Long.valueOf(missionId));
        if (StringUtils.isEmpty(missionAssign.getTodoIdxs())) {
            return Optional.empty();
        }

        Integer firstQueryIdx = convert2IdxList(missionAssign.getTodoIdxs()).getFirst();
        Optional<GeneratedEvalQueryGroupDetail> queryDetailOp = this.queryGroupDetailRepository.findByIdxAndQueryGroupId(firstQueryIdx, generatedEvalMission.getQueryGroupId());

        return queryDetailOp.map(GeneratedEvalQueryGroupDetail::getQuery);
    }

    /**
     * 获取相邻的query标注记录
     *
     * @param allFinishedRecord 所有已完成的标注记录
     * @param currentRecordId   当前标注记录id
     * @param type              prev: 上一个， next: 下一个
     * @return 相邻的标注记录
     */
    private Optional<GeneratedEvalMarkRecord> findPreOrNextFinishRecord(List<GeneratedEvalMarkRecord> allFinishedRecord, String currentRecordId, String type) {
        if (CollectionUtil.isEmpty(allFinishedRecord)) {
            return Optional.empty();
        }
        allFinishedRecord.sort(Comparator.comparing(GeneratedEvalMarkRecord::getCreatedDate));

        if (StringUtils.isBlank(currentRecordId)) {
            // 当前任务不存在，自动滚动到第一条
            return Optional.of(allFinishedRecord.getFirst());
        }

        int currentIndex = -1;
        for (int i = 0; i < allFinishedRecord.size(); i++) {
            if (currentRecordId.equals(String.valueOf(allFinishedRecord.get(i).getId()))) {
                currentIndex = i;
                break;
            }
        }
        if (currentIndex == -1) {
            return Optional.empty();
        }

        if ("prev".equalsIgnoreCase(type) && currentIndex > 0) {
            return Optional.of(allFinishedRecord.get(currentIndex - 1));

        } else if ("next".equalsIgnoreCase(type) && currentIndex + 1 < allFinishedRecord.size()) {
            return Optional.of(allFinishedRecord.get(currentIndex + 1));
        }
        log.warn("没有更多的标注记录了，当前记录id：{}, currentIndex={}", currentRecordId, currentIndex);
        return Optional.empty();
    }


    private List<Integer> getTodoIdxByMissionAndGroup(List<GeneratedEvalMissionAssign> evalMissionAssigns) {
        if (CollectionUtil.isEmpty(evalMissionAssigns)) {
            return new ArrayList<>();
        }
        List<String> todoIdx = evalMissionAssigns.stream().map(GeneratedEvalMissionAssign::getTodoIdxs).toList();
        List<Integer> idxList = new ArrayList<>();
        for (String idxStr : todoIdx) {
            idxList.addAll(convert2IdxList(idxStr));
        }
        return idxList;
    }


    public static List<GeneratedEvalMissionAssign> assign(Long missionId, Long queryGroupId, Map<String, List<String>> dataAssign, List<GeneratedEvalQueryGroupDetail> queryList) {
        // 1. 按照组分配, k:组序号，v:用户名称集合(key:用户账号，value:用户query列表)
        Map<String, Map<String, List<GeneratedEvalQueryGroupDetail>>> groupAssignMap = assignWithGroup(queryList, dataAssign);

        // 2.组装任务分配实体表
        List<GeneratedEvalMissionAssign> assignList = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<GeneratedEvalQueryGroupDetail>>> entry : groupAssignMap.entrySet()) {
            String groupKey = entry.getKey();
            Map<String, List<GeneratedEvalQueryGroupDetail>> userAccountQueriesMap = entry.getValue();
            for (Map.Entry<String, List<GeneratedEvalQueryGroupDetail>> userEntry : userAccountQueriesMap.entrySet()) {
                String userName = userEntry.getKey();
                List<GeneratedEvalQueryGroupDetail> queries = userEntry.getValue();
                // 保存分配结果
                GeneratedEvalMissionAssign generatedEvalMissionAssign = new GeneratedEvalMissionAssign();
                generatedEvalMissionAssign.setMissionId(missionId);
                generatedEvalMissionAssign.setQueryGroupId(queryGroupId);
                generatedEvalMissionAssign.setUserGroup(groupKey);
                generatedEvalMissionAssign.setAccount(userName);
                generatedEvalMissionAssign.setStatus(1);
                generatedEvalMissionAssign.setAssignedCount(queries.size());
                String idxString = convert2IdxStr(queries.stream().map(GeneratedEvalQueryGroupDetail::getIdx).toList());
                generatedEvalMissionAssign.setTodoIdxs(idxString);
                generatedEvalMissionAssign.setDeleted(false);
                generatedEvalMissionAssign.setCompletedCount(0);
                generatedEvalMissionAssign.setCompletedCount(0);
                AuditingEntityUtil.fillCreateValue(generatedEvalMissionAssign);
                assignList.add(generatedEvalMissionAssign);
            }
        }
        return assignList;
    }

    /**
     * 按照用户组进行分配
     *
     * @param queryList query列表
     * @param userGroup 数据分配  key:组的序号  value:用户账号列表
     * @return 每组的分配结果   key:组的序号  value:用户名称集合(key:用户账号，value:用户query列表)
     */
    private static Map<String, Map<String, List<GeneratedEvalQueryGroupDetail>>> assignWithGroup(List<GeneratedEvalQueryGroupDetail> queryList, Map<String, List<String>> userGroup) {
        Map<String, Map<String, List<GeneratedEvalQueryGroupDetail>>> result = new HashMap<>();

        for (Map.Entry<String, List<String>> entry : userGroup.entrySet()) {
            String groupKey = entry.getKey();
            List<String> users = entry.getValue();
            Map<String, List<GeneratedEvalQueryGroupDetail>> groupAllocation = assignWithUsers(queryList, users);
            result.put(groupKey, groupAllocation);
        }

        return result;
    }

    /**
     * 根据用户平分query集，key为用户名称，value为分配的query列表
     */
    private static Map<String, List<GeneratedEvalQueryGroupDetail>> assignWithUsers(List<GeneratedEvalQueryGroupDetail> queries, List<String> users) {
        Map<String, List<GeneratedEvalQueryGroupDetail>> allocation = new HashMap<>();
        int userCount = users.size();
        if (userCount == 0) {
            return allocation;
        }
        List<List<GeneratedEvalQueryGroupDetail>> splitResult = new ArrayList<>();
        int total = queries.size();
        int baseSize = total / userCount;
        int remainder = total % userCount;

        int index = 0;
        for (int i = 0; i < userCount; i++) {
            int currentSize = baseSize + (i < remainder ? 1 : 0);
            int endIndex = index + currentSize;
            if (endIndex > total) {
                endIndex = total;
            }
            splitResult.add(new ArrayList<>(queries.subList(index, endIndex)));
            index = endIndex;
        }
        for (int i = 0; i < userCount; i++) {
            allocation.put(users.get(i), splitResult.get(i));
        }

        return allocation;
    }

    private static List<Integer> convert2IdxList(String idxStr) {
        return StringUtils.isBlank(idxStr) ? new ArrayList<>(0) : Arrays.stream(idxStr.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }

    private static String convert2IdxStr(List<Integer> idxList) {
        return StringUtils.join(idxList, ",");
    }

}
