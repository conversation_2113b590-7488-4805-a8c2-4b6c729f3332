package com.iflytek.lynxiao.portal.eval.component.core;

import com.iflytek.lynxiao.portal.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.portal.eval.utils.EvalUtils;
import com.iflytek.lynxiao.portal.eval.utils.WorkflowUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * goodcase归因分析 预评估调用链信息
 *
 * <AUTHOR>  2025/3/6 18:04
 */
public class PreEvalNodeChain {


    // 执行组件链
    private final List<PreEvalNode> nodeChain = new ArrayList<>();

    public PreEvalNodeChain(List<TraceLogItem> traceLogItems) {
        List<PreEvalNode> templateNodeChain = new ArrayList<>();

        //初始化组件链 仅包含非召回节点
        WorkflowUtil.concernNodeCacheList.forEach(item->{
            if (item.isCanMock()){
                templateNodeChain.add(PreEvalNode.builder().code(item.getCode()).canBeMock(true).build());
            }
        });

        //实际执行的节点列表
        for (PreEvalNode templateNode : templateNodeChain) {
            for (TraceLogItem traceLogItem : traceLogItems) {
                if (traceLogItem.getCode().equals(templateNode.getCode())) {
                    this.nodeChain.add(PreEvalNode.builder()
                            .code(templateNode.getCode())
                            .canBeMock(templateNode.isCanBeMock())
                            .nodeName(traceLogItem.getName())
                            .nodeId(traceLogItem.getNodeId())
                            .build());
                }
            }
        }
    }

    public List<PreEvalNode> getNodeChain() {
        return this.nodeChain;
    }

    public PreEvalNode getFirstNode() {
        return this.nodeChain.getFirst();
    }

    /**
     * 检查组件是否需要mock
     */
    public void checkNeedMock(String docId, List<TraceLogItem> traceLogs) {
        for (PreEvalNode preEvalNode : this.nodeChain) {
            for (TraceLogItem traceLog : traceLogs) {
                if (traceLog.getNodeId().equals(preEvalNode.getNodeId())) {
                    // 如果当前节点的执行结果中包含目标文档id，则不需要mock
                    if (EvalUtils.isIdExist(traceLog.getDocs(), docId)) {
                        preEvalNode.setCanBeMock(false);
                    }
                }
            }
        }
    }
}
