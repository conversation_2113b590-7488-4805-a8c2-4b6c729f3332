package com.iflytek.lynxiao.portal.eval.component.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
@Accessors(chain = true)
public class AscribeInputBadCase extends Jsonable {
    /**
     * 请求query
     */
    private String query;

    /**
     * 场景策略流程id
     */
    private String processId;

    /**
     * traceId 历史执行记录的traceId， 用于从es中获取历史执行记录
     */
    private String traceId;

    /**
     * 记录当前流程执行的路径id
     */
    private String _traceId;

    /**
     * 测评对象id
     */
    private String targetId;

    /**
     * region  hf、sh
     */
    private String region;

    /**
     * badUrl
     */
    private String badUrl = "";

    /**
     * goodUrl, 多条url之间通过 换行符、逗号（中英文）、分号（中英文）、空格分割
     */
    private String goodUrl;

    /**
     * badId
     */
    private String badId = "";

    /**
     * goodId, 多条id之间通过 换行符、逗号（中英文）、分号（中英文）、空格分割
     */
    private String goodId;


    /**
     * payload 流程入参
     */
    private JSONObject payload = new JSONObject();


    private static final String delimiters = "\\R|，|,|；|;|\\s+";


    public List<String> getGoodList() {
        if (StringUtils.isNotBlank(this.goodId)) {
            return getGoodIdList();
        } else {
            return getGoodUrlList();
        }
    }

    public List<String> getGoodUrlList() {
        if (StringUtils.isBlank(this.goodUrl)) {
            return new ArrayList<>();
        }

        // 将非空字符串添加到结果列表，并去掉多余的空格
        String[] parts = goodUrl.split(delimiters);
        return Arrays.stream(parts)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    private List<String> getGoodIdList() {
        // 将非空字符串添加到结果列表，并去掉多余的空格
        String[] parts = goodId.split(delimiters);
        return Arrays.stream(parts)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

}
