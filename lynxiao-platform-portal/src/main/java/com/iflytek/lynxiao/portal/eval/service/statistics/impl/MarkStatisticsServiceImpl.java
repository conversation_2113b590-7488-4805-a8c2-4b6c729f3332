package com.iflytek.lynxiao.portal.eval.service.statistics.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.lynxiao.common.utils.DownloadUtils;
import com.iflytek.lynxiao.portal.eval.domain.*;
import com.iflytek.lynxiao.portal.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.portal.eval.domain.mark.result.MarkResultData;
import com.iflytek.lynxiao.portal.eval.domain.mark.result.MarkResultGroup;
import com.iflytek.lynxiao.portal.eval.domain.mark.result.MarkResultRecall;
import com.iflytek.lynxiao.portal.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.portal.eval.dto.mission.EvalMissionFilterParamsDTO;
import com.iflytek.lynxiao.portal.eval.dto.statistics.MarkResultAnalysisDTO;
import com.iflytek.lynxiao.portal.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.portal.eval.dto.statistics.MarkStatisticsViewDTO;
import com.iflytek.lynxiao.portal.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.portal.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.portal.eval.repository.MarkResultRepository;
import com.iflytek.lynxiao.portal.eval.repository.MarkTargetRepository;
import com.iflytek.lynxiao.portal.eval.service.mission.EvalMissionService;
import com.iflytek.lynxiao.portal.eval.service.statistics.MarkAnalysisService;
import com.iflytek.lynxiao.portal.eval.service.statistics.MarkStatisticsService;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedEvalMissionAssign;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedFlowVersion;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedMetaRegion;
import com.iflytek.lynxiao.resource.repository.EvalMarkRecordRepository;
import com.iflytek.lynxiao.resource.repository.EvalMissionAssignRepository;
import com.iflytek.lynxiao.resource.repository.FlowVersionRepository;
import com.iflytek.lynxiao.resource.repository.MetaRegionRepository;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标注结果统计
 *
 * <AUTHOR>
 */
@Log4j2
@Service
@Transactional(rollbackFor = Exception.class)
public class MarkStatisticsServiceImpl implements MarkStatisticsService {

    /**
     * k: strategyId或者processId, v:processName(策略名称), 用于前端过滤
     */
    private final Map<String, String> strategyIdOrProcessIdNameCache = new HashMap<>();

    private final MetaRegionRepository metaRegionRepository;
    private final FlowVersionRepository flowVersionRepository;
    private final EvalMissionAssignRepository evalMissionAssignRepository;
    private final MarkResultRepository markResultRepository;
    private final EvalMissionService evalMissionService;

    private final MarkAnalysisService markAnalysisService;
    private final EvalMarkRecordRepository evalMarkRecordRepository;
    private final MarkTargetRepository markTargetRepository;

    public MarkStatisticsServiceImpl(MetaRegionRepository metaRegionRepository, FlowVersionRepository flowVersionRepository,
                                     EvalMissionAssignRepository evalMissionAssignRepository, MarkResultRepository markResultRepository,
                                     EvalMissionService evalMissionService, MarkAnalysisService markAnalysisService,
                                     EvalMarkRecordRepository evalMarkRecordRepository, MarkTargetRepository markTargetRepository) {
        this.metaRegionRepository = metaRegionRepository;
        this.flowVersionRepository = flowVersionRepository;
        this.evalMissionAssignRepository = evalMissionAssignRepository;
        this.markResultRepository = markResultRepository;
        this.evalMissionService = evalMissionService;
        this.markAnalysisService = markAnalysisService;
        this.evalMarkRecordRepository = evalMarkRecordRepository;
        this.markTargetRepository = markTargetRepository;
    }

    @Override
    public Page<MarkStatisticsViewDTO> findByPage(MarkResultQueryDTO dto, Pageable pageable) {
        log.debug("查询标注结果, dto:{}", dto);
        // 重置查询条件
        dto.setMarkRecordIds(getRecordIds(dto));
        // 处理场景策略筛选,根据缓存取策略名称对应的strategyId
        refreshStrategyIdNameCache(dto);
        dto.setStrategyIds(getStrategyIds(dto));
        //处理策略比对
        getRecordIdsByContrastStrategy(dto);
        long count = this.markResultRepository.count(dto);
        if (count == 0) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
        List<MarkStatisticsViewDTO> resultDetailList = getDataListByCondition(dto, pageable);
        return new PageImpl<>(resultDetailList, pageable, count);
    }

    @Override
    public void export(MarkResultQueryDTO dto, HttpServletResponse response) {
        dto.setMarkRecordIds(getRecordIds(dto));
        refreshStrategyIdNameCache(dto);
        dto.setStrategyIds(getStrategyIds(dto));
        //处理策略比对
        getRecordIdsByContrastStrategy(dto);
        List<MarkStatisticsViewDTO> resultListDTOList = getDataListByCondition(dto, null);
        // 配置固定列
        LinkedHashMap<String, Function<MarkStatisticsViewDTO, Object>> fixedColumns = new LinkedHashMap<>();
        fixedColumns.put("策略名称", MarkStatisticsViewDTO::getSceneProcessName);
        fixedColumns.put("归因模式", MarkStatisticsViewDTO::getAscribeMode);
        fixedColumns.put("环境", MarkStatisticsViewDTO::getRegionCode);
        fixedColumns.put("query", MarkStatisticsViewDTO::getQuery);
        fixedColumns.put("标注人", MarkStatisticsViewDTO::getAccount);
        fixedColumns.put("标注时间", MarkStatisticsViewDTO::getLastModifiedDate);
        fixedColumns.put("traceId", MarkStatisticsViewDTO::getTraceId);
        fixedColumns.put("id", MarkStatisticsViewDTO::getDocId);
        fixedColumns.put("来源", MarkStatisticsViewDTO::getTypeName);
        fixedColumns.put("结果", MarkStatisticsViewDTO::getDocIdx);
        fixedColumns.put("URL", MarkStatisticsViewDTO::getUrl);
        fixedColumns.put("标题", MarkStatisticsViewDTO::getTitle);
        try {
            DownloadUtils.exportWithDynamicHeaders(
                    response,
                    "markResult_" + System.currentTimeMillis(),
                    resultListDTOList,
                    fixedColumns,
                    MarkStatisticsViewDTO::getExtendFieldMap
            );
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public EvalMissionFilterParamsDTO findFilterParams(String missionId) {
        EvalMissionFilterParamsDTO result = new EvalMissionFilterParamsDTO();
        EvalMissionDTO missionDTO = this.evalMissionService.findById(missionId);
        List<StrategyConfig> strategyConfigList = missionDTO.getStrategyConfig();
        // 获取策略配置topK的最大值
        OptionalInt max = strategyConfigList.stream().mapToInt(StrategyConfig::getTopK).max();
        if (max.isPresent()) {
            result.setTopK(max.getAsInt());
        }

        result.setGroupNumber(missionDTO.getGroupNumber());
        return result;
    }

    /**
     * 标注结果分析
     *
     * @param dto 标注结果查询条件
     */
    @Override
    public List<MarkResultAnalysisDTO> analysis(MarkResultQueryDTO dto) {
        // 查询已完成的测评记录
        dto.setMarkRecordIds(getFinishedMarkRecordIds(dto));
        // 1. 根据查询条件获取标注结果
        List<MarkResultEntity> markResultEntities = this.markResultRepository.findRecallList(dto);
        List<MarkTargetEntity> markTargetEntities = this.markTargetRepository.findAllFinishedByMissionIdAndMarkRecordIds(dto.getMissionId(), dto.getMarkRecordIds());

        // 2. 查询必备数据，为回显做准备
        EvalMissionDTO missionDTO = this.evalMissionService.findById(dto.getMissionId());
        Map<String, GeneratedMetaRegion> regionMap = findRegionMap();
        Map<String, GeneratedFlowVersion> processIdFlowVersionMap = findFlowVersionMap(markResultEntities);

        // 3. 将标注结果按照模式分组
        List<MarkResultGroup> groupList = fetchGroupList(dto, missionDTO.getStrategyConfig(), markResultEntities, markTargetEntities);

        // 4. 根据分组的标注结果，进行统计分析
        return markAnalysisService.analysis(dto, groupList, missionDTO, regionMap, processIdFlowVersionMap, markTargetEntities);
    }


    @Override
    public void exportAnalysis(MarkResultQueryDTO dto, HttpServletResponse response) {
        List<MarkResultAnalysisDTO> analysisList = analysis(dto);

        // 配置固定列
        LinkedHashMap<String, Function<MarkResultAnalysisDTO, Object>> fixedColumns = new LinkedHashMap<>();
        // 针对多query任务标注模式有不同的列
        if (StringUtils.isNotBlank(dto.getMissionId())) {
            fixedColumns.put("标注人", MarkResultAnalysisDTO::getMarkUser);
            fixedColumns.put("分配query条数", MarkResultAnalysisDTO::getAssignQueryCount);
            fixedColumns.put("已完成query条数", MarkResultAnalysisDTO::getCompleteQueryCount);
            fixedColumns.put("未完成query条数", MarkResultAnalysisDTO::getNoCompleteQueryCount);
            fixedColumns.put("完成率", MarkResultAnalysisDTO::getCompletePercentage);
            fixedColumns.put("有结果query数", MarkResultAnalysisDTO::getHasResultQueryCount);
        } else {
            fixedColumns.put("标注人", MarkResultAnalysisDTO::getCreatedBy);
            fixedColumns.put("query条数", MarkResultAnalysisDTO::getQueryCount);
        }
        fixedColumns.put("策略名称", MarkResultAnalysisDTO::getSceneProcessName);
        fixedColumns.put("归因模式", MarkResultAnalysisDTO::getAscribeMode);
        fixedColumns.put("环境", MarkResultAnalysisDTO::getRegionName);
        fixedColumns.put("开始时间", MarkResultAnalysisDTO::getMarkMinTime);
        fixedColumns.put("结束时间", MarkResultAnalysisDTO::getMarkMaxTime);
        fixedColumns.put("含≥1条goodUrl结果的query数", MarkResultAnalysisDTO::getOneGoodDocQuery);
        fixedColumns.put("有结果吸收率", MarkResultAnalysisDTO::getHasResultAbsorbPercentage);
        fixedColumns.put("Doc数", MarkResultAnalysisDTO::getDocCount);
        fixedColumns.put("good doc", MarkResultAnalysisDTO::getGoodDocCount);
        fixedColumns.put("good率", MarkResultAnalysisDTO::getGoodDocPercentage);
        fixedColumns.put("bad doc", MarkResultAnalysisDTO::getBadDocCount);
        fixedColumns.put("TopK完全good的query数", MarkResultAnalysisDTO::getTopKGoodQuery);
        fixedColumns.put("TopK完全good率", MarkResultAnalysisDTO::getTopKGoodQueryPercentage);

        try {
            DownloadUtils.exportWithDynamicHeaders(response, "markAnalysis_" + System.currentTimeMillis(), analysisList, fixedColumns, MarkResultAnalysisDTO::getExtendMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取标注记录id列表
     * 当查询条件column等于TOPK_GOOD_QUERY或ONE_GOOD_DOC, 使用dto中的markRecordIds作为查询条件
     * 否则使用已完成的标注记录id列表作为查询条件
     */
    private List<String> getRecordIds(MarkResultQueryDTO dto) {
        if (MarkResultSearchTypeEnum.TOPK_GOOD_QUERY.getCode().equals(dto.getColumn())
                || MarkResultSearchTypeEnum.ONE_GOOD_DOC.getCode().equals(dto.getColumn())) {
            return CollectionUtils.isEmpty(dto.getMarkRecordIds()) ? new ArrayList<>() : dto.getMarkRecordIds();
        } else {
            return getFinishedMarkRecordIds(dto);
        }
    }

    /**
     * 获取分组列表
     */
    private List<MarkResultGroup> fetchGroupList(MarkResultQueryDTO dto, List<StrategyConfig> strategyConfigs, List<MarkResultEntity> markResultEntities, List<MarkTargetEntity> markTargetEntities) {
        // 将标注结果按照统计维度分组，标注模式、测评任务模式有不同的统计维度
        if (getMarkMode(dto) == MarkMode.MISSION) {
            List<GeneratedEvalMissionAssign> missionAssigns = this.evalMissionAssignRepository.findUndeletedByMissionIdAndUserGroup(Long.valueOf(dto.getMissionId()),
                    StringUtils.isBlank(dto.getUserGroup()) ? null : dto.getUserGroup());
            return MarkResultGroup.groupByMission(strategyConfigs, missionAssigns, markResultEntities, markTargetEntities);
        } else if (getMarkMode(dto) == MarkMode.SINGLE) {
            return MarkResultGroup.groupByStrategyId(markResultEntities, markTargetEntities);
        } else {
            // todo 分体验模式
            return new ArrayList<>();
        }
    }

    private Map<String, GeneratedMetaRegion> findRegionMap() {
        return metaRegionRepository.findAllByDeletedFalse(Sort.unsorted()).stream().collect(Collectors.toMap(GeneratedMetaRegion::getCode, r -> r));
    }

    /**
     * 根据标注结果查询关联的流程版本
     *
     * @param resultEntities 标注结果
     * @return key: processId, value: GeneratedFlowVersion
     */
    private Map<String, GeneratedFlowVersion> findFlowVersionMap(List<MarkResultEntity> resultEntities) {
        if (CollectionUtil.isEmpty(resultEntities)) {
            return new HashMap<>();
        }
        List<String> processIds = resultEntities.stream()
                .map(MarkResultEntity::getSceneProcessId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        return flowVersionRepository.findByProcessIdIn(processIds).stream().collect(Collectors.toMap(GeneratedFlowVersion::getProcessId, t -> t));
    }

    /**
     * 获取已经完成的标注记录id列表
     */
    private List<String> getFinishedMarkRecordIds(MarkResultQueryDTO dto) {
        List<String> finishedMarkRecords;
        if (getMarkMode(dto) == MarkMode.MISSION) {
            finishedMarkRecords = this.evalMarkRecordRepository.findByMissionIdAndDeletedFalse(Long.valueOf(dto.getMissionId()))
                    .stream()
                    .filter(item -> {
                        if (StringUtils.isBlank(dto.getUserGroup())) {
                            return true;
                        }
                        return dto.getUserGroup().equals(item.getUserGroup());
                    })
                    .map(item -> String.valueOf(item.getId())).toList();
        } else {
            //todo 体验模式
            finishedMarkRecords = null;
        }

        if (MarkResultSearchTypeEnum.HAS_RESULT_QUERY.getCode().equals(dto.getColumn())) {
            // 只获取有结果的标注记录
            return this.markTargetRepository.findAllByRecordIdsWithOutData(finishedMarkRecords).stream().filter(MarkTargetEntity::isRecalled).map(MarkTargetEntity::getMarkRecordId).distinct().toList();
        }

        return finishedMarkRecords;
    }


    private void getRecordIdsByContrastStrategy(MarkResultQueryDTO dto) {
        if (StringUtils.isBlank(dto.getContrastStrategyIdA()) || StringUtils.isBlank(dto.getContrastStrategyIdB())
                || dto.getOperator() == null
                || StringUtils.isBlank(dto.getMissionId())) {
            log.warn("未配置策略比对参数，跳过策略比对, dto:{}", dto);
            return;
        }

        List<MarkResultEntity> recallList = this.markResultRepository.findRecallList(dto);
        EvalMissionDTO missionDTO = this.evalMissionService.findById(dto.getMissionId());

        if (missionDTO.getStandardConfig() == null || CollectionUtils.isEmpty(missionDTO.getStandardConfig().getClassifyRules())) {
            log.error("测评任务标准配置有误，无法进行策略比对, missionId:{}", dto.getMissionId());
            return;
        }

        List<DocClassifyRule> classifyRules = missionDTO.getStandardConfig().getClassifyRules();
        List<String> resultRecordIds = recallList.stream()
                .map(MarkResultEntity::getMarkRecordId)
                .distinct()
                .filter(recordId -> compareGoodDocCounts(recordId, recallList, dto, classifyRules))
                .toList();

        dto.setMarkRecordIds(resultRecordIds);
    }

    private boolean compareGoodDocCounts(String recordId, List<MarkResultEntity> recallList, MarkResultQueryDTO dto, List<DocClassifyRule> classifyRules) {
        List<MarkResultEntity> resultEntities4A = filterByStrategyId(recordId, recallList, dto.getContrastStrategyIdA());
        List<MarkResultEntity> resultEntities4B = filterByStrategyId(recordId, recallList, dto.getContrastStrategyIdB());

        int goodDocCountA = getGoodDocCount(resultEntities4A, classifyRules);
        int goodDocCountB = getGoodDocCount(resultEntities4B, classifyRules);

        return switch (dto.getOperator()) {
            case ET -> goodDocCountA == goodDocCountB;
            case GT -> goodDocCountA > goodDocCountB;
            case LT -> goodDocCountA < goodDocCountB;
            default -> false;
        };
    }

    private List<MarkResultEntity> filterByStrategyId(String recordId, List<MarkResultEntity> recallList, String strategyId) {
        return recallList.stream()
                .filter(item -> recordId.equals(item.getMarkRecordId()) && strategyId.equals(item.getStrategyId()))
                .toList();
    }

    private int getGoodDocCount(List<MarkResultEntity> resultEntities, List<DocClassifyRule> classifyRules) {
        int goodDocCount = 0;
        for (MarkResultEntity entity : resultEntities) {
            MarkResultData data = entity.getData();
            if (data == null) {
                continue;
            }
            MarkResultRecall recall = data.getRecall();
            if (recall == null || recall.isIgnore() || CollectionUtils.isEmpty(recall.getDimsList())) {
                continue;
            }
            UrlEvalType urlEvalType = DocClassifyRule.classifyBaseRule(recall.getDimsList(), classifyRules);
            if (UrlEvalType.GOOD_DOC == urlEvalType) {
                goodDocCount++;
            }
        }
        return goodDocCount;
    }

    /**
     * 获取测评模式
     *
     * @param dto 标注结果查询条件
     */
    private int getMarkMode(MarkResultQueryDTO dto) {
        if (StringUtils.isNotBlank(dto.getMissionId())) {
            return MarkMode.MISSION;
        } else {
            return MarkMode.EXPERIENCE;
        }
    }

    /**
     * 刷新策略id和名称的缓存
     */
    private void refreshStrategyIdNameCache(MarkResultQueryDTO dto) {

        List<MarkResultEntity> resultEntities = this.markResultRepository.findAllSceneProcessId(dto);

        // 获取关联数据，后续回显数据使用
        Map<String, GeneratedFlowVersion> flowVersionIdMap = findFlowVersionMap(resultEntities);

        //没有missionId
        List<String> processIds = resultEntities.stream().filter(item -> StringUtils.isBlank(item.getMissionId())).map(MarkResultEntity::getSceneProcessId).distinct().toList();
        processIds.forEach(processId -> {
            GeneratedFlowVersion flowVersion = flowVersionIdMap.get(processId);
            if (flowVersion != null) {
                strategyIdOrProcessIdNameCache.put(processId, flowVersion.getName());
            }
        });

        //有missionId
        resultEntities.stream().map(MarkResultEntity::getMissionId).filter(StringUtils::isNotBlank).distinct().forEach(missionId -> {

            EvalMissionDTO missionDTO = this.evalMissionService.findById(missionId);// 确保标准id存在
            for (StrategyConfig strategyConfig : missionDTO.getStrategyConfig()) {
                if (StringUtils.isNotBlank(strategyConfig.getProcessId())) {
                    GeneratedFlowVersion flowVersion = flowVersionIdMap.get(strategyConfig.getProcessId());
                    strategyIdOrProcessIdNameCache.put(strategyConfig.getId(), flowVersion == null ? "" : flowVersion.getName());
                } else {
                    strategyIdOrProcessIdNameCache.put(strategyConfig.getId(), strategyConfig.getCompName());
                }
            }
        });
    }

    /**
     * 根据search获取策略id列表
     * search account、 query或者 流程名称
     *
     * @param dto
     * @return
     */
    private List<String> getStrategyIds(MarkResultQueryDTO dto) {
        if (StringUtils.isBlank(dto.getSearch())) {
            return new ArrayList<>(0);
        }
        List<String> strategyIds = new ArrayList<>();
        for (Map.Entry<String, String> entry : strategyIdOrProcessIdNameCache.entrySet()) {
            if (entry.getValue().contains(dto.getSearch().trim())) {
                strategyIds.add(entry.getKey());
            }
        }
        if (CollectionUtil.isNotEmpty(strategyIds)) {
            // 如果有策略id，移除search中的策略名称
            dto.setSearch(null);
        }
        return strategyIds;
    }

    private List<MarkStatisticsViewDTO> getDataListByCondition(MarkResultQueryDTO dto, Pageable pageable) {
        List<MarkResultEntity> markResultEntities;
        if (null != pageable) {
            markResultEntities = this.markResultRepository.findPage(dto, pageable);
        } else {
            markResultEntities = this.markResultRepository.findRecallList(dto);
        }

        // dto转换
        return MarkStatisticsViewDTO.of(markResultEntities, findRegionMap(), strategyIdOrProcessIdNameCache);
    }
}
