package com.iflytek.lynxiao.portal.eval.service.search.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.common.feign.region.MetaDictFeign;
import com.iflytek.lynxiao.data.dto.FlowVersionDTO;
import com.iflytek.lynxiao.data.dto.meta.MetaDictDTO;
import com.iflytek.lynxiao.portal.eval.component.core.impl.ProcTraceFetcher4TraceId;
import com.iflytek.lynxiao.portal.eval.component.domain.ProcTraceFetcherParam;
import com.iflytek.lynxiao.portal.eval.component.domain.ProcTraceFetcherResult;
import com.iflytek.lynxiao.portal.eval.config.EvalProperties;
import com.iflytek.lynxiao.portal.eval.domain.IntentDictItem;
import com.iflytek.lynxiao.portal.eval.domain.QueryExportStatusEnum;
import com.iflytek.lynxiao.portal.eval.dto.mark.record.TraceRecordDTO;
import com.iflytek.lynxiao.portal.eval.dto.search.SearchApiResultDTO;
import com.iflytek.lynxiao.portal.eval.dto.search.SearchApiResultExportDTO;
import com.iflytek.lynxiao.portal.eval.dto.search.SearchApiResultQueryDTO;
import com.iflytek.lynxiao.portal.eval.entity.EvalSearchApiExportTaskEntity;
import com.iflytek.lynxiao.portal.eval.repository.ExportTaskRepository;
import com.iflytek.lynxiao.portal.eval.service.search.SearchApiResultFetcher;
import com.iflytek.lynxiao.portal.eval.service.search.SearchApiResultService;
import com.iflytek.lynxiao.portal.eval.utils.TraceLogUtils;
import com.iflytek.lynxiao.portal.flow.service.core.FlowVersionService;
import com.iflytek.lynxiao.portal.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.resource.generated.domain.GeneratedApplication;
import com.iflytek.lynxiao.resource.repository.ApplicationRepository;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import skynet.boot.pandora.api.ApiResponse;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 搜索结果
 */
@Log4j2
@Service
public class SearchApiResultServiceImpl implements SearchApiResultService {

    private final SearchApiResultFetcher searchApiResultFetcher;

    private final ExportTaskRepository exportTaskRepository;

    @Qualifier("platformGridFsTemplate")
    private final GridFsTemplate gridFsTemplate;

    private final ProcTraceFetcher4TraceId procTraceFetcher4TraceId;

    private final LynxiaoFeignClientManager lynxiaoFeignClientManager;

    private final ApplicationRepository applicationRepository;

    private final FlowVersionService flowVersionService;
    private final WorkflowProcessService workflowProcessService;

    private final EvalProperties evalProperties;


    private final ExecutorService executorService = new ThreadPoolExecutor(
            1,
            1,
            0L, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(5),
            new ThreadPoolExecutor.AbortPolicy() // 拒绝策略,抛出 RejectedExecutionException
    );

    public SearchApiResultServiceImpl(SearchApiResultFetcher searchApiResultFetcher,
                                      ExportTaskRepository exportTaskRepository,
                                      GridFsTemplate gridFsTemplate,
                                      ProcTraceFetcher4TraceId procTraceFetcher4TraceId,
                                      LynxiaoFeignClientManager lynxiaoFeignClientManager,
                                      ApplicationRepository applicationRepository,
                                      FlowVersionService flowVersionService, WorkflowProcessService workflowProcessService, EvalProperties evalProperties) {
        this.searchApiResultFetcher = searchApiResultFetcher;
        this.exportTaskRepository = exportTaskRepository;
        this.gridFsTemplate = gridFsTemplate;
        this.procTraceFetcher4TraceId = procTraceFetcher4TraceId;
        this.lynxiaoFeignClientManager = lynxiaoFeignClientManager;
        this.applicationRepository = applicationRepository;
        this.flowVersionService = flowVersionService;
        this.workflowProcessService = workflowProcessService;
        this.evalProperties = evalProperties;
    }

    @Override
    public Page<SearchApiResultDTO> page(SearchApiResultQueryDTO dto) {
        log.debug("page dto: {}", dto);
        List<GeneratedApplication> generatedApplications = applicationRepository.searchEnableList();
        // 特殊处理app类型查询参数,如果同时选择了应用类型和应用ID,则两者取交集，如果交集为空值，则返回空结果
        if (CollectionUtil.isNotEmpty(dto.getAppTypeList())) {
            List<String> appIdList = generatedApplications.stream().filter(t -> dto.getAppTypeList().contains(t.getType())).map(GeneratedApplication::getAppId).toList();
            if (CollectionUtil.isNotEmpty(dto.getAppIdList())) {
                dto.getAppIdList().retainAll(appIdList);
                if (CollectionUtil.isEmpty(dto.getAppIdList())) {
                    return new PageImpl<>(List.of(), Pageable.ofSize(dto.getSize()), 0); // 返回空结果
                }
            } else {
                dto.setAppIdList(appIdList);
            }
        }
        // 条件分页查询ES(浅分页，最大返回10000条)
        Page<JSONObject> pageEsResult = this.searchApiResultFetcher.fetchPage(dto);
        if (pageEsResult == null || pageEsResult.getContent().isEmpty()) {
            return new PageImpl<>(List.of(), Pageable.ofSize(dto.getSize()), 0);
        }
        // 转换DTO
        Map<String, IntentDictItem> dictItemMap = intentDict().stream().collect(Collectors.toMap(IntentDictItem::getDictCode, t -> t)); // 获取意图字典配置
        Map<String, GeneratedApplication> appInfoMap = generatedApplications.stream().collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));
        Map<String, FlowVersionDTO> flowVersionMap = flowVersionService.findOpenedList().stream().collect(Collectors.toMap(FlowVersionDTO::getId, t -> t)); // 获取产品版本列表
        List<SearchApiResultDTO> list = pageEsResult.getContent().stream().map(t -> transfer(t, dictItemMap, appInfoMap, flowVersionMap, dto.getRegion())).toList();
        return new PageImpl<>(list, Pageable.ofSize(dto.getSize()), pageEsResult.getTotalElements());
    }

    @Override
    public void export(SearchApiResultExportDTO dto) {
        log.debug("export region: {}, dto: {}", dto.getRegion(), dto);
        Integer DEFAULT_COUNT = 100000;
        if (dto.getCount() > DEFAULT_COUNT) {
            throw new LynxiaoException("导出数据量不能超过" + DEFAULT_COUNT + "条");
        }

        // 1. 初始化导出任务
        EvalSearchApiExportTaskEntity queryExportTask = new EvalSearchApiExportTaskEntity();
        queryExportTask.setCondition(dto);
        queryExportTask.setStatus(QueryExportStatusEnum.EXPORTING.getCode());
        AuditingEntityUtil.fillCreateValueMongo(queryExportTask);
        exportTaskRepository.save(queryExportTask);
        // 使用线程池异步执行导出任务
        executorService.submit(() -> {
            try {
                // 2. 查询符合条件数据
                SearchApiResultQueryDTO condition = BeanUtil.copyProperties(dto, SearchApiResultQueryDTO.class);
                List<JSONObject> result = this.searchApiResultFetcher.fetchList(condition, dto.getCount());
                // 3. 解析转换为导出DTO
                Map<String, IntentDictItem> dictItemMap = intentDict().stream().collect(Collectors.toMap(IntentDictItem::getDictCode, t -> t)); // 获取意图字典配置
                Map<String, GeneratedApplication> appInfoMap = applicationRepository.searchEnableList().stream().collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));
                Map<String, FlowVersionDTO> flowVersionMap = flowVersionService.findOpenedList().stream().collect(Collectors.toMap(FlowVersionDTO::getId, t -> t)); // 获取产品版本列表
                List<SearchApiResultDTO> excelDataList = result.stream().map(t -> transfer(t, dictItemMap, appInfoMap, flowVersionMap, dto.getRegion())).toList();
                // 4. 导出为excel, 并使用mongo的 GridFS方式存储导出文件,得到文件id
                String fileId = storeFileToGridFs(excelDataList);
                // 5. 更新导出任务状态已完成,关联文件id
                queryExportTask.setStatus(QueryExportStatusEnum.SUCCESS.getCode());
                queryExportTask.setFileId(fileId);
                queryExportTask.getCondition().setCount(excelDataList.size()); // 记录导出真实数量
                queryExportTask.setLastModifiedDate(Instant.now());
                exportTaskRepository.save(queryExportTask);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                queryExportTask.setStatus(QueryExportStatusEnum.FAIL.getCode());
                queryExportTask.setErrorMsg(e.getMessage());
                queryExportTask.setLastModifiedDate(Instant.now());
                exportTaskRepository.save(queryExportTask);
            }
        });
    }

    @Override
    public Page<EvalSearchApiExportTaskEntity> taskPage(Pageable pageable) {
        return exportTaskRepository.page(pageable);
    }

    @Override
    public void taskDelete(List<String> recordIds) {
        log.debug("taskDelete recordIds: {}", recordIds);
        // 1. 查询导出记录,获取关联文件id
        List<EvalSearchApiExportTaskEntity> evalSearchApiExportTaskEntityList = exportTaskRepository.findByIds(recordIds);
        List<String> fileIds = evalSearchApiExportTaskEntityList.stream().map(EvalSearchApiExportTaskEntity::getFileId).toList();

        try {
            // 2. 删除文件
            gridFsTemplate.delete(new Query(Criteria.where("_id").in(fileIds)));
            // 3. 删除导出记录
            exportTaskRepository.removeByIds(recordIds);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LynxiaoException("删除记录失败!");
        }
    }

    @Override
    public List<TraceRecordDTO> trace(String region, String traceId) {

        ProcTraceFetcherParam param = new ProcTraceFetcherParam();
        param.setHistoryTraceId(traceId);
        param.setRegionCode(region);

        //查es 获取processId 获取workflowProcess
        String processId = this.procTraceFetcher4TraceId.fetchWorkflowProcessId(param);
        param.setWorkflowProcess(this.workflowProcessService.getWorkflowProcess(processId, false));

        ProcTraceFetcherResult process = this.procTraceFetcher4TraceId.process(param);
        if (null == process || CollectionUtil.isEmpty(process.getTraceLogItems())) {
            return new ArrayList<>();
        }
        //将全链路调用轨迹转换为前端需要的格式
        return TraceLogUtils.convertTraceLog2TraceRecordDTO(process.getTraceLogItems());
    }

    @Override
    public List<IntentDictItem> intentDict() {
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, "hf");
        ApiResponse chatMarkDict = metaDictFeign.getListByCode("search-api-intent");
        return convertDict(chatMarkDict.getPayload().getList("data", MetaDictDTO.class));
    }

    // 解析从ES中拿到的数据, 转换成前端需要展示的数据
    private SearchApiResultDTO transfer(JSONObject source,
                                        Map<String, IntentDictItem> dictItemMap,
                                        Map<String, GeneratedApplication> applicationMap,
                                        Map<String, FlowVersionDTO> flowVersionMap,
                                        String region) {
        SearchApiResultDTO dto = new SearchApiResultDTO();

        dto.setTraceId(source.getString("traceId"));
        dto.setCreateTime(source.getString("@timestamp"));

        BeanUtil.copyProperties(source, dto);
        if (StringUtils.isNotBlank(dto.getIntent())) {
            dto.setIntent(dto.getIntent() + "、" + (dictItemMap.get(dto.getIntent()) == null ? "-" : dictItemMap.get(dto.getIntent()).getDictName()));
        }
        if (StringUtils.isNotBlank(dto.getAppId())) {
            dto.setAppType(applicationMap.get(dto.getAppId()) == null ? "-" : applicationMap.get(dto.getAppId()).getType());
        }
        if (StringUtils.isNotBlank(source.getString("prodCode"))) {
            dto.setProdCode(source.getString("prodCode"));
        }
        if (StringUtils.isNotBlank(source.getString("prodCode"))) {
            dto.setProdCode(source.getString("prodCode"));
        }
        if (StringUtils.isNotBlank(source.getString("pvid"))) {
            FlowVersionDTO flowVersionDTO = flowVersionMap.get(source.getString("pvid"));
            if (null != flowVersionDTO) {
                dto.setProductVersion(flowVersionDTO.getName());
            }
        }
        //医疗定制埋点数据
        if (StringUtils.isNotBlank(source.getString("qid"))) {
            dto.setQid(source.getString("qid"));
        }
        if (StringUtils.isNotBlank(source.getString("rawQuery"))) {
            dto.setRawQuery(source.getString("rawQuery"));
        }
        return dto;
    }


    private String storeFileToGridFs(List<SearchApiResultDTO> excelDataList) {
        String excelFileName = "searchApi_result_export_" + System.currentTimeMillis() + ".xlsx";

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 使用 EasyExcel 写入数据到 outputStream
            EasyExcel.write(outputStream, SearchApiResultDTO.class)
                    .sheet("Export Data")
                    .doWrite(excelDataList);

            if (outputStream.size() == 0) {
                throw new RuntimeException("outputStream is empty");
            }
            byte[] bytes = outputStream.toByteArray();
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
                // 将 inputStream 存储到 GridFS
                return this.gridFsTemplate.store(inputStream, excelFileName,
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet").toString();
            }
        } catch (Exception e) {
            log.error("Error storing file to GridFS", e);
            throw new RuntimeException("Error storing file to GridFS", e);
        }
    }


    private List<IntentDictItem> convertDict(List<MetaDictDTO> metaDicts) {
        List<IntentDictItem> result = new ArrayList<>();
        for (MetaDictDTO dictDTO : metaDicts) {
            IntentDictItem dictItem = new IntentDictItem();
            dictItem.setDictCode(dictDTO.getCode());
            dictItem.setDictName(dictDTO.getName());
            result.add(dictItem);
        }
        return result;
    }
}
