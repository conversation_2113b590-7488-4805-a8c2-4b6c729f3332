#------------------------------------------------------------------------------------------------------
# Lynxiao Feature Configuration
#------------------------------------------------------------------------------------------------------
lynxiao.asset.action.doc.index-dedup.enabled=true
#------------------------------------------------------------------------------------------------------
# Endpoint Configuration
skynet.pandora.mq.endpoint.index-dedup.enabled=true
skynet.pandora.mq.endpoint.index-dedup.handler-size=32
skynet.pandora.mq.endpoint.index-dedup.handle-timeout=30m
# Consumer Topics Configuration
skynet.pandora.mq.consumer-topics.lynxiao-asset-index-dedup.enabled=${lynxiao.asset.action.doc.index-dedup.enabled}
skynet.pandora.mq.consumer-topics.lynxiao-asset-index-dedup.consumer-size=4
skynet.pandora.mq.consumer-topics.lynxiao-asset-index-dedup.handle-endpoint=index-dedup
skynet.pandora.mq.consumer-topics.lynxiao-asset-index-dedup.group=${SKYNET_PANDORA_MQ_CONSUMER_GROUP}

lynxiao.mongo.enabled=true
lynxiao.platform-mongo.enabled=true
lynxiao.platform-mongo.uri=mongodb+srv://${lynxiao.platform.mongodb.user}:${lynxiao.platform.mongodb.password}@${lynxiao.platform.mongodb.host}/lynxiao_platform?authSource=admin&tls=false&ssl=false
spring.data.mongodb.uri=${lynxiao.platform-mongo.uri}