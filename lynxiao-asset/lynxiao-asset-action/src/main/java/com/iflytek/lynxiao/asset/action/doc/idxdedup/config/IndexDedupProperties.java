package com.iflytek.lynxiao.asset.action.doc.idxdedup.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;

/**
 * 索引去重配置属性
 * 
 * <AUTHOR>
 * @date 2025/09/02
 */
@Data
public class IndexDedupProperties {

    /**
     * 是否启用索引去重功能
     */
    private boolean enabled = false;

    /**
     * 任务配置
     */
    private TaskConfig task = new TaskConfig();

    /**
     * Elasticsearch配置
     */
    private ElasticsearchConfig esConfig = new ElasticsearchConfig();

    /**
     * Elasticsearch相关配置
     */
    @Getter
    @Setter
    public static class ElasticsearchConfig {

        private String vectorField = "";

        /**
         * Elasticsearch密码解密密钥
         * <p>用于解密存储在配置中的ES密码</p>
         */
        private String secretKey = "VISpQS+qpIaNtUc7oC8XFN6g59F6RIkV";

        /**
         * Elasticsearch查询超时时间（毫秒）
         * <p>建议值：3000-10000ms，根据网络延迟调整</p>
         */
        private Integer timeout = 4000;
    }

    /**
     * 线程池配置
     */
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();

    @Data
    public static class TaskConfig {
        /**
         * 批处理大小
         */
        private int batchSize = 100;

        /**
         * 分区数量
         */
        private int partition = 16;

        /**
         * 任务超时时间
         */
        private Duration taskTimeout = Duration.ofHours(4);
    }

    @Data
    public static class ThreadPoolConfig {
        /**
         * 核心线程数
         */
        private int corePoolSize = 1;

        /**
         * 最大线程数
         */
        private int maxPoolSize = 1;

        /**
         * 队列大小
         */
        private int queueCapacity = 100;

        /**
         * 线程空闲时间（秒）
         */
        private long keepAliveSeconds = 60L;

        /**
         * 线程名称前缀
         */
        private String threadNamePrefix = "index-dedup-handler";
    }
}