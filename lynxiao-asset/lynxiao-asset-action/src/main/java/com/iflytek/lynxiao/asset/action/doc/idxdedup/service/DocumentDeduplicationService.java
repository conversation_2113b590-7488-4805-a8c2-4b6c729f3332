package com.iflytek.lynxiao.asset.action.doc.idxdedup.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.constants.IndexDedupConstants;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.model.DocumentProcessingContext;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import skynet.boot.mongo.DocumentCompressor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文档去重处理服务实现
 * 负责单个文档的去重逻辑处理，包括向量相似度搜索、重复文档识别和状态更新
 * 
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
public class DocumentDeduplicationService {
    
    private final EsService esService;
    private final DocumentCompressor documentCompressor;
    private final MongoTemplate platformMongoTemplate;
    
    public DocumentDeduplicationService(EsService esService, MongoTemplate platformMongoTemplate) {
        this.esService = esService;
        this.platformMongoTemplate = platformMongoTemplate;
        this.documentCompressor = new DocumentCompressor();
    }
    
    public int processDocumentDeduplication(Document document,
                                           DocumentProcessingContext context,
                                           FeatureBaseDetailGetDTO featureBase) {
        final Long docId = document.getLong(IndexDedupConstants.ID);
        
        try {
            log.debug("{} Starting document deduplication processing: docId={}, bucketCode={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, docId, context.getBucketCode());
            
            // 1. Validate document
            if (!isDocumentValidForProcessing(document)) {
                log.debug("{} Document invalid for processing, skipping: docId={}", 
                        IndexDedupConstants.LogTags.INDEX_DEDUP, docId);
                return 0;
            }
            
            // 2. Decompress document and extract vectors
            JSONObject decompressedDoc = JSONObject.from(documentCompressor.decompress(document));
            JSONArray embeddings = decompressedDoc.getJSONArray(IndexDedupConstants.EMBS);
            
            if (embeddings == null || embeddings.isEmpty()) {
                log.trace("{} Document has no embedding data, skipping: docId={}", 
                        IndexDedupConstants.LogTags.INDEX_DEDUP, docId);
                return 0;
            }
            
            // 3. Process each vector for similarity search
            return processDocumentEmbeddings(document, embeddings, context, featureBase);
            
        } catch (Exception e) {
            log.error("{} Document deduplication processing failed: docId={}, bucketCode={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, docId, context.getBucketCode(), e);
            throw new LynxiaoException("Document deduplication processing failed for docId: " + docId, e);
        }
    }

    public boolean isDocumentValidForProcessing(Document document) {
        if (document == null) {
            log.debug("{} Document is null, skipping processing", IndexDedupConstants.LogTags.INDEX_DEDUP);
            return false;
        }
        
        final Long docId = document.getLong(IndexDedupConstants.ID);
        if (docId == null) {
            log.debug("{} Document ID is null, skipping processing", IndexDedupConstants.LogTags.INDEX_DEDUP);
            return false;
        }
        
        return true;
    }
    
    /**
     * 处理文档的所有嵌入向量
     */
    private int processDocumentEmbeddings(Document document, 
                                         JSONArray embeddings,
                                         DocumentProcessingContext context,
                                         FeatureBaseDetailGetDTO featureBase) {
        final Long docId = document.getLong(IndexDedupConstants.ID);
        int maxSimilarDocsCount = 0;
        
        for (int i = 0; i < embeddings.size(); i++) {
            try {
                JSONObject embeddingObj = embeddings.getJSONObject(i);
                List<Float> embeddingVector = embeddingObj.getList(IndexDedupConstants.EMB, Float.class);
                
                if (embeddingVector == null || embeddingVector.isEmpty()) {
                    log.debug("{} Embedding vector is null or empty, skipping: docId={}, embeddingIndex={}", 
                            IndexDedupConstants.LogTags.INDEX_DEDUP, docId, i);
                    continue;
                }
                
                // Execute similarity search
                List<JSONObject> similarDocs = esService.findSimilarityDoc(
                        embeddingVector, featureBase, context.getRecallConfig());
                
                // Process similar document group
                if (!similarDocs.isEmpty()) {
                    // Process similar documents and get sorted IDs
                    List<JSONObject> allDocs = buildCompleteDocs(similarDocs, docId, document.getInteger(IndexDedupConstants.LEN));
                    List<Long> sortedIds = sortSimilarDocs(allDocs);

                    maxSimilarDocsCount = Math.max(sortedIds.size(), maxSimilarDocsCount);
                    
                    if (sortedIds.size() > 1) {
                        // Found duplicates, process them
                        Long usefulDocId = sortedIds.getFirst();
                        List<Long> duplicateIds = sortedIds.subList(1, sortedIds.size());
                        
                        try {
                            // Log trace if enabled
                            logDeduplicationTrace(document, context, usefulDocId, similarDocs);

                            // Update Elasticsearch document status
                            esService.bulkUpdateDocByIds(featureBase, featureBase.getEsAlias(), usefulDocId, duplicateIds);
                            
                            // Update MongoDB document flags  
                            updateDocumentFlags(context, usefulDocId, duplicateIds);
                            
                            log.debug("{} Similar document group processing completed: usefulId={}, processedDuplicates={}", 
                                    IndexDedupConstants.LogTags.INDEX_DEDUP, usefulDocId, duplicateIds.size());

                        } catch (IOException e) {
                            log.error("{} Failed to update Elasticsearch document status: usefulId={}, duplicateIds={}",
                                    IndexDedupConstants.LogTags.ES_SERVICE, usefulDocId, duplicateIds, e);
                            throw new LynxiaoException("Failed to update Elasticsearch document status", e);
                        } catch (Exception e) {
                            log.error("{} Failed to process similar document group: usefulId={}, duplicateIds={}",
                                    IndexDedupConstants.LogTags.INDEX_DEDUP, usefulDocId, duplicateIds, e);
                            throw new LynxiaoException("Failed to process similar document group", e);
                        }
                    }
                }
                
            } catch (Exception e) {
                log.warn("{} Error processing embedding vector: docId={}, embeddingIndex={}", 
                        IndexDedupConstants.LogTags.INDEX_DEDUP, docId, i, e);
                // Continue processing other vectors
            }
        }
        
        return maxSimilarDocsCount;
    }
    
    /**
     * 构建包含当前文档在内的完整文档组
     */
    private List<JSONObject> buildCompleteDocs(List<JSONObject> filteredSimilarDocs,
                                               Long currentDocId,
                                               Integer currentDocLen) {
        List<JSONObject> allDocs = new ArrayList<>(filteredSimilarDocs);
        
        JSONObject currentDocJson = new JSONObject();
        currentDocJson.put("id", currentDocId);
        currentDocJson.put(IndexDedupConstants.LEN, currentDocLen);
        allDocs.add(currentDocJson);
        
        return allDocs;
    }
    
    /**
     * 分析文档组，找出最优文档和重复文档
     */
    private List<Long> sortSimilarDocs(List<JSONObject> allDocs) {
        // Build document length mapping, handle duplicate keys
        Map<Long, Integer> docLenMap = allDocs.stream()
                .collect(Collectors.toMap(
                        doc -> doc.getLong("id"),
                        doc -> doc.getInteger(IndexDedupConstants.LEN),
                        (existing, replacement) -> existing // Keep first value for duplicate keys
                ));
        
        // Sort by length descending, then by ID ascending

        return docLenMap.entrySet().stream()
                .sorted((e1, e2) -> {
                    int lenCompare = Integer.compare(e2.getValue(), e1.getValue()); // Length descending
                    if (lenCompare != 0) {
                        return lenCompare;
                    }
                    return Long.compare(e1.getKey(), e2.getKey()); // ID ascending
                })
                .map(Map.Entry::getKey)
                .toList();
    }
    
    /**
     * 记录去重追踪日志
     */
    private void logDeduplicationTrace(Document document, 
                                     DocumentProcessingContext context,
                                     Long usefulDocId,
                                     List<JSONObject> similarDocs) {
        if (!context.getRecallConfig().getLogTrace()) {
            return;
        }
        
        try {
            Document traceDoc = new Document();
            traceDoc.put("doc_id", document.getLong(IndexDedupConstants.ID));
            traceDoc.put("doc_len", document.getInteger(IndexDedupConstants.LEN));
            traceDoc.put("collection", context.getBucketCode());
            traceDoc.put("reverse_id", usefulDocId);
            traceDoc.put("ts", System.currentTimeMillis());
            traceDoc.put("similar_docs", similarDocs);
            
            platformMongoTemplate.insert(traceDoc, "index_dedup");
            
            log.debug("{} Deduplication trace logged: docId={}, usefulDocId={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, document.getLong(IndexDedupConstants.ID), usefulDocId);
                    
        } catch (Exception e) {
            log.warn("{} Failed to log deduplication trace: docId={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, document.getLong(IndexDedupConstants.ID), e);
        }
    }
    
    /**
     * 更新文档标记状态
     */
    private void updateDocumentFlags(DocumentProcessingContext context, 
                                   Long usefulDocId, 
                                   List<Long> duplicateDocIds) {
        if (usefulDocId == null) {
            throw new IllegalArgumentException("Useful document ID cannot be null");
        }
        
        if (duplicateDocIds == null || duplicateDocIds.isEmpty()) {
            log.debug("{} No duplicate documents to update: collection={}, usefulDocId={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), usefulDocId);
            return;
        }
        
        final long startTime = System.currentTimeMillis();
        final long timestamp = System.currentTimeMillis();
        
        try {
            // Use UNORDERED mode for better bulk operation performance
            BulkOperations bulkOps = context.getMongoTemplate()
                    .bulkOps(BulkOperations.BulkMode.UNORDERED, context.getBucketCode());
            
            // Bulk set useful document flag (retain)
            Query usefulQuery = new Query(Criteria.where(IndexDedupConstants.ID).is(usefulDocId));
            Update usefulUpdate = new Update()
                    .set(AssetCellProps.KEY_S, AssetCellProps.BoolValue.FALSE.getValue()) // Mark as retain
                    .set(AssetCellProps.KEY_UPDATE_TS, timestamp); // Add dedup processing timestamp
            bulkOps.updateOne(usefulQuery, usefulUpdate);
            
            // Bulk set duplicate document flags (delete)
            Query duplicateQuery = new Query(Criteria.where(IndexDedupConstants.ID).in(duplicateDocIds));
            Update duplicateUpdate = new Update()
                    .set(AssetCellProps.KEY_S, AssetCellProps.BoolValue.TRUE.getValue()) // Mark as delete
                    .set(AssetCellProps.KEY_UPDATE_TS, timestamp); // Record corresponding useful document ID
            bulkOps.updateMulti(duplicateQuery, duplicateUpdate);
            
            log.debug("{} Preparing bulk document flags update: collection={}, usefulId={}, duplicateCount={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), usefulDocId, duplicateDocIds.size());
            
            // Execute bulk operation
            var result = bulkOps.execute();
            
            long executionTime = System.currentTimeMillis() - startTime;
            log.info("{} Document flags bulk update completed: collection={}, usefulId={}, duplicateCount={}, " +
                            "modifiedCount={}, matchedCount={}, executionTime={}ms", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), usefulDocId, duplicateDocIds.size(),
                    result.getModifiedCount(), result.getMatchedCount(), executionTime);
            
            // Validate update result
            long expectedMatches = duplicateDocIds.size() + 1; // Duplicate documents + useful document
            if (result.getMatchedCount() != expectedMatches) {
                log.warn("{} Some documents were not matched: collection={}, expected={}, matched={}", 
                        IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), expectedMatches, result.getMatchedCount());
            }
            
        } catch (Exception e) {
            log.error("{} Failed to update document flags: collection={}, usefulId={}, duplicateCount={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), usefulDocId, duplicateDocIds.size(), e);
            throw new LynxiaoException("Failed to update document flags for collection: " + context.getBucketCode(), e);
        }
    }
}