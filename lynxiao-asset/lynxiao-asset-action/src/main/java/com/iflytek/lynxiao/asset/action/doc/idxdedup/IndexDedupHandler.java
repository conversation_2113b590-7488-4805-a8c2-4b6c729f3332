package com.iflytek.lynxiao.asset.action.doc.idxdedup;

import com.alibaba.fastjson2.JSON;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.condition.ConditionalOnIndexDedup;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.service.IndexDedupTaskExecutor;
import com.iflytek.lynxiao.asset.sender.MqMessageCtrlSender;
import com.iflytek.lynxiao.asset.util.HeaderParser;
import com.iflytek.lynxiao.data.constant.DedupType;
import com.iflytek.lynxiao.data.dto.action.dedup.BucketIdxDedupMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import skynet.boot.pandora.MqServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraMqHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.PandoraConsts;
import skynet.boot.pandora.exception.PandoraException;
import skynet.boot.pandora.support.MqSessionContext;

import java.util.concurrent.CompletableFuture;

/**
 * 索引去重处理器 - MQ消息处理入口
 * <p>
 * 职责：
 * <ul>
 *   <li>接收并验证MQ消息</li>
 *   <li>委托任务执行器处理具体业务逻辑</li>
 *   <li>统一异常处理和错误通知</li>
 *   <li>资源生命周期管理</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * <AUTHOR> optimized
 * @version 2.0
 * @since 2025/09/03
 */
@Slf4j
@ConditionalOnIndexDedup
@SkynetPandoraMqHandler
public class IndexDedupHandler implements MqServiceHandler {

    private final IndexDedupTaskExecutor indexDedupTaskExecutor;
    private final MqMessageCtrlSender mqMessageCtrlSender;

    @Autowired
    public IndexDedupHandler(IndexDedupTaskExecutor indexDedupTaskExecutor, MqMessageCtrlSender mqMessageCtrlSender) {
        this.indexDedupTaskExecutor = indexDedupTaskExecutor;
        this.mqMessageCtrlSender = mqMessageCtrlSender;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        // 索引去重不支持同步处理模式
        return null;
    }

    @Override
    public ApiResponse process(MqSessionContext sessionContext, ApiRequest apiRequest) throws PandoraException {
        final String taskId = HeaderParser.get(PandoraConsts.PANDORA_TASK_ID);
        final long requestStartTime = System.currentTimeMillis();
        
        try {
            // 解析并验证请求
            BucketIdxDedupMessage indexDedupMessage = parseAndValidateRequest(taskId, apiRequest);
            
            // 异步提交任务执行
            submitAsyncTask(taskId, indexDedupMessage, requestStartTime);
            
            log.info("[IndexDedup] Task submitted successfully. taskId={}, bucketCode={}, submissionTime={}ms", 
                    taskId, indexDedupMessage.getBucketCode(), 
                    System.currentTimeMillis() - requestStartTime);
            
            return null; // 异步处理，立即返回
            
        } catch (IllegalArgumentException e) {
            handleValidationError(taskId, e, requestStartTime);
            return null;
        } catch (Exception e) {
            handleUnexpectedError(taskId, e, requestStartTime);
            return null;
        }
    }

    /**
     * 解析并验证请求参数
     */
    private BucketIdxDedupMessage parseAndValidateRequest(String taskId, ApiRequest apiRequest) {
        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("Task ID cannot be empty");
        }
        
        if (apiRequest == null || apiRequest.getPayload() == null) {
            throw new IllegalArgumentException("Request payload cannot be null");
        }

        BucketIdxDedupMessage message;
        try {
            message = JSON.to(BucketIdxDedupMessage.class, apiRequest.getPayload());
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to parse request payload: " + e.getMessage(), e);
        }
        
        if (message == null) {
            throw new IllegalArgumentException("Parsed message data is null");
        }
        
        if (StringUtils.isBlank(message.getBucketCode())) {
            throw new IllegalArgumentException("Bucket code cannot be empty");
        }
        
        if (message.getRecallConfig() == null) {
            log.warn("[IndexDedup] No recall config provided, using default. taskId={}", taskId);
            message.setRecallConfig(new BucketIdxDedupMessage.RecallConfig());
        }
        
        return message;
    }
    
    /**
     * 提交异步任务执行
     */
    private void submitAsyncTask(String taskId, BucketIdxDedupMessage message, long requestStartTime) {
        CompletableFuture.runAsync(() -> {
            final long executionStartTime = System.currentTimeMillis();
            final long queueWaitTime = executionStartTime - requestStartTime;
            
            try {
                log.info("[IndexDedup] Starting task execution. taskId={}, bucketCode={}, queueWaitTime={}ms", 
                        taskId, message.getBucketCode(), queueWaitTime);
                
                indexDedupTaskExecutor.execute(taskId, message);
                
                final long totalTime = System.currentTimeMillis() - requestStartTime;
                final long executionTime = System.currentTimeMillis() - executionStartTime;
                
                log.info("[IndexDedup] Task execution completed. taskId={}, executionTime={}ms, totalTime={}ms", 
                        taskId, executionTime, totalTime);
                        
            } catch (Exception e) {
                final long errorTime = System.currentTimeMillis() - executionStartTime;
                log.error("[IndexDedup] Task execution failed. taskId={}, executionTime={}ms", 
                        taskId, errorTime, e);
                notifyTaskFailure(taskId, "Task execution failed: " + e.getMessage());
            }
        }, indexDedupTaskExecutor.getAsyncExecutor())
        .exceptionally(throwable -> {
            log.error("[IndexDedup] Async task execution failed unexpectedly. taskId={}", taskId, throwable);
            notifyTaskFailure(taskId, "Async execution error: " + throwable.getMessage());
            return null;
        });
    }
    
    /**
     * 处理验证错误
     */
    private void handleValidationError(String taskId, IllegalArgumentException e, long startTime) {
        final long processingTime = System.currentTimeMillis() - startTime;
        log.error("[IndexDedup] Request validation failed. taskId={}, processingTime={}ms, error: {}", 
                taskId, processingTime, e.getMessage());
        notifyTaskFailure(taskId, "Validation error: " + e.getMessage());
    }
    
    /**
     * 处理意外错误
     */
    private void handleUnexpectedError(String taskId, Exception e, long startTime) {
        final long processingTime = System.currentTimeMillis() - startTime;
        log.error("[IndexDedup] Unexpected error occurred. taskId={}, processingTime={}ms", 
                taskId, processingTime, e);
        notifyTaskFailure(taskId, "Internal server error: " + e.getMessage());
    }

    /**
     * 发送任务失败通知
     */
    private void notifyTaskFailure(String taskId, String errorMessage) {
        try {
            // 索引去重使用STEP_ONE阶段的失败通知
            mqMessageCtrlSender.sendFailMsg(DedupType.STEP_ONE, taskId, 0L, errorMessage);
            log.info("[IndexDedup] Failure notification sent. taskId={}", taskId);
        } catch (Exception ex) {
            log.error("[IndexDedup] Failed to send failure notification. taskId={}, originalError={}", 
                    taskId, errorMessage, ex);
        }
    }

    @Override
    public void close() throws Exception {
        log.info("[IndexDedup] Starting handler shutdown...");
        final long shutdownStartTime = System.currentTimeMillis();
        
        try {
            // 优雅关闭任务执行器
            if (indexDedupTaskExecutor != null) {
                log.info("[IndexDedup] Closing task executor...");
                indexDedupTaskExecutor.close();
                log.info("[IndexDedup] Task executor closed successfully");
            }
            
            final long shutdownTime = System.currentTimeMillis() - shutdownStartTime;
            log.info("[IndexDedup] Handler shutdown completed successfully. shutdownTime={}ms", shutdownTime);
            
        } catch (Exception e) {
            final long shutdownTime = System.currentTimeMillis() - shutdownStartTime;
            log.error("[IndexDedup] Error during handler shutdown. shutdownTime={}ms", shutdownTime, e);
            throw e;
        }
    }
}