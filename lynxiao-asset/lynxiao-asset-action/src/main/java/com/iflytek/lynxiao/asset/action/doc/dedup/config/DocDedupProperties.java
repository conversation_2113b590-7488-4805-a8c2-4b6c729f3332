package com.iflytek.lynxiao.asset.action.doc.dedup.config;

import com.iflytek.lynxiao.data.constant.ScriptType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;
import skynet.boot.common.domain.Jsonable;

import java.time.Duration;

/**
 * 去重配置属性 - 优化版本
 * 增加了配置验证、默认值处理和配置检查功能
 *
 * <AUTHOR> by architect
 */
@Slf4j
@Getter
@Setter
@Validated
public class DocDedupProperties extends Jsonable {

    /**
     * 是否启用去重功能
     */
    private boolean enabled = false;

    /**
     * 任务配置
     */
    private DedupTaskConfig taskConfig = new DedupTaskConfig();

    /**
     * 文档处理配置
     */
    private DedupDocConfig docConfig = new DedupDocConfig();

    /**
     * Portal服务URI
     */
    private String portalFeignUri = "http://tlb:lynxiao-asset-portal";

    /**
     * 任务控制消息队列
     */
    private String taskCtrlMq = "mq://lynxiao_asset_task_ctl/sample";

    /**
     * 去重任务配置 - 优化版本
     */
    @Getter
    @Setter
    @Validated
    public static class DedupTaskConfig {

        /**
         * 每个文档集合的分区数量
         * 建议根据数据量和服务器性能调整，通常设置为CPU核心数的2-4倍
         */
        private int partition = 20;

        /**
         * 分页查询大小
         * 建议根据内存大小和网络延迟调整
         */
        private int pageSize = 1000;

        /**
         * 查询文档超时时间
         */
        private Duration queryDocTimeout = Duration.ofHours(2);

        /**
         * 异步管道队列大小
         */
        private int pipelineQueueSize = 10000;

        /**
         * 异步管道写入流量限制
         */
        private int pipelineWriteLimit = 1000;

        /**
         * 异步管道写入触发限流后的等待时间
         */
        private Duration pipelineWriteWait = Duration.ofMillis(1000);

        /**
         * 布隆过滤器预期插入数量
         * 默认1.2亿，根据实际数据量调整
         */
        private int expectedInsertions = 12000 * 10000;

        /**
         * 布隆过滤器假阳性率
         * 值越小，内存占用越大，但准确性越高
         */
        private double falsePositiveProbability = 0.001;

        /**
         * GID缓存过期时间
         */
        private Duration gidTtl = Duration.ofHours(10);

        /**
         * GID缓存扫描批次大小
         */
        private int gidScanPageSize = 20;

        /**
         * 进度统计间隔周期
         */
        private Duration statisticsPeriod = Duration.ofMinutes(1);

        /**
         * 每个分片存储的GID个数
         * 用于计算分片数量：总数 / gidsPerShard = 分片数
         */
        private int gidsPerShard = 5000;

        private int maxShard = 5000;
    }


    /**
     * 去重文档处理配置 - 优化版本
     */
    @Getter
    @Setter
    @Validated
    public static class DedupDocConfig {

        /**
         * 去重脚本类型
         */
        private ScriptType scriptType = ScriptType.GROOVY;

        /**
         * 去重计算线程池大小
         * 建议根据CPU核心数和I/O密集程度调整
         */
        private Integer dedupPoolSize = 20;

        /**
         * 去重计算队列大小
         * 建议设置为线程池大小的10-20倍
         */
        private Integer dedupQueueSize = 200;

        /**
         * 去重计算超时时间
         */
        private Duration timeout = Duration.ofMinutes(5);
    }
}
