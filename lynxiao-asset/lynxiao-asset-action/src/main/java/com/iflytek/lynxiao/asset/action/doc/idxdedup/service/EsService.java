package com.iflytek.lynxiao.asset.action.doc.idxdedup.service;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.KnnQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.FieldAndFormat;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.bulk.UpdateOperation;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.InnerHits;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheStats;
import com.google.common.cache.RemovalListener;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.config.IndexDedupProperties;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.action.dedup.BucketIdxDedupMessage;
import com.iflytek.lynxiao.data.dto.featurebase.EsClusterDTO;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContextBuilder;
import org.elasticsearch.client.RestClient;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Elasticsearch服务类
 * <p>
 * 该服务负责与Elasticsearch集群的交互，提供向量相似度搜索、文档批量更新等功能。
 * 支持多集群管理、连接池优化、自动重试和性能监控。
 * </p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>向量相似度搜索</li>
 *   <li>批量文档状态更新</li>
 *   <li>ES客户端连接池管理</li>
 *   <li>自动重试和错误恢复</li>
 *   <li>性能监控和统计</li>
 * </ul>
 *
 * <p>使用示例：</p>
 * <pre>
 * // 执行向量相似度搜索
 * List&lt;JSONObject&gt; results = esService.findSimilarityDoc(embeddings, featureBase);
 *
 * // 批量更新文档状态
 * esService.bulkUpdateDocByIds(featureBase, alias, usefulId, duplicateIds);
 * </pre>
 *
 * <AUTHOR>
 * <AUTHOR> by architect
 * @version 2.0
 * @since 2025/08/12
 */
@Slf4j
public class EsService implements AutoCloseable {

    /**
     * ES客户端缓存，按集群ID缓存
     */
    private final Cache<Long, ElasticsearchClient> clientCache;

    /**
     * 配置属性
     */
    private final IndexDedupProperties properties;

    /**
     * 向量字段路径常量
     */
    private static final String DEFAULT_FIELD = "embeddings.embedding";

    /**
     * 性能统计
     */
    private final AtomicLong totalSearchRequests = new AtomicLong(0);
    private final AtomicLong totalUpdateRequests = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);

    /**
     * 构造函数
     *
     * @param properties 配置属性
     */
    public EsService(IndexDedupProperties properties) {
        this.properties = properties;

        // 创建客户端缓存，优化连接管理
        this.clientCache = CacheBuilder.newBuilder()
                .maximumSize(100) // 限制缓存大小
                .expireAfterAccess(10, TimeUnit.HOURS) // 2小时未访问则过期
                .removalListener(this.handleClientRemoval())
                .recordStats() // 启用统计
                .build();

        log.info("Elasticsearch服务初始化完成: maxCacheSize=10, expireAfterAccess=2h");
    }

    /**
     * 执行向量相似度搜索
     * <p>
     * 根据输入的向量在Elasticsearch中搜索相似的文档，支持多种相似度算法。
     * </p>
     *
     * @param embeddings  查询向量
     * @param featureBase 特征库配置
     * @return 相似文档列表
     * @throws RuntimeException 当搜索失败时
     */
    public List<JSONObject> findSimilarityDoc(List<Float> embeddings, FeatureBaseDetailGetDTO featureBase, BucketIdxDedupMessage.RecallConfig recallConfig) {
        if (embeddings == null || embeddings.isEmpty()) {
            log.debug("查询向量为空，返回空结果");
            return new ArrayList<>();
        }

        if (featureBase == null || featureBase.getEsClusters() == null) {
            log.warn("特征库配置无效，返回空结果");
            return new ArrayList<>();
        }

        final long startTime = System.currentTimeMillis();
        totalSearchRequests.incrementAndGet();

        try {
            // 获取ES客户端
            ElasticsearchClient client = getElasticsearchClient(featureBase.getEsClusters().getFull());

            // 获取相似度算法配置
            String similarity = extractSimilarityAlgorithm(featureBase);

            // 构建搜索请求
            SearchRequest searchRequest = buildSimilaritySearchRequest(embeddings, similarity, featureBase, recallConfig);

            // 执行搜索
            SearchResponse<JSONObject> response = executeSearchWithRetry(client, searchRequest);

            // 处理搜索结果
            List<JSONObject> results = processSimilaritySearchResults(response);

            long executionTime = System.currentTimeMillis() - startTime;
            log.debug("向量相似度搜索完成: index={}, vectorSize={}, resultCount={}, executionTime={}ms",
                    featureBase.getEsAlias(), embeddings.size(), results.size(), executionTime);

            return results;
        } catch (Exception e) {
            totalErrors.incrementAndGet();
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("向量相似度搜索失败: index={}, vectorSize={}, executionTime={}ms",
                    featureBase.getEsAlias(), embeddings.size(), executionTime, e);
            throw new RuntimeException("向量相似度搜索失败: " + e.getMessage(), e);
        }
    }


    private SearchRequest.Builder buildSearchQuery(List<Float> embeddings, String similarity, BucketIdxDedupMessage.RecallConfig recallConfig) {
        final String field = StringUtils.defaultIfBlank(properties.getEsConfig().getVectorField(), DEFAULT_FIELD);
        final boolean isNested = field.contains(".");

        // 构建基础KNN查询
        final Query knnQuery = buildKnnQuery(field, embeddings, recallConfig);

        // 构建脚本评分查询
        final Query scriptScoreQuery = buildScriptScoreQuery(knnQuery, field, similarity, embeddings);

        final Query matchQuery = isNested ? buildNestedQuery(field, scriptScoreQuery, recallConfig) : buildNonNestedQuery(scriptScoreQuery);
        // 构建KNN查询请求
        return new SearchRequest.Builder()
                .query(q -> q.bool(
                        b -> b
                                .mustNot(List.of(
                                        Query.of(qq -> qq.term(t -> t.field("s").value("1"))),
                                        Query.of(qq -> qq.term(t -> t.field("_f").value("1")))
                                )) // 添加过滤条件
                                .must(matchQuery)
                ));
    }

    private Query buildNonNestedQuery(Query contentQuery) {
        return Query.of(q -> q.scriptScore(
                s -> s.query(contentQuery)
        ));
    }

    private Query buildNestedQuery(String field, Query contentQuery, BucketIdxDedupMessage.RecallConfig recallConfig) {
        final String path = field.substring(0, field.lastIndexOf("."));
        return Query.of(q -> q.nested(n -> n
                .path(path)
                .innerHits(innerHits -> configureInnerHits(innerHits, recallConfig))
                .query(contentQuery)
        ));
    }

    private Query buildScriptScoreQuery(Query knnQuery, String field, String similarity, List<Float> embeddings) {
        return Query.of(q -> q.scriptScore(s -> s
                .query(knnQuery)
                .script(ss -> ss
                        .source(buildSimilarityScript(similarity, field))
                        .lang("painless")
                        .params("queryVector", JsonData.of(embeddings))
                )
        ));
    }

    private Query buildKnnQuery(String field, List<Float> embeddings, BucketIdxDedupMessage.RecallConfig recallConfig) {
        return Query.of(q -> q.knn(
                        KnnQuery.of(k -> k
                                .field(field)
                                .queryVector(embeddings)
                                .k(recallConfig.getTopK())
                                .numCandidates(recallConfig.getNumCandidates())
                        )
                )
        );
    }

    private String buildSimilarityScript(String similarity, String field) {
        return switch (similarity) {
            case "cosine" -> String.format("cosineSimilarity(params.queryVector, '%s')", field);
            case "dot_product" -> String.format("(1 + dotProduct(params.queryVector, '%s')) / 2", field);
            case "l2_norm" ->
                    String.format("double v = l2norm(params.queryVector, '%s');return 1 / (v * v + 1);", field);
            default -> "";
        };
    }

    /**
     * 配置inner_hits参数（用于获取匹配的嵌套文档）
     *
     * @param innerHitsBuilder inner_hits构建器
     */
    private InnerHits.Builder configureInnerHits(InnerHits.Builder innerHitsBuilder, BucketIdxDedupMessage.RecallConfig recallConfig) {
        innerHitsBuilder.size(recallConfig.getInnerHitSize()); // 设置每页返回数量

        // 配置字段投影：当指定输出字段时，使用docvalue_fields优化性能
        innerHitsBuilder.source(sourceConfig -> sourceConfig.fetch(false)); // 禁用_source检索
        List<String> outputFields = List.of();
        innerHitsBuilder.docvalueFields(buildDocValueFields(outputFields));
        return innerHitsBuilder;
    }

    /**
     * 构建文档值字段列表
     *
     * @param fields 字段名称列表
     * @return 字段格式配置列表
     */
    private List<FieldAndFormat> buildDocValueFields(List<String> fields) {

        return fields.stream()
                .map(field -> new FieldAndFormat.Builder().field(field).build())
                .collect(Collectors.toList());
    }

    public long bulkUpdateDocByIds(FeatureBaseDetailGetDTO featureBase, String alias, Long usefulId,
                                   List<Long> uselessIds) throws IOException {
        ElasticsearchClient client = this.getElasticsearchClient(featureBase.getEsClusters().getFull());
        List<BulkOperation> bulkOperations = new ArrayList<>();

        Map<String, Object> usefulUpdate = new HashMap<>();
        usefulUpdate.put(AssetCellProps.KEY_S, 0);
        usefulUpdate.put(AssetCellProps.KEY_UPDATE_TS, System.currentTimeMillis());
        UpdateOperation<Object, Object> usefulOperation =
                UpdateOperation.of(fn -> fn.id(usefulId.toString()).index(alias)
                        .action(action -> action.doc(usefulUpdate)));
        bulkOperations.add(BulkOperation.of(fn -> fn.update(usefulOperation)));

        for (Long id : uselessIds) {
            Map<String, Object> updates = new HashMap<>();
            updates.put(AssetCellProps.KEY_S, 1);
            updates.put(AssetCellProps.KEY_UPDATE_TS, System.currentTimeMillis());
            UpdateOperation<Object, Object> updateOperation = UpdateOperation.of(fn -> fn.id(id.toString()).index(alias)
                    .action(action -> action.doc(updates)));
            BulkOperation bulkOperation = BulkOperation.of(fn -> fn.update(updateOperation));
            bulkOperations.add(bulkOperation);
        }

        BulkRequest bulkRequest = BulkRequest.of(fn -> fn.operations(bulkOperations));
        BulkResponse bulkResponse = client.bulk(bulkRequest);
        if (bulkResponse.errors()) {
            log.error("ES Bulk update failed with errors，alias: {}, ids: {}", alias, uselessIds);
            throw new LynxiaoException("es批量更新失败！");
        }

        return bulkResponse.items().stream().filter(item -> Objects.isNull(item.error())).count();
    }

    private ElasticsearchClient buildClient(EsClusterDTO esCluster) {
        // 节点配置解析
        String protocol = Objects.nonNull(esCluster.getUseSsl()) && esCluster.getUseSsl() ? "https" : "http";
        String[] hosts = esCluster.getHosts().split(",");
        HttpHost[] httpHosts = new HttpHost[hosts.length];
        Arrays.stream(hosts).map(host -> HttpHost.create(String.format("%s://%s", protocol, host)))
                .toList().toArray(httpHosts);

        // 凭据生成
        String password = skynet.boot.security.CryptoUtil.decrypt(esCluster.getSecret(),
                properties.getEsConfig().getSecretKey());
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esCluster.getUser(),
                password));

        // 构建RestClient
        RestClient restClient = RestClient.builder(httpHosts)
                .setHttpClientConfigCallback(httpClientBuilder -> {
                    // 创建信任所有证书的 SSLContext
                    try {
                        SSLContext sslContext = SSLContextBuilder.create()
                                .loadTrustMaterial((chain, authType) -> true) // 信任所有证书
                                .build();
                        // 设置 Basic Authentication,禁用ssl验证
                        return httpClientBuilder.setSSLContext(sslContext)
                                .setDefaultCredentialsProvider(credentialsProvider)
                                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).build();

        // 构建传输层
        ElasticsearchTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper());

        // 构建客户端
        return new ElasticsearchClient(transport);
    }

    /**
     * 创建客户端移除监听器
     * <p>
     * 当客户端从缓存中移除时，确保正确关闭连接资源。
     * </p>
     */
    private RemovalListener<Long, ElasticsearchClient> handleClientRemoval() {
        return notification -> {
            try {
                ElasticsearchClient client = notification.getValue();
                if (client != null) {
                    log.debug("移除ES客户端: clusterId={}, cause={}",
                            notification.getKey(), notification.getCause());
                    // 注意：这里不直接关闭客户端，因为可能还有其他地方在使用
                    // 实际的关闭操作在服务关闭时统一处理
                }
            } catch (Exception e) {
                log.error("处理ES客户端移除事件失败: clusterId={}", notification.getKey(), e);
            }
        };
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取ES客户端（兼容性方法）
     */
    private ElasticsearchClient getElasticsearchClient(EsClusterDTO esCluster) {
        ElasticsearchClient ret;
        try {
            ret = this.clientCache.get(esCluster.getId(), () -> this.buildClient(esCluster));
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    /**
     * 提取相似度算法配置
     */
    private String extractSimilarityAlgorithm(FeatureBaseDetailGetDTO featureBase) {
        try {
            return featureBase.getFeatureBaseConfig()
                    .getVectorConfig()
                    .getFields()
                    .getLast()
                    .getSimilarity();
        } catch (Exception e) {
            log.warn("无法获取相似度算法配置，使用默认值: cosine", e);
            return "cosine";
        }
    }

    /**
     * 构建相似度搜索请求
     */
    private SearchRequest buildSimilaritySearchRequest(List<Float> embeddings,
                                                       String similarity,
                                                       FeatureBaseDetailGetDTO featureBase,
                                                       BucketIdxDedupMessage.RecallConfig recallConfig) {
        SearchRequest.Builder builder = buildSearchQuery(embeddings, similarity, recallConfig);

        // 配置返回字段
        builder.source(source -> {
            source.fetch(false);
            source.filter(f -> f.includes("id", "len"));
            return source;
        });

        // List<SortOptions> sortOptions = List.of(new SortOptions.Builder()
        //         .field(f -> f.field("_id").order(SortOrder.Asc))
        //         .build());
        // builder.sort(sortOptions);

        // 配置索引和大小
        builder.index(featureBase.getEsAlias())
                .size(recallConfig.getTopK())
                .minScore(recallConfig.getScoreLimit())
                .timeout(String.format("%dms", properties.getEsConfig().getTimeout()));

        return builder.build();
    }

    /**
     * 执行搜索并支持重试
     */
    private SearchResponse<JSONObject> executeSearchWithRetry(ElasticsearchClient client, SearchRequest request)
            throws IOException {
        int maxRetries = 3;
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return client.search(request, JSONObject.class);
            } catch (IOException e) {
                lastException = e;
                if (attempt < maxRetries) {
                    log.warn("搜索请求失败，准备重试: attempt={}/{}", attempt, maxRetries, e);
                    try {
                        Thread.sleep(100L * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("搜索被中断", ie);
                    }
                }
            }
        }

        throw new IOException("搜索请求在" + maxRetries + "次重试后仍然失败", lastException);
    }

    /**
     * 处理相似度搜索结果
     */
    private List<JSONObject> processSimilaritySearchResults(SearchResponse<JSONObject> response) {
        List<JSONObject> results = new ArrayList<>();

        if (response.hits() == null || response.hits().hits() == null) {
            return results;
        }

        for (Hit<JSONObject> hit : response.hits().hits()) {
            JSONObject source = Optional.ofNullable(hit.source())
                    .orElseGet(JSONObject::new);

            source.put("score", hit.score());

            // 添加文档ID
            if (hit.id() != null) {
                try {
                    source.put("id", Long.parseLong(hit.id()));
                } catch (NumberFormatException e) {
                    log.warn("无法解析文档ID: {}", hit.id());
                    continue;
                }
            }

            // 添加相似度分数
            if (hit.score() != null) {
                source.put("score", hit.score());
            }

            results.add(source);
        }

        return results;
    }

    /**
     * 获取服务统计信息
     */
    public EsServiceStatistics getStatistics() {
        return new EsServiceStatistics(
                totalSearchRequests.get(),
                totalUpdateRequests.get(),
                totalErrors.get(),
                clientCache.stats()
        );
    }

    /**
     * ES服务统计信息
     */
    public record EsServiceStatistics(
            long totalSearchRequests,
            long totalUpdateRequests,
            long totalErrors,
            CacheStats cacheStats
    ) {
    }

    @Override
    public void close() throws Exception {
        log.info("开始关闭Elasticsearch服务...");

        // 关闭所有缓存的客户端
        clientCache.asMap().forEach((clusterId, client) -> {
            try {
                client.close();
                log.debug("已关闭ES客户端: clusterId={}", clusterId);
            } catch (IOException e) {
                log.error("关闭ES客户端失败: clusterId={}", clusterId, e);
            }
        });

        // 清空缓存
        clientCache.invalidateAll();

        log.info("Elasticsearch服务已关闭，统计信息: {}", getStatistics());
    }
}
