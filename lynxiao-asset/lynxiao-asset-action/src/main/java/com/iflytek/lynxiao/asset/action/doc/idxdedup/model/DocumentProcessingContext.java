package com.iflytek.lynxiao.asset.action.doc.idxdedup.model;

import com.iflytek.lynxiao.data.dto.action.dedup.BucketIdxDedupMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 * 文档处理上下文
 * 封装文档处理过程中的所有必要信息
 * 
 * <AUTHOR>
 * @since 2025/09/03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentProcessingContext {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 存储桶代码
     */
    private String bucketCode;
    
    /**
     * 索引去重消息配置
     */
    private BucketIdxDedupMessage.RecallConfig recallConfig;
    
    /**
     * MongoDB模板
     */
    private MongoTemplate mongoTemplate;
    
    /**
     * 分区起始ID
     */
    private long startId;
    
    /**
     * 分区结束ID  
     */
    private long endId;
    
    /**
     * 分区索引
     */
    private int partitionIndex;
    
    /**
     * 当前处理的文档ID
     */
    private Long currentDocumentId;
    
    /**
     * 创建分区上下文
     */
    public static DocumentProcessingContext forPartition(String taskId, String bucketCode,
                                                         BucketIdxDedupMessage.RecallConfig recallConfig,
                                                        MongoTemplate mongoTemplate,
                                                        long startId, long endId, int partitionIndex) {
        return DocumentProcessingContext.builder()
                .taskId(taskId)
                .bucketCode(bucketCode)
                .recallConfig(recallConfig)
                .mongoTemplate(mongoTemplate)
                .startId(startId)
                .endId(endId)
                .partitionIndex(partitionIndex)
                .build();
    }
    
    /**
     * 创建文档处理上下文
     */
    public DocumentProcessingContext withCurrentDocument(Long documentId) {
        DocumentProcessingContext context = new DocumentProcessingContext();
        context.taskId = this.taskId;
        context.bucketCode = this.bucketCode;
        context.recallConfig = this.recallConfig;
        context.mongoTemplate = this.mongoTemplate;
        context.startId = this.startId;
        context.endId = this.endId;
        context.partitionIndex = this.partitionIndex;
        context.currentDocumentId = documentId;
        return context;
    }
}