package com.iflytek.lynxiao.asset.action.doc.idxdedup.config;

import com.iflytek.lynxiao.asset.action.doc.idxdedup.condition.ConditionalOnIndexDedup;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.service.DocumentDeduplicationService;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.service.DocumentQueryService;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.service.EsService;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.service.IndexDedupTaskExecutor;
import com.iflytek.lynxiao.asset.api.FeatureBaseApi;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.config.ActionProperties;
import com.iflytek.lynxiao.asset.sender.MqMessageCtrlSender;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.core.MongoTemplate;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.ogma.feign.OgmaFeignClientBuilder;
import skynet.boot.pandora.support.TaskCancelCache;

/**
 * <AUTHOR>
 * @date 2025/09/02
 */
@Configuration(proxyBeanMethods = false)
@ImportAutoConfiguration(exclude = DataSourceAutoConfiguration.class)
@ConditionalOnIndexDedup
@Import({
        org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration.class,
        org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration.class
})
public class IndexDedupConfiguration {

    @Bean
    @ConfigurationProperties("lynxiao.asset.action.doc.index-dedup")
    public IndexDedupProperties indexDedupProperties() {
        return new IndexDedupProperties();
    }

    @Bean
    public FeatureBaseApi featureBaseApi(OgmaFeignClientBuilder ogmaFeignClientBuilder, ActionProperties properties) {
        return new FeatureBaseApi(ogmaFeignClientBuilder, properties);
    }

    @Bean
    public EsService esService(IndexDedupProperties indexDedupProperties) {
        return new EsService(indexDedupProperties);
    }

    @Bean
    public MqMessageCtrlSender mqMessageCtrlSender(ActionProperties properties, PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder) {
        return new MqMessageCtrlSender(properties, pandoraApiRequestObserverBuilder);
    }
    
    @Bean
    public DocumentQueryService documentQueryService(BucketCacheService bucketCacheService) {
        return new DocumentQueryService(bucketCacheService);
    }
    
    @Bean
    public DocumentDeduplicationService documentDeduplicationService(EsService esService,
                                                                     @Qualifier("platformMongoTemplate") MongoTemplate platformMongoTemplate) {
        return new DocumentDeduplicationService(esService, platformMongoTemplate);
    }

    @Bean
    public IndexDedupTaskExecutor indexDedupTaskExecutor(BucketCacheService bucketCacheService,
                                                         FeatureBaseApi featureBaseApi,
                                                         MqMessageCtrlSender mqMessageCtrlSender,
                                                         IndexDedupProperties indexDedupProperties,
                                                         DocumentQueryService documentQueryService,
                                                         DocumentDeduplicationService documentDeduplicationService,
                                                         TaskCancelCache taskCancelCache) {
        return new IndexDedupTaskExecutor(bucketCacheService, featureBaseApi, mqMessageCtrlSender,
                indexDedupProperties, documentQueryService, documentDeduplicationService, taskCancelCache);
    }

}
