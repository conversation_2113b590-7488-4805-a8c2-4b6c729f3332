package com.iflytek.lynxiao.asset.action.doc.idxdedup;

import com.iflytek.lynxiao.asset.action.doc.idxdedup.condition.ConditionalOnIndexDedup;
import io.swagger.v3.oas.annotations.tags.Tag;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.pandora.MqServiceEndpoint;
import skynet.boot.pandora.annotation.SkynetPandoraMqEndpoint;

/**
 * <AUTHOR>
 * @date 2025/09/02
 */
@Tag(name = "索引去重组件")
@ExposeSwagger2
@ConditionalOnIndexDedup
@SkynetPandoraMqEndpoint(endpointName = "index-dedup", serviceHandler = IndexDedupHandler.class)
public class IndexDedupEndpoint implements MqServiceEndpoint {
}
