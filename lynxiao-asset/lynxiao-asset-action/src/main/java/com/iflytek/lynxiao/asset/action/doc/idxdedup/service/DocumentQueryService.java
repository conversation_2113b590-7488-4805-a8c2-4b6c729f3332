package com.iflytek.lynxiao.asset.action.doc.idxdedup.service;

import com.iflytek.lynxiao.asset.action.doc.idxdedup.constants.IndexDedupConstants;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.model.DocumentProcessingContext;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.domain.AssetOperationType;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * 文档查询服务实现
 * 负责从MongoDB查询文档数据，抽取重复查询逻辑
 * 
 * <AUTHOR>
 * @since 2025/09/03
 */
@Slf4j
public class DocumentQueryService {
    
    private final BucketCacheService bucketCacheService;
    
    public DocumentQueryService(BucketCacheService bucketCacheService) {
        this.bucketCacheService = bucketCacheService;
    }
    
    public long getEstimatedDocumentCount(String bucketCode) {
        try {
            long estimatedCount = bucketCacheService.getClient(bucketCode)
                    .getMongoTemplate().estimatedCount(bucketCode);
            log.debug("{} Estimated document count: bucketCode={}, count={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, estimatedCount);
            return estimatedCount;
        } catch (Exception e) {
            log.error("{} Failed to get estimated document count: bucketCode={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, e);
            throw new LynxiaoException("Failed to get estimated document count for bucket: " + bucketCode, e);
        }
    }
    
    public long getExactDocumentCount(String bucketCode) {
        try {
            MongoTemplate mongoTemplate = bucketCacheService.getClient(bucketCode).getMongoTemplate();
            Query query = buildBaseQuery();
            long exactCount = mongoTemplate.count(query, bucketCode);
            log.debug("{} Exact document count: bucketCode={}, count={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, exactCount);
            return exactCount;
        } catch (Exception e) {
            log.error("{} Failed to get exact document count: bucketCode={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, e);
            throw new LynxiaoException("Failed to get exact document count for bucket: " + bucketCode, e);
        }
    }
    
    public Document findMinMaxDocuments(String bucketCode, Sort sort) {
        try {
            MongoTemplate mongoTemplate = bucketCacheService.getClient(bucketCode).getMongoTemplate();
            Query query = new Query();
            
            Document doc = mongoTemplate.findOne(
                    query.with(sort),
                    Document.class, bucketCode);
                    
            if (doc != null) {
                log.debug("{} Found min/max documents: bucketCode={}, idd={}",
                        IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, doc.getLong(IndexDedupConstants.ID));
            }
                        
            return doc;
        } catch (Exception e) {
            log.error("{} Failed to find min/max documents: bucketCode={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, bucketCode, e);
            throw new LynxiaoException("Failed to find min/max documents for bucket: " + bucketCode, e);
        }
    }
    
    public List<Document> fetchDocumentBatch(DocumentProcessingContext context, long currentId, int batchSize) {
        try {
            Query query = buildBatchQuery(currentId, context.getEndId(), batchSize);
            
            List<Document> documents = context.getMongoTemplate().find(query, Document.class, context.getBucketCode());
            
            log.debug("{} Fetched document batch: collection={}, range=({}, {}], batchSize={}, actualSize={}",
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), 
                    currentId, context.getEndId(), batchSize, documents.size());
                    
            return documents;
        } catch (Exception e) {
            log.error("{} Failed to fetch document batch: collection={}, currentId={}, endId={}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, context.getBucketCode(), currentId, context.getEndId(), e);
            throw new LynxiaoException("Failed to fetch document batch for collection: " + context.getBucketCode(), e);
        }
    }
    
    /**
     * 构建基础查询条件
     * 抽取重复的查询条件逻辑
     */
    private Query buildBaseQuery() {
        Query query = new Query();
        query.addCriteria(
                Criteria.where(AssetCellProps.KEY_OP).ne(AssetOperationType.DELETE.getValue())
                        .and(AssetCellProps.KEY_A).is(AssetAuditStatus.PASS.getValue())
                        .and(AssetCellProps.KEY_S).ne(AssetCellProps.BoolValue.TRUE))
                .addCriteria(
                        new Criteria().orOperator(
                                Criteria.where(AssetCellProps.KEY_DBG).is(AssetCellProps.BoolValue.FALSE.getValue()),
                                Criteria.where(AssetCellProps.KEY_DBG).exists(false)
                        )
                );
        return query;
    }
    
    /**
     * 构建批次查询条件
     */
    private Query buildBatchQuery(long currentId, long endId, int batchSize) {
        Query query = buildBaseQuery();
        query.addCriteria(Criteria.where(IndexDedupConstants.ID).gt(currentId).lte(endId))
                .with(Sort.by(Sort.Direction.ASC, IndexDedupConstants.ID))
                .limit(batchSize);
                
        // 只查询必要的字段以减少内存占用
        query.fields().include(IndexDedupConstants.ID, IndexDedupConstants.EMBS, IndexDedupConstants.LEN);
        
        return query;
    }
}