package com.iflytek.lynxiao.asset.action.doc.idxdedup.condition;


import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@ConditionalOnProperty(value = "lynxiao.asset.action.doc.index-dedup.enabled")
public @interface ConditionalOnIndexDedup {
}

