package com.iflytek.lynxiao.asset.action.doc.idxdedup.service;

import com.iflytek.lynxiao.asset.action.doc.idxdedup.config.IndexDedupProperties;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.constants.IndexDedupConstants;
import com.iflytek.lynxiao.asset.action.doc.idxdedup.model.DocumentProcessingContext;
import com.iflytek.lynxiao.asset.api.FeatureBaseApi;
import com.iflytek.lynxiao.asset.cache.BucketCacheService;
import com.iflytek.lynxiao.asset.sender.MqMessageCtrlSender;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.constant.DedupType;
import com.iflytek.lynxiao.data.dto.action.dedup.BucketIdxDedupMessage;
import com.iflytek.lynxiao.data.dto.featurebase.FeatureBaseDetailGetDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import skynet.boot.pandora.support.TaskCancelCache;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 索引去重任务执行器
 * 包含索引去重的主要业务逻辑
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Slf4j
public class IndexDedupTaskExecutor implements AutoCloseable {

    private final BucketCacheService bucketCacheService;
    private final FeatureBaseApi featureBaseApi;
    private final MqMessageCtrlSender mqMessageCtrlSender;
    private final IndexDedupProperties properties;
    private final DocumentQueryService documentQueryService;
    private final DocumentDeduplicationService documentDeduplicationService;

    private final ExecutorService partitionExecutor;
    /**
     * -- GETTER --
     *  获取异步执行器
     */
    @Getter
    private final ExecutorService asyncExecutor;
    private final TaskCancelCache taskCancelCache;


    public IndexDedupTaskExecutor(BucketCacheService bucketCacheService,
                                  FeatureBaseApi featureBaseApi,
                                  MqMessageCtrlSender mqMessageCtrlSender,
                                  IndexDedupProperties properties,
                                  DocumentQueryService documentQueryService,
                                  DocumentDeduplicationService documentDeduplicationService,
                                  TaskCancelCache taskCancelCache) {
        this.bucketCacheService = bucketCacheService;
        this.featureBaseApi = featureBaseApi;
        this.mqMessageCtrlSender = mqMessageCtrlSender;
        this.properties = properties;
        this.documentQueryService = documentQueryService;
        this.documentDeduplicationService = documentDeduplicationService;
        this.taskCancelCache = taskCancelCache;

        int corePoolSize = properties.getTask().getPartition();
        // 创建分区处理线程池
        this.partitionExecutor = new ThreadPoolExecutor(
                corePoolSize,
                corePoolSize,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(corePoolSize * 4),
                new BasicThreadFactory.Builder().namingPattern("part-dedup-%d").build(),
                new ThreadPoolExecutor.AbortPolicy()
        );
        
        // 创建异步任务执行线程池
        this.asyncExecutor = new ThreadPoolExecutor(
                1,
                1,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                new BasicThreadFactory.Builder().namingPattern("task-dedup-%d").build(),
                new ThreadPoolExecutor.AbortPolicy()
        );
    }


    public void execute(String taskId, BucketIdxDedupMessage message) {
        log.info("{} Starting index dedup task. taskId={}, bucketCode={}",
                IndexDedupConstants.LogTags.INDEX_DEDUP, taskId, message.getBucketCode());

        try {
            long count = documentQueryService.getEstimatedDocumentCount(message.getBucketCode());
            // 1. Send start message
            mqMessageCtrlSender.sendTotal(DedupType.STEP_ONE, taskId, count);

            // 2. Process documents
            processAllDocuments(taskId, message);

            // Get exact count using the service
            long realCount = documentQueryService.getExactDocumentCount(message.getBucketCode());

            // 3. Send success message
            mqMessageCtrlSender.sendTotal(DedupType.STEP_ONE, taskId, realCount);
            log.info("{} Index dedup task finished successfully. taskId={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, taskId);

        } catch (Exception e) {
            log.error("{} Index dedup task failed. taskId={}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, taskId, e);
            try {
                mqMessageCtrlSender.sendFailMsg(DedupType.STEP_ONE, taskId, 0L, e.getMessage());
            } catch (Exception ex) {
                log.error("{} Failed to send failure notification. taskId={}", 
                        IndexDedupConstants.LogTags.INDEX_DEDUP, taskId, ex);
            }
        }
    }


    private void processAllDocuments(String taskId, BucketIdxDedupMessage message) {
        MongoTemplate mongoTemplate = bucketCacheService.getClient(message.getBucketCode()).getMongoTemplate();

        // Use document query service to find min/max documents
        Document minDoc = documentQueryService.findMinMaxDocuments(message.getBucketCode(),
                Sort.by(Sort.Direction.ASC, IndexDedupConstants.ID));
        Document maxDoc = documentQueryService.findMinMaxDocuments(message.getBucketCode(),
                Sort.by(Sort.Direction.DESC, IndexDedupConstants.ID));

        if (minDoc == null || maxDoc == null) {
            log.info("{} No documents to process after filtering in collection: {}", 
                    IndexDedupConstants.LogTags.MONGO_SERVICE, message.getBucketCode());
            return;
        }

        long minId = minDoc.getLong(IndexDedupConstants.ID);
        long maxId = maxDoc.getLong(IndexDedupConstants.ID);
        int partitionCount = properties.getTask().getPartition();
        long range = (maxId - minId) / partitionCount;
        if (range == 0) {
            partitionCount = 1;
        }

        // 使用CompletableFuture替代CountDownLatch，提供更好的异常处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < partitionCount; i++) {
            long startId = minId + i * range;
            long endId = (i == partitionCount - 1) ? maxId : (startId + range) - 1;
            final int partitionIndex = i;

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    log.info("{} Processing partition {} from {} to {}", 
                            IndexDedupConstants.LogTags.PARTITION, partitionIndex, startId, endId);
                    FeatureBaseDetailGetDTO featureBase = featureBaseApi.getFeatureBase(UUID.randomUUID().toString(), message.getBucketCode());
                    
                    // Create processing context
                    DocumentProcessingContext context = DocumentProcessingContext.forPartition(
                            taskId, message.getBucketCode(), message.getRecallConfig(), 
                            mongoTemplate, startId, endId, partitionIndex);
                    
                    processPartition(context, featureBase);
                    log.info("{} Partition {} processing completed", 
                            IndexDedupConstants.LogTags.PARTITION, partitionIndex);
                } catch (Exception e) {
                    log.error("{} Failed to process partition {}", 
                            IndexDedupConstants.LogTags.PARTITION, partitionIndex, e);
                    throw new RuntimeException("Partition processing failed", e);
                }
            }, partitionExecutor);

            futures.add(future);
        }

        // 等待所有分区处理完成，支持超时和异常处理
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(properties.getTask().getTaskTimeout().toMillis(), TimeUnit.MILLISECONDS);
            log.info("{} All partitions processing completed successfully for taskId: {}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, taskId);
        } catch (Exception e) {
            log.error("{} Error waiting for partition processing completion. taskId: {}", 
                    IndexDedupConstants.LogTags.INDEX_DEDUP, taskId, e);
            // 取消未完成的任务
            futures.forEach(f -> f.cancel(true));
            throw new LynxiaoException("Partition processing timeout or failed", e);
        }
    }

    private void processPartition(DocumentProcessingContext context, FeatureBaseDetailGetDTO featureBase) {
        final String threadName = Thread.currentThread().getName();
        log.info("{} Starting partition processing: thread={}, startId={}, endId={}, bucketCode={}", 
                IndexDedupConstants.LogTags.PARTITION, threadName, context.getStartId(), 
                context.getEndId(), context.getBucketCode());

        long currentId = context.getStartId() - 1; // Initialize to startId-1, so first query starts from startId
        long processedInPartition = 0;
        long duplicatesInPartition = 0;

        try {
            while (currentId <= context.getEndId()) {
                if (taskCancelCache.isCancel(context.getTaskId())) {
                    log.info("{} Task cancelled, stopping partition processing. taskId={}, thread={}", 
                            IndexDedupConstants.LogTags.PARTITION, context.getTaskId(), threadName);
                    return;
                }

                // Use document query service to fetch batch
                List<Document> batch = documentQueryService.fetchDocumentBatch(
                        context, currentId, properties.getTask().getBatchSize());

                if (batch.isEmpty()) {
                    log.debug("{} Partition processing completed, no more documents: thread={}, currentId={}, endId={}", 
                            IndexDedupConstants.LogTags.PARTITION, threadName, currentId, context.getEndId());
                    break;
                }

                currentId = batch.getLast().getLong(IndexDedupConstants.ID);

                for (Document doc : batch) {
                    try {
                        // Use document deduplication service
                        int similarDocsCount = documentDeduplicationService
                                .processDocumentDeduplication(doc, context, featureBase);
                        
                        if (similarDocsCount > 1) {
                            duplicatesInPartition++;
                        }
                        processedInPartition++;
                        mqMessageCtrlSender.sendDone(DedupType.STEP_ONE, context.getTaskId(), 1);

                    } catch (Exception e) {
                        log.error("{} Error processing document: docId={}, bucketCode={}", 
                                IndexDedupConstants.LogTags.PARTITION, doc.getLong(IndexDedupConstants.ID), context.getBucketCode(), e);
                        // Continue processing next document, don't interrupt entire partition
                    }
                }

                // Periodic partition processing progress logging
                if (processedInPartition % 1000 == 0) {
                    log.debug("{} Partition processing progress: thread={}, processed={}, duplicates={}, currentId={}", 
                            IndexDedupConstants.LogTags.PARTITION, threadName, processedInPartition, duplicatesInPartition, currentId);
                }
            }

            log.info("{} Partition processing completed successfully: thread={}, processed={}, duplicates={}, range=[{}, {}]", 
                    IndexDedupConstants.LogTags.PARTITION, threadName, processedInPartition, duplicatesInPartition, 
                    context.getStartId(), context.getEndId());

        } catch (Exception e) {
            log.error("{} Partition processing failed: thread={}, startId={}, endId={}, bucketCode={}", 
                    IndexDedupConstants.LogTags.PARTITION, threadName, context.getStartId(), 
                    context.getEndId(), context.getBucketCode(), e);
            throw e;
        }
    }

    
    @Override
    public void close() throws Exception {
        log.info("{} Shutting down IndexDedupTaskExecutor...", IndexDedupConstants.LogTags.INDEX_DEDUP);
        
        // 优雅关闭分区执行器
        shutdownExecutor(partitionExecutor, "partitionExecutor", 30);
        
        // 优雅关闭异步执行器
        shutdownExecutor(asyncExecutor, "asyncExecutor", 15);
        
        log.info("{} IndexDedupTaskExecutor shutdown completed.", IndexDedupConstants.LogTags.INDEX_DEDUP);
    }
    
    /**
     * 优雅关闭线程池
     */
    private void shutdownExecutor(ExecutorService executor, String executorName, long timeout) {
        if (executor == null || executor.isShutdown()) {
            return;
        }
        
        try {
            log.info("{} Shutting down {}", IndexDedupConstants.LogTags.INDEX_DEDUP, executorName);
            executor.shutdown();
            
            if (!executor.awaitTermination(timeout, TimeUnit.SECONDS)) {
                log.warn("{} {} did not terminate gracefully within {} {}, forcing shutdown", 
                        IndexDedupConstants.LogTags.INDEX_DEDUP, executorName, timeout, TimeUnit.SECONDS);
                executor.shutdownNow();
                
                // 再等待一半时间
                if (!executor.awaitTermination(timeout / 2, TimeUnit.SECONDS)) {
                    log.error("{} {} did not terminate after forced shutdown", 
                            IndexDedupConstants.LogTags.INDEX_DEDUP, executorName);
                }
            } else {
                log.info("{} {} shutdown completed gracefully", IndexDedupConstants.LogTags.INDEX_DEDUP, executorName);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("{} Interrupted while waiting for {} to terminate", IndexDedupConstants.LogTags.INDEX_DEDUP, executorName);
            executor.shutdownNow();
        }
    }
}
