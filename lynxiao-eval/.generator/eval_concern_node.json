{"domainInfos": [{"domainEnName": "eval_concern_node", "domainCnName": "关注节点配置表", "domainApiExclude": "*", "columnInfos": [{"columnEnName": "name", "columnCnName": "节点名称，例如：文本召回", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "code", "columnCnName": "节点编码，例如：recall-textrc", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "sort", "columnCnName": "排序", "columnType": "int"}, {"columnEnName": "input_filed", "columnCnName": "节点入参，针对doc的入参字段", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "output_field", "columnCnName": "节点出参，针对doc的出参字段", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "score_field", "columnCnName": "得分字段", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "index_field", "columnCnName": "位次字段", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "can_mock", "columnCnName": "是否可mock", "columnType": "tinyint"}, {"columnEnName": "show_in_recall", "columnCnName": "是否在标注页面展示", "columnType": "tinyint"}, {"columnEnName": "show_in_trace", "columnCnName": "是否在全链路中展示", "columnType": "tinyint"}, {"columnEnName": "default_value", "columnCnName": "默认值", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "description", "columnCnName": "描述", "columnType": "text"}, {"columnEnName": "deleted", "columnCnName": "是否删除", "columnType": "tinyint"}]}]}