{"domainInfos": [{"domainEnName": "eval_field_config", "domainCnName": "测评字段配置表", "domainApiExclude": "*", "columnInfos": [{"columnEnName": "name", "columnCnName": "字段名称，例如：标题、内容", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "field", "columnCnName": "字段，例如：title、content", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "type", "columnCnName": "字段类型，String、long等", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "category", "columnCnName": "字段分类，例如：通用字段、医疗字段等", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "idx", "columnCnName": "排序", "columnType": "int"}, {"columnEnName": "path", "columnCnName": "字段路径，如果为空，默认从doc下取", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "show_in_trace", "columnCnName": "是否在全链路展示", "columnType": "tinyint"}, {"columnEnName": "default_value", "columnCnName": "默认值", "columnType": "<PERSON><PERSON><PERSON>"}, {"columnEnName": "description", "columnCnName": "描述", "columnType": "text"}, {"columnEnName": "sample", "columnCnName": "样例", "columnType": "text"}, {"columnEnName": "deleted", "columnCnName": "是否删除", "columnType": "tinyint"}]}]}