package com.iflytek.lynxiao.eval.test.asset;

import com.iflytek.lynxiao.common.feign.asset.AssetPortalApi;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.eval.EvalTestBoot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;

@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = EvalTestBoot.class)
@ActiveProfiles("dev")
public class AssetTest {

    @Autowired
    private AssetPortalApi assetPortalApi;

    @Test
    public void test(){
        List<AssetBucketDTO> cwruanIdxU39H4IK6 = assetPortalApi.findAllSourceBuckets("dev_IDX_KQEUDJO1");
        log.info("response: {}", cwruanIdxU39H4IK6);
    }



    @Test
    public void testGetCell(){
        String docId = "8111941372165170580";
        String docUrl = "https://dxy.com/disease/6792/detail";
//        String docUrl = "";
        CellQueryDTO cellQueryDTO = new CellQueryDTO();
        cellQueryDTO.setRegionCode("hf");
        cellQueryDTO.setBucketCode("dev_IDX_KQEUDJO1");
        if (StringUtils.isNotBlank(docId)) {
            cellQueryDTO.setIds(Collections.singletonList(docId));
        }
        if (StringUtils.isNotBlank(docUrl)) {
            cellQueryDTO.setUrls(Collections.singletonList(docUrl));
        }
        List<AssetCell> docs = this.assetPortalApi.listAssetCell(cellQueryDTO);
        log.info("docs: {}", docs);
    }
}



