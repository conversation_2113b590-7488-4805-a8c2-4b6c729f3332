--liquibase formatted sql
--changeset skynet:create_table_eval_field_config
CREATE TABLE `eval_field_config`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '字段名称，例如：标题、内容', 
    `field` varchar(100) COMMENT '字段，例如：title、content', 
    `type` varchar(100) COMMENT '字段类型，String、long等', 
    `category` varchar(100) COMMENT '字段分类，例如：通用字段、医疗字段等', 
    `idx` int COMMENT '排序', 
    `path` varchar(100) COMMENT '字段路径，如果为空，默认从doc下取', 
    `show_in_trace` tinyint COMMENT '是否在全链路展示', 
    `default_value` varchar(100) COMMENT '默认值', 
    `description` text COMMENT '描述', 
    `sample` text COMMENT '样例', 
    `deleted` tinyint COMMENT '是否删除', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

