--liquibase formatted sql
--changeset skynet:create_table_eval_concern_node
CREATE TABLE `eval_concern_node`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '节点名称，例如：文本召回', 
    `code` varchar(100) COMMENT '节点编码，例如：recall-textrc', 
    `sort` int COMMENT '排序', 
    `input_filed` varchar(100) COMMENT '节点入参，针对doc的入参字段', 
    `output_field` varchar(100) COMMENT '节点出参，针对doc的出参字段', 
    `score_field` varchar(100) COMMENT '得分字段', 
    `index_field` varchar(100) COMMENT '位次字段', 
    `can_mock` tinyint COMMENT '是否可mock', 
    `show_in_recall` tinyint COMMENT '是否在标注页面展示', 
    `show_in_trace` tinyint COMMENT '是否在全链路中展示', 
    `default_value` varchar(100) COMMENT '默认值', 
    `description` text COMMENT '描述', 
    `deleted` tinyint COMMENT '是否删除', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

