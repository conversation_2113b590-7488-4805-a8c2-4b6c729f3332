--liquibase formatted sql
--changeset skynet:create_table_eval_mission
CREATE TABLE `eval_mission`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '名称', 
    `label` varchar(100) COMMENT '分组', 
    `group_number` int COMMENT '分组个数', 
    `type` int COMMENT '状态 1：在线  2：离线', 
    `query_group_id` bigint COMMENT 'query集id', 
    `begin_time` datetime COMMENT '活动开始时间', 
    `end_time` datetime COMMENT '活动结束时间', 
    `description` text COMMENT '描述', 
    `catalog_code` varchar(100) COMMENT '所属树目录code', 
    `standard_id` bigint COMMENT '测评标准id', 
    `standard_config` text COMMENT '测评标准id的具体测评配置', 
    `ascribe_status` int COMMENT '归因0：待分析；1：正在分析；2：已完成；3：已完成（但有失败）', 
    `ascribe_start_ts` datetime COMMENT '归因开始时间', 
    `ascribe_end_ts` datetime COMMENT '归因结束时间', 
    `extend_fields` text COMMENT '扩展字段', 
    `strategy_config` text COMMENT '策略配置', 
    `enabled` tinyint COMMENT '是否启用', 
    `status` int COMMENT '状态', 
    `deleted` tinyint COMMENT '删除标志', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

