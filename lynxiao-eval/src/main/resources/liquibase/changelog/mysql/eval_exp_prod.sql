--liquibase formatted sql
--changeset skynet:create_table_eval_exp_prod
CREATE TABLE `eval_exp_prod`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COMMENT '流程名称', 
    `code` varchar(100) COMMENT '流程id', 
    `sort` int COMMENT '排序', 
    `description` text COMMENT '描述', 
    `enabled` tinyint COMMENT '是否启用', 
    `deleted` tinyint COMMENT '是否删除', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

