--liquibase formatted sql
--changeset skynet:create_table_eval_mark_record
CREATE TABLE `eval_mark_record`( 
    `id` int NOT NULL AUTO_INCREMENT,
    `query` varchar(400) COMMENT '测评的query', 
    `account` varchar(100) COMMENT '用户account', 
    `mode` int COMMENT '测评模式，1:体验模式 2:单问题模式  3:测评任务模式', 
    `mission_id` bigint COMMENT '测评任务id', 
    `user_group` varchar(100) COMMENT '组名', 
    `ascribed` bit COMMENT '是否完成自动归因', 
    `ascribe_success` bit COMMENT '自动归因是否成功', 
    `finish` bit COMMENT '是否标注完成', 
    `query_ignore` text COMMENT 'query类弃标', 
    `created_by` varchar(100) COMMENT '创建人',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_modified_by` varchar(100) COMMENT '更新人',
    `last_modified_date` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(100) COMMENT '租户',
    PRIMARY KEY (`id`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_created_date` (`created_date`),
    INDEX `idx_last_modified_by` (`last_modified_by`),
    INDEX `idx_last_modified_date` (`last_modified_date`),
    INDEX `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin;

