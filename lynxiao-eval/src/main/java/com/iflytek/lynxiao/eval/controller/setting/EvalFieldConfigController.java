package com.iflytek.lynxiao.eval.controller.setting;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.field.EvalFieldConfigSaveDTO;
import com.iflytek.lynxiao.eval.service.setting.EvalFieldConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 测评字段配置管理
 *
 * <AUTHOR>
 */
@Tag(name = "测评字段配置管理")
@RestController
@RequestMapping("/eval/api/v1/field-config")
@EnableSkynetSwagger2
public class EvalFieldConfigController {

    private final EvalFieldConfigService service;

    public EvalFieldConfigController(EvalFieldConfigService service) {
        this.service = service;
    }

    @Operation(summary = "创建或更新字段配置")
    @PostMapping("")
    public ApiResponse save(@Validated @RequestBody EvalFieldConfigSaveDTO dto) {
        service.save(dto);
        return new ApiResponse();
    }

    @Operation(summary = "分页查询字段配置")
    @GetMapping("/findByPage")
    public ApiResponse findByPage(@RequestParam(value = "search", required = false) String search,
                                  Pageable pageable) {
        return new ApiResponse(JSONObject.from(service.findByPage(search, pageable)));
    }

    @Operation(summary = "查询所有字段配置")
    @GetMapping("/list")
    public ApiResponse list(@RequestParam(value = "category", required = false) String category) {
        return new ApiResponse(JSONObject.of("data", service.findAll(category)));
    }

    @Operation(summary = "查询字段配置详情")
    @GetMapping("/{id}")
    public ApiResponse findById(@PathVariable String id) {
        return new ApiResponse(JSONObject.from(service.findById(id)));
    }

    @Operation(summary = "删除字段配置")
    @DeleteMapping("/{id}")
    public ApiResponse delete(@PathVariable String id) {
        service.deleteById(id);
        return new ApiResponse();
    }

    @Operation(summary = "根据字段名查询配置")
    @GetMapping("/field/{fieldName}")
    public ApiResponse findByFieldName(@PathVariable String fieldName) {
        return new ApiResponse(JSONObject.from(service.findByFieldName(fieldName)));
    }

    @Operation(summary = "查询所有字段分类")
    @GetMapping("/categories")
    public ApiResponse findAllCategories() {
        return new ApiResponse(JSONObject.of("data", service.findAllCategories()));
    }

    @Operation(summary = "根据分类查询字段配置")
    @GetMapping("/category/{category}")
    public ApiResponse findByCategory(@PathVariable String category) {
        return new ApiResponse(JSONObject.of("data", service.findByCategory(category)));
    }

    @Operation(summary = "检查字段名是否存在")
    @GetMapping("/check-field-name")
    public ApiResponse checkFieldName(@RequestParam String fieldName,
                                      @RequestParam(required = false) String excludeId) {
        boolean exists = service.isFieldNameExists(fieldName, excludeId);
        return new ApiResponse(JSONObject.of("exists", exists));
    }
}
