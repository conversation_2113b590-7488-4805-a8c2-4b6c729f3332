package com.iflytek.lynxiao.eval.dto.standard;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 关注节点配置表保存DTO
 */
@Getter
@Setter
public class EvalConcernNodeSaveDTO extends Jsonable {

    /**
     * 有id编辑，无id创建
     */
    private String id;

    /**
     * 节点名称，例如：文本召回
     */
    @NotBlank(message = "节点名称不能为空")
    private String name;

    /**
     * 节点编码，例如：recall-textrc
     */
    @NotBlank(message = "节点编码不能为空")
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 节点入参，针对doc的入参字段
     */
    @NotBlank(message = "节点入参不能为空")
    private String inputFiled;

    /**
     * 节点出参，针对doc的出参字段
     */
    @NotBlank(message = "节点出参不能为空")
    private String outputField;

    /**
     * 得分字段
     */
    private String scoreField;

    /**
     * 位次字段
     */
    private String indexField;

    /**
     * 是否允许mock
     */
    private boolean canMock = false;

    /**
     * 是否在标注页面展示
     */
    private boolean showInRecall = false;

    /**
     * 是否在全链路中展示
     */
    private boolean showInTrace = false;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 描述
     */
    private String description;
}
