package com.iflytek.lynxiao.eval.dto.standard;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 效果体验配置表保存DTO
 */
@Getter
@Setter
public class EvalExpProdSaveDTO extends Jsonable {

    /**
     * 有id编辑，无id创建
     */
    private String id;

    /**
     * 节点名称，例如：文本召回
     */
    @NotBlank(message = "节点名称不能为空")
    private String name;

    /**
     * 节点编码，例如：recall-textrc
     */
    @NotBlank(message = "节点编码不能为空")
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否启用
     */
    private Boolean enabled;
}
