package com.iflytek.lynxiao.eval.component.domain;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class EvalResultItem extends Jsonable {

    // 分析环节名称
    private String name;

    // 0 状态正常  1 状态异常
    private int value;

    public EvalResultItem() {
    }

    public EvalResultItem(String name) {
        this(name, 0);
    }

    public EvalResultItem(String name, int value) {
        this.name = name;
        this.value = value;
    }
}