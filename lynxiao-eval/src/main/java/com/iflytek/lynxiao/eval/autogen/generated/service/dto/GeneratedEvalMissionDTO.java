package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedEvalMissionDTO extends AbstractAuditingEntity<Long> {

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 分组
     */
    @Schema(title = "分组")
    private String label;

    /**
     * 分组个数
     */
    @Schema(title = "分组个数")
    private Integer groupNumber;

    /**
     * 状态 1：在线  2：离线
     */
    @Schema(title = "状态 1：在线  2：离线")
    private Integer type;

    /**
     * query集id
     */
    @Schema(title = "query集id")
    private Long queryGroupId;

    /**
     * 活动开始时间
     */
    @Schema(title = "活动开始时间")
    private Instant beginTime;

    /**
     * 活动结束时间
     */
    @Schema(title = "活动结束时间")
    private Instant endTime;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 所属树目录code
     */
    @Schema(title = "所属树目录code")
    private String catalogCode;

    /**
     * 测评标准id
     */
    @Schema(title = "测评标准id")
    private Long standardId;

    /**
     * 测评标准id的具体测评配置
     */
    @Schema(title = "测评标准id的具体测评配置")
    private String standardConfig;

    /**
     * 归因0：待分析；1：正在分析；2：已完成；3：已完成（但有失败）
     */
    @Schema(title = "归因0：待分析；1：正在分析；2：已完成；3：已完成（但有失败）")
    private Integer ascribeStatus;

    /**
     * 归因开始时间
     */
    @Schema(title = "归因开始时间")
    private Instant ascribeStartTs;

    /**
     * 归因结束时间
     */
    @Schema(title = "归因结束时间")
    private Instant ascribeEndTs;

    /**
     * 扩展字段
     */
    @Schema(title = "扩展字段")
    private String extendFields;

    /**
     * 策略配置
     */
    @Schema(title = "策略配置")
    private String strategyConfig;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 状态
     */
    @Schema(title = "状态")
    private Integer status;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;
}