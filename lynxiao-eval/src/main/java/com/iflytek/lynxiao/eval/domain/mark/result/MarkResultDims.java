package com.iflytek.lynxiao.eval.domain.mark.result;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注维度集详情DTO
 */
@Setter
@Getter
@Accessors(chain = true)
public class MarkResultDims extends Jsonable {

    /**
     * 名称
     */
    private String name;

    /**
     * code
     */
    private String code;

    /**
     * 选项类型
     *
     * @see com.iflytek.lynxiao.portal.eval.domain.MarkOptionType
     */
    private int optType;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 层级
     * @see com.iflytek.lynxiao.portal.eval.domain.DimsGroupDetailLevel
     */
    private int level;

    private boolean value;

    /**
     * 子项列表
     */
    private List<MarkResultDims> children;
}
