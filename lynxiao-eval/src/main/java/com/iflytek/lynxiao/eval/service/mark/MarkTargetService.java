package com.iflytek.lynxiao.eval.service.mark;


import com.iflytek.lynxiao.eval.dto.mark.record.ChatRecordDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordCreateDTO;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;

import java.util.List;

/**
 * 标注对象管理
 */
public interface MarkTargetService {

    /**
     * 生成待标注对象
     *
     * @param markRecordId 测评记录id
     * @param dto          参数
     * @return 标注对象
     */
    List<MarkTargetEntity> create(String markRecordId, MarkRecordCreateDTO dto);

    /**
     * 获取chat结果
     *
     * @param id markTargetId
     * @return 大模型推理结果
     */
    ChatRecordDTO findChat(String id);

}
