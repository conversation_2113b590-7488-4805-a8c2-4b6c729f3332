package com.iflytek.lynxiao.eval.domain;

import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultDims;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iflytek.lynxiao.eval.domain.UrlEvalType.BAD_DOC;
import static com.iflytek.lynxiao.eval.domain.UrlEvalType.GOOD_DOC;

/**
 * [
 * {
 * "name": "good",
 * "rule": [
 * {
 * "dimName": "时效性",
 * "optionNames": [
 * "好"
 * ]
 * },
 * {
 * "dimName": "ABC",
 * "optionNames": [
 * "A"
 * ]
 * }
 * ]
 * },
 * {
 * "name": "bad",
 * "rule": [
 * {
 * "dimName": "时效性",
 * "optionNames": [
 * "不好"
 * ]
 * },
 * {
 * "dimName": "ABC",
 * "optionNames": [
 * "C"
 * ]
 * }
 * ]
 * },
 * {
 * "name": "自定义标准",
 * "rule": [
 * {
 * "dimName": "时效性",
 * "optionNames": [
 * "一般"
 * ]
 * },
 * {
 * "dimName": "ABC",
 * "optionNames": [
 * "B"
 * ]
 * }
 * ]
 * }
 * ]
 * <p>
 * 判断标注结果中 good doc 和 bad doc 的规则。
 */
@Setter
@Getter
public class DocClassifyRule extends Jsonable {

    /**
     * 规则名称 good和bad为固定值  也可自定义新增规则
     */
    private String name;

    /**
     * 规则组集合,  如： [{时效性:好, ABC:A,内容质量:高} 、{时效性:好, ABC:B,内容质量:高}、] 满足其中一组即满足规则标准
     */
    private List<RuleGroup> ruleGroup;


    /**
     * 根据标注结果判断是good 、 bad or unknown
     *
     * @param dimMarkResult 维度标注结果
     * @param rules         判定规则列表
     * @return good 、 bad or unknown
     */
    public static UrlEvalType classifyBaseRule(List<MarkResultDims> dimMarkResult, List<DocClassifyRule> rules) {
        if (CollectionUtils.isEmpty(dimMarkResult)) {
            return UrlEvalType.UNKNOWN;
        }

        //没有选中的维度 或者存在doc类弃标 则返回 UNKNOWN
        if (!isDimMarkResultValid(dimMarkResult)) {
            return UrlEvalType.UNKNOWN;
        }

        // Step 1: 获取good 规则与bad规则
        Map<String, List<Map<String, DimOption>>> classifyMap = convertRuleList2Map(rules);

        List<Map<String, DimOption>> goodRuleMap = classifyMap.get(GOOD_DOC.getValue());
        List<Map<String, DimOption>> badRuleMap = classifyMap.get(BAD_DOC.getValue());

        if (goodRuleMap == null || badRuleMap == null) {
            //good bad规则不能为空
            throw new LynxiaoException("分类规则不合法");
        }

        Map<String, List<String>> markDimOptMap = convert(dimMarkResult);
        // Step 2: 检查 bad 规则  满足任意一条 bad 规则即为 BAD_DOC
        for (Map<String, DimOption> badRuleMapItem : badRuleMap) {
            // 如果标注结果符合任意一条 bad 规则，则返回 BAD_DOC
            if (isMeetRule(markDimOptMap, badRuleMapItem)) {
                return BAD_DOC;
            }

        }
        // 如果没有符合 bad 规则，则继续检查 good 规则  满足任意一条 good 规则即为 GOOD_DOC
        for (Map<String, DimOption> goodRuleMapItem : goodRuleMap) {
            // 如果标注结果符合任意一条 good 规则，则返回 GOOD_DOC
            if (isMeetRule(markDimOptMap, goodRuleMapItem)) {
                return GOOD_DOC;
            }
        }
        return UrlEvalType.UNKNOWN;
    }

    /**
     * 将MarkResultDims结构的标注结果转换为 <维度名称, 选项值列表>的map，方便后面进行规则判断
     *
     * @example: abc模式标注结果 转换为 <ABC模式, [A,B]>
     * @param dimMarkResult
     * @return
     */
    private static Map<String, List<String>> convert(List<MarkResultDims> dimMarkResult) {
        //<维度名， 选项列表>
        Map<String, List<String>> markDimOptMap = dimMarkResult.stream()
                .collect(Collectors.toMap(MarkResultDims::getName, dim -> {
                    if (CollectionUtils.isEmpty(dim.getChildren())) {
                        return new ArrayList<>();
                    }
                    List<MarkResultDims> optionList = dim.getChildren();
                    return optionList.stream().filter(MarkResultDims::isValue).map(MarkResultDims::getName).collect(Collectors.toList());
                }));

        return markDimOptMap;
    }

    /**
     * 判断自定义规则
     *
     * @param dimMarkResult 维度标注结果
     * @param rules         判定规则列表
     * @return key：规则分类名称  value: 是否符合规则
     */
    public static Map<String, Boolean> classifyCustomRule(List<MarkResultDims> dimMarkResult, List<DocClassifyRule> rules) {

        Map<String, List<Map<String, DimOption>>> classifyMap = convertRuleList2Map(rules);
        classifyMap.remove(GOOD_DOC.getValue());
        classifyMap.remove(BAD_DOC.getValue());

        if (!isDimMarkResultValid(dimMarkResult)) {

            Map<String, Boolean> result = new HashMap<>();
            classifyMap.keySet()
                    .forEach(classifyName -> result.put(classifyName, false));
            return result;
        }

        Map<String, List<String>> markDimOptMap = convert(dimMarkResult);
        Map<String, Boolean> classifyResultmap = new HashMap<>();
        classifyMap.forEach((classifyName, ruleMaps) -> {
            boolean flag = false;
            for (Map<String, DimOption> ruleMap : ruleMaps) {
                if (isMeetRule(markDimOptMap, ruleMap)) {
                    // 满足任意一组即为符合规则
                    flag = true;
                }
            }
            classifyResultmap.put(classifyName, flag);
        });

        return classifyResultmap;
    }

    /**
     * 判断维度标注结果是否符合规则
     *
     * @param dimMarkResult 维度标注结果
     * @return true: 符合规则， false: 不符合规则
     */
    private static boolean isDimMarkResultValid(List<MarkResultDims> dimMarkResult) {
        //没有选中的维度 或者存在doc类弃标 则返回 UNKNOWN
        if (CollectionUtils.isEmpty(dimMarkResult.stream().filter(MarkResultDims::isValue).toList()) ||
                dimMarkResult.stream().anyMatch(item -> item.isValue() && item.getName().equals("doc类弃标原因"))) {
            return false;
        }
        return true;
    }

    /**
     * 将分类规则转为map
     * key: 规则名称  value.key: 标注维度名称  value.value: 选项列表
     */
    static Map<String, List<Map<String, DimOption>>> convertRuleList2Map(List<DocClassifyRule> rules) {

        // 例： key: good  value: [{时效性:好、 ABC:A、内容质量:高}、{时效性:好、 ABC:B、内容质量:高}]
        Map<String, List<Map<String, DimOption>>> rulesMap = new HashMap<>();

        if (CollectionUtils.isEmpty(rules)) {
            return rulesMap;
        }

        for (DocClassifyRule rule : rules) {
            rulesMap.put(rule.getName(), getRulesMap(rule));
        }
        return rulesMap;
    }

    private static List<Map<String, DimOption>> getRulesMap(DocClassifyRule rule) {
        List<Map<String, DimOption>> rulesMap = new ArrayList<>();
        for (RuleGroup ruleGroup : rule.getRuleGroup()) {
            rulesMap.add(
                    ruleGroup.getDimOptions().stream()
                            .collect(Collectors.toMap(DimOption::getDimName, dimOption -> dimOption))
            );
        }
        return rulesMap;
    }


    /**
     * 判断当前标注结果是否符合规则
     * <p>
     * 单个维度是否满足规则：所有的标注选项都在判断对则的选项列表中
     *
     * @param markDimOptMap 维度标注值， key:标注维度 例如时效性，  value: 选项列表 例如[好, 一般],支持多选
     * @param rule          判断规则  key: 标注维度 例如时效性，  value: 选项列表 例如[好, 一般]，
     */
    private static boolean isMeetRule(Map<String, List<String>> markDimOptMap, Map<String, DimOption> rule) {
        // 判断是否符合负责，标注选项都满足设置的规则
        return rule.entrySet().stream().allMatch(entry -> {

            //没有在标注结果中找到当前校验规则的标注值  规则检验不通过
            if (CollectionUtils.isEmpty(markDimOptMap.get(entry.getKey()))) {
                return false;
            }

            return markDimOptMap.get(entry.getKey()).stream().allMatch(
                    markDimOption -> entry.getValue().isMatchOption(markDimOption)
            );

        });
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    static class DimOption extends Jsonable {

        // 标注维度的名称
        private String dimName;

        // 标准维度选项的名称
        private List<String> optionNames = new ArrayList<>();

        public boolean isMatchOption(String markOptionName) {
            return this.optionNames.contains(markOptionName);
        }

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class RuleGroup extends Jsonable {
        private List<DimOption> dimOptions = new ArrayList<>();
    }
}