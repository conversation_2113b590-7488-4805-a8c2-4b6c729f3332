package com.iflytek.lynxiao.eval.service.mark;

import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordCreateDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordSaveDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordSaveResultDTO;

import java.util.List;

/**
 * 测评记录管理
 */
public interface MarkRecordService {

    /**
     * 创建测评记录：执行搜索时调用
     *
     * @param dto 搜索参数
     * @return 记录id
     */
    String create(MarkRecordCreateDTO dto);

    /**
     * 根据id查询测评记录
     *
     * @param id     测评记录id
     * @param recall 是否包含召回结果
     * @param trace  是否包含全链路结果
     * @param chat   是否包含chat结果
     * @return 测评记录
     */
    MarkRecordDTO find(String id, boolean recall, boolean trace, boolean chat);

    /**
     * 查询未完成的标注记录
     *
     * @param missionId 任务id
     * @return 测评记录
     */
    MarkRecordDTO findUnfinished(String missionId);

    /**
     * 查询指定任务下的所有标注记录
     * @param missionId 任务id
     * @return 标注记录id列表
     */
    List<String> findByMissionId(String missionId);

    /**
     * 保存标注记录
     *
     * @param dto 标注记录
     */
    MarkRecordSaveResultDTO save(MarkRecordSaveDTO dto);

    /**
     * 删除指定标注结果
     *
     * @param resultId 标注结果id
     */
    void deleteMarkResult(String resultId);

    /**
     * 归因分析
     *
     * @param id 测评记录id
     */
    void ascribe(String id);

}
