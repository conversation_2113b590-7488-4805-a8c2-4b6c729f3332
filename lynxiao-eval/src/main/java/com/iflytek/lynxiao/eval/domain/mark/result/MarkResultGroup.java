package com.iflytek.lynxiao.eval.domain.mark.result;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.lynxiao.eval.domain.MarkResultGroupType;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMissionAssign;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标注结果统计分组信息，包含分组指标（划分标注）、关联的标注任务、标注对象、标注结果等信息；
 * 单query：按照策略id分组；
 * 多query（测评任务）：按照策略id和标注人（组）分组；
 */
@Setter
@Getter
public class MarkResultGroup {

    /**
     * 模式 1:体验模式 2:单问题模式  3:测评任务模式
     */
    private Integer mode;

    /**
     * 分组指标： 1:组维度   2:个人维度
     */
    private Integer userType;

    /**
     * 测评任务模式：userType=1（组维度） 这个值是组名；userType=2（个人维度）这个值是用户账号 account；
     */
    private String markUser;

    /**
     * 标注人分组
     */
    private String userGroup;

    /**
     * 分组指标：标注目录策略id
     */
    private String strategyId;

    /**
     * 当前分组测评对象列表
     */
    List<MarkTargetEntity> markTargetList = new ArrayList<>();

    /**
     * 当前分组测评结果列表
     */
    List<MarkResultEntity> markResultList = new ArrayList<>();

    /**
     * 单query模式，按照策略id分组
     *
     * @param markResultEntities 标注结果
     * @param markTargetEntities 测评对象
     * @return 分组结果
     */
    public static List<MarkResultGroup> groupByStrategyId(List<MarkResultEntity> markResultEntities, List<MarkTargetEntity> markTargetEntities) {
        if (CollectionUtil.isEmpty(markResultEntities)) {
            return new ArrayList<>();
        }

        List<MarkResultGroup> groupList = new ArrayList<>();
        Map<String, List<MarkResultEntity>> markResultMap = markResultEntities.stream().collect(Collectors.groupingBy(MarkResultEntity::getStrategyId));
        for (Map.Entry<String, List<MarkResultEntity>> entry : markResultMap.entrySet()) {
            MarkResultGroup markResultGroup = new MarkResultGroup();
            markResultGroup.setStrategyId(entry.getKey());
            markResultGroup.setMarkResultList(entry.getValue());
            markResultGroup.setMarkTargetList(markTargetEntities.stream()
                    .filter(target -> target.getStrategyId().equals(entry.getKey()))
                    .collect(Collectors.toList()));
            markResultGroup.setMode(MarkMode.SINGLE);
            groupList.add(markResultGroup);
        }

        return groupList;
    }


    /**
     * 多query测评任务模式，按照标注用户+策略id 以及 标注用户组 + 策略id 两种维度分组
     */
    public static List<MarkResultGroup> groupByMission(List<StrategyConfig> strategyConfigs, List<GeneratedEvalMissionAssign> missionAssigns, List<MarkResultEntity> markResultEntities, List<MarkTargetEntity> markTargetEntities) {

        List<MarkResultGroup> resultGroups = new ArrayList<>();
        // 1): 按照用户组划分
        Map<String, List<GeneratedEvalMissionAssign>> groupByUserGroup = missionAssigns.stream().collect(Collectors.groupingBy(GeneratedEvalMissionAssign::getUserGroup));
        List<String> userGroupList = new ArrayList<>(groupByUserGroup.keySet());
        for (String userGroup : userGroupList) {
            for (StrategyConfig strategyConfig : strategyConfigs) {
                MarkResultGroup resultGroup = new MarkResultGroup();
                resultGroup.setUserType(MarkResultGroupType.TYPE_GROUP);
                resultGroup.setUserGroup(userGroup);
                resultGroup.setMarkUser(userGroup);
                resultGroup.setMode(MarkMode.MISSION);
                resultGroup.setStrategyId(strategyConfig.getId());
                resultGroups.add(resultGroup);
            }
        }

        // 2): 再按照人维度统计
        for (Map.Entry<String, List<GeneratedEvalMissionAssign>> entry : groupByUserGroup.entrySet()) {
            String userGroup = entry.getKey();
            List<String> accountList = entry.getValue().stream().map(GeneratedEvalMissionAssign::getAccount).distinct().toList();
            for (String account : accountList) {
                for (StrategyConfig strategyConfig : strategyConfigs) {
                    MarkResultGroup resultGroup = new MarkResultGroup();
                    resultGroup.setUserType(MarkResultGroupType.TYPE_USER);
                    resultGroup.setUserGroup(userGroup);
                    resultGroup.setMarkUser(account);
                    resultGroup.setMode(MarkMode.MISSION);
                    resultGroup.setStrategyId(strategyConfig.getId());
                    resultGroups.add(resultGroup);
                }
            }
        }

        // 3): 将标注结果和标注对象分配到对应的分组中
        for (MarkResultGroup group : resultGroups) {
            String strategyId = group.getStrategyId();
            String userGroup = group.getUserGroup();
            String markUser = group.getMarkUser();

            List<MarkResultEntity> filteredResults = markResultEntities.stream()
                    .filter(result ->
                            group.getUserType() == MarkResultGroupType.TYPE_GROUP ? result.getUserGroup().equals(userGroup) && result.getStrategyId().equals(strategyId) :
                                    result.getAccount().equals(markUser) && result.getUserGroup().equals(userGroup) && result.getStrategyId().equals(strategyId)
                    )
                    .collect(Collectors.toList());
            group.setMarkResultList(filteredResults);

            // 标注对象过滤
            List<MarkTargetEntity> filteredTargets = markTargetEntities.stream()
                    .filter(target -> group.getUserType() == MarkResultGroupType.TYPE_GROUP ? target.getUserGroup().equals(userGroup) && target.getStrategyId().equals(strategyId) :
                           target.getAccount().equals(markUser) && target.getStrategyId().equals(strategyId))
                    .collect(Collectors.toList());
            group.setMarkTargetList(filteredTargets);
        }

        return resultGroups;
    }
}
