package com.iflytek.lynxiao.eval.service.ascribe.impl;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.domain.VersionStatus;
import com.iflytek.lynxiao.eval.config.EvalProperties;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalPlanStatus;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalRecordDTO;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalRecordStatus;
import com.iflytek.lynxiao.eval.service.mark.EvalRecordService;
import com.iflytek.lynxiao.eval.utils.TaskSplitUtils;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalPlan;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalStrategyVersion;
import com.iflytek.lynxiao.eval.repository.EvalPlanRepository;
import com.iflytek.lynxiao.eval.repository.EvalStrategyVersionRepository;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.impl.WorkflowFlatInvokerImpl;
import jakarta.annotation.PostConstruct;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import skynet.boot.common.AsyncPipelineHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;

import static com.iflytek.lynxiao.eval.dto.ascribe.EvalRecordStatus.FAILED;
import static com.iflytek.lynxiao.eval.dto.ascribe.EvalRecordStatus.UNPROCESSED;

/**
 * <AUTHOR> 2025/2/22 13:34
 */

@Slf4j
@Component
public class EvalPlanPipelineHandler extends AsyncPipelineHandler<String, Object> {

    private final EvalProperties evalProperties;
    private final ExecutorService executor;
    private final EvalPlanRepository evalPlanRepository;
    private final EvalRecordService evalRecordService;
    private final WorkflowProcessService workflowProcessService;
    private final WorkflowFlatInvokerImpl workflowFlatInvokerImpl;
    private final EvalStrategyVersionRepository evalStrategyVersionRepository;

    public EvalPlanPipelineHandler(EvalProperties evalProperties, EvalPlanRepository evalPlanRepository, EvalRecordService evalRecordService,
                                   EvalStrategyVersionRepository evalStrategyVersionRepository, WorkflowProcessService workflowProcessService,
                                   WorkflowFlatInvokerImpl workflowFlatInvokerImpl) {
        this.executor = new ThreadPoolExecutor(
                evalProperties.getPlanSetting().getPartition(),
                evalProperties.getPlanSetting().getPartition(),
                0L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new BasicThreadFactory.Builder().namingPattern("eval-plan-%d").build(),
                new ThreadPoolExecutor.AbortPolicy()
        );
        this.evalProperties = evalProperties;
        this.evalStrategyVersionRepository = evalStrategyVersionRepository;
        this.evalPlanRepository = evalPlanRepository;
        this.evalRecordService = evalRecordService;
        this.workflowProcessService = workflowProcessService;
        this.workflowFlatInvokerImpl = workflowFlatInvokerImpl;
    }

    @SneakyThrows
    @PostConstruct
    public void init() {
        this.init(
                this.evalProperties.getPlanSetting().getLimitCount(),
                ""
        );

    }

    @Override
    protected void onInit(Object param) throws Exception {
        log.info("PlanAsyncPipelineHandler initialized.");
    }

    @Override
    protected void onEvent(String planId) {
        log.info("Start processing planId: {}", planId);
        Optional<GeneratedEvalPlan> evalPlanOp = evalPlanRepository.findById(Long.valueOf(planId));
        if (evalPlanOp.isEmpty()) {
            log.error("No plan found for planId: {}", planId);
            return;
        }
        GeneratedEvalPlan evalPlan = evalPlanOp.get();
        String execResult = null;
        evalPlanRepository.updateStatusAndResult(Long.valueOf(planId), null, null, Instant.now(), null);
        try {
            // step1: 获取拓扑流程
            WorkflowProcess workflowProcess = fetchWorkflowProcess(evalPlan);
            if (workflowProcess == null) return;

            // step2: 根据planId找出所有未处理、失败的记录
            Page<EvalRecordDTO> page = evalRecordService.find(planId, Pageable.unpaged(), Arrays.asList(UNPROCESSED, FAILED));
            if (page.isEmpty()) {
                log.info("No unprocessed or failed records found for planId: {}", planId);
                return;
            }
            List<EvalRecordDTO> recordList = page.getContent();

            // step3: 按照partition划分
            List<List<EvalRecordDTO>> splitTasks = TaskSplitUtils.splitTasks(recordList, this.evalProperties.getPlanSetting().getPartition());

            // Step 4: 并行处理每组任务
            List<CompletableFuture<List<EvalRecordDTO>>> futures = splitTasks.stream()
                    .map(task -> CompletableFuture.supplyAsync(() -> processTaskGroup(task, workflowProcess, evalPlan), executor))
                    .toList();

            // Step 5: 等待所有任务执行完成
            List<EvalRecordDTO> records = new ArrayList<>();
            for (CompletableFuture<List<EvalRecordDTO>> future : futures) {
                try {
                    records.addAll(future.get());
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error occurred while processing taskGroup for planId: {}, error: {}", planId, e.getMessage(), e);
                }
            }
            log.info("Finished processing planId: {}", planId);
            // 统计结果
            if (CollectionUtils.isNotEmpty(records)) {
                execResult = statisticResult(records);
            }
        } catch (Exception e) {
            log.error("Error processing planId: {}, error: {}", planId, e.getMessage());
        } finally {
            // step6: 修改分析计划的状态为已完成
            if (evalRecordService.find(planId, Pageable.unpaged(), Arrays.asList(UNPROCESSED, FAILED)).isEmpty()) {
                evalPlanRepository.updateStatusAndResult(Long.valueOf(planId), EvalPlanStatus.FINISH, execResult, null, Instant.now());
            } else {
                evalPlanRepository.updateStatusAndResult(Long.valueOf(planId), EvalPlanStatus.FINISH_WITH_FAILURE, execResult, null, Instant.now());
            }
        }
    }

    /**
     * 获取拓扑流程
     */
    private WorkflowProcess fetchWorkflowProcess(GeneratedEvalPlan evalPlan) {
        Optional<GeneratedEvalStrategyVersion> strategyVersionOpt = this.evalStrategyVersionRepository.findByProcessIdAndCategory(evalPlan.getProcessId(), evalPlan.getMode());
        if (strategyVersionOpt.isEmpty()) {
            log.error("Strategy version not found for planId: {}", evalPlan.getId());
            return null;
        }
        if (strategyVersionOpt.get().getStatus() != VersionStatus.PUBLISHED) {
            log.error("Strategy version is not enabled for planId: {}", evalPlan.getId());
            return null;
        }

        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(strategyVersionOpt.get().getProcessId());
        if (workflowProcess == null) {
            log.error("Workflow process not found for strategyVersionId: {}", strategyVersionOpt.get().getId());
        }
        return workflowProcess;
    }

    /**
     * 处理单个分组任务
     */
    private List<EvalRecordDTO> processTaskGroup(List<EvalRecordDTO> records, WorkflowProcess workflowProcess, GeneratedEvalPlan evalPlan) {
        List<EvalRecordDTO> results = new ArrayList<>();
        for (EvalRecordDTO record : records) {
            ApiRequest request = new ApiRequest();
            JSONObject input = record.getInput();
            input.put("processId", evalPlan.getSceneProcId());
            request.setPayload(input);
            log.info("apiRequest:{}", request);
            ApiResponse response = null;
            try {
                evalRecordService.updateTime(record.getId(), Instant.now(), null);
                response = workflowFlatInvokerImpl.invoke(workflowProcess, request);
                log.debug("eval plan execute finish, recordId: {}, response: {}", record.getId(), response);
                handleResponse(record, response);
            } catch (Exception e) {
                log.error("eval plan execute error:{}, recordId: {}, response: {}", e.getMessage(), record.getId(), response);
                evalRecordService.saveResult(record.getId(), null, null, e.getMessage(), EvalRecordStatus.FAILED);
            }
            results.add(record);
        }
        return results;
    }

    private void handleResponse(EvalRecordDTO record, ApiResponse response) {
        if (response.getHeader().getCode() != 0 || response.getPayload().getJSONObject("output").getJSONArray("evalResult") == null) {
            log.error("Error processing recordId: {}, response error: {}", record.getId(), response);
            evalRecordService.saveResult(record.getId(), null, null, response.getHeader().getMessage(), EvalRecordStatus.FAILED);
        } else {
            log.info("Success processing recordId: {}", record.getId());
            evalRecordService.saveResult(record.getId(),
                    response.getPayload().getJSONObject("output").getJSONArray("evalResult"),
                    response.getPayload().getJSONObject("output").getJSONArray("traceResult"),
                    null,
                    EvalRecordStatus.SUCCESS);
            record.setEvalResult(response.getPayload().getJSONObject("output").getJSONArray("evalResult"));
        }
    }

    private String statisticResult(List<EvalRecordDTO> records) {
        // key是节点名称 value是节点的统计结果
        Map<String, Integer> statisticMap = new LinkedHashMap<>();
        for (EvalRecordDTO record : records) {
            if (record.getEvalResult() == null) {
                continue;
            }
            for (int i = 0; i < record.getEvalResult().size(); i++) {
                JSONObject evalResultItem = record.getEvalResult().getJSONObject(i);
                String name = evalResultItem.getString("name");
                int value = evalResultItem.getIntValue("value");
                statisticMap.put(name, statisticMap.getOrDefault(name, 0) + value);
            }
        }
        return new JSONObject(statisticMap).toJSONString();
    }

    @Override
    protected void onClose() throws Exception {
        // 在关闭时需要执行的一些清理操作
        log.info("PlanAsyncPipelineHandler is closing...");
    }
}