package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalExpProdRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface EvalExpProdRepository extends GeneratedEvalExpProdRepository {
    boolean existsByNameOrCode(String name, String code);

    @Query("SELECT MAX(m.sort) FROM GeneratedEvalExpProd m")
    Integer findMaxSort();
}