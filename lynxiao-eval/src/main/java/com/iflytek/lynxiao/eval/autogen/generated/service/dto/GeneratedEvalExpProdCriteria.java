package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedEvalExpProdCriteria implements Serializable, Criteria  {

    public GeneratedEvalExpProdCriteria() {}

    public GeneratedEvalExpProdCriteria(GeneratedEvalExpProdCriteria other) {
    }

    @Override
    public GeneratedEvalExpProdCriteria copy() {
        return new GeneratedEvalExpProdCriteria(this);
    }
}
