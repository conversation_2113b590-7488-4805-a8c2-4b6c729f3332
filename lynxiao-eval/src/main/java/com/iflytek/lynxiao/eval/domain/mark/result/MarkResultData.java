package com.iflytek.lynxiao.eval.domain.mark.result;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultChatDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultQueryIgnoreDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultRecallDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultTraceDTO;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;


/**
 * 标注结果数据, 包括：最终召回结果、全链路结果、大模型推理结果
 *
 * <AUTHOR>  2025/5/19 10:43
 */
@Getter
@Setter
public class MarkResultData extends Jsonable {

    /**
     * 标注结果-最终召回文档
     */
    private MarkResultRecall recall;

    /**
     * 标注结果-全链路结果
     */
    private MarkResultTrace trace;

    /**
     * 标注结果-大模型推理结果
     */
    private MarkResultChat chat;

    /**
     * 标注结果-query类弃标标注
     */
    private MarkResultQueryIgnore queryIgnore;


    public static MarkResultData of(MarkResultRecallDTO dto) {
        MarkResultRecall markResultRecall = BeanUtil.copyProperties(dto, MarkResultRecall.class);
        MarkResultData markResultData = new MarkResultData();
        markResultData.setRecall(markResultRecall);
        return markResultData;
    }

    public static MarkResultData of(MarkResultChatDTO dto) {
        MarkResultChat chat = BeanUtil.copyProperties(dto, MarkResultChat.class);
        MarkResultData markResultData = new MarkResultData();
        markResultData.setChat(chat);
        return markResultData;
    }

    public static MarkResultData of(MarkResultTraceDTO dto) {
        MarkResultTrace trace = BeanUtil.copyProperties(dto, MarkResultTrace.class);
        MarkResultData markResultData = new MarkResultData();
        markResultData.setTrace(trace);
        return markResultData;
    }

    public static MarkResultData of(MarkResultQueryIgnoreDTO dto) {
        MarkResultQueryIgnore queryIgnore = BeanUtil.copyProperties(dto, MarkResultQueryIgnore.class);
        MarkResultData markResultData = new MarkResultData();
        markResultData.setQueryIgnore(queryIgnore);
        return markResultData;
    }


}