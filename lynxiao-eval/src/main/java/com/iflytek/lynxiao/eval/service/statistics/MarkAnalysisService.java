package com.iflytek.lynxiao.eval.service.statistics;


import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.data.dto.FlowVersionDTO;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultGroup;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultAnalysisDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

public interface MarkAnalysisService {

    /**
     * 分析标注结果
     *
     * @param resultGroup 标注结果分组
     * @param topK        需要分析的topK, 可选
     * @param missionDTO  测评任务
     * @return 标注结果分析
     */
    MarkResultAnalysisDTO analysis(MarkResultGroup resultGroup, Integer topK, EvalMissionDTO missionDTO);

    /**
     * 批量分析标注结果
     *
     * @param dto                     标注结果查询参数
     * @param resultGroupList         分组列表
     * @param missionDTO              测评任务
     * @param regionMap               区域映射
     * @param processIdFlowVersionMap 流程id与流程版本的映射
     * @param targetEntities          测评对象列表
     * @return
     */
    List<MarkResultAnalysisDTO> analysis(MarkResultQueryDTO dto, List<MarkResultGroup> resultGroupList, EvalMissionDTO missionDTO, Map<String,  LynxiaoFeignClientManager.MetaRegionFeign.MetaRegion> regionMap,
                                         Map<String, FlowVersionDTO> processIdFlowVersionMap, @NotNull List<MarkTargetEntity> targetEntities);
}
