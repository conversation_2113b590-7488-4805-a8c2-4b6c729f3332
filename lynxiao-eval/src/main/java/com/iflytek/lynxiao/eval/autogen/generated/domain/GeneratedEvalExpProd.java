package com.iflytek.lynxiao.eval.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 效果体验配置表
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "eval_exp_prod")
public class GeneratedEvalExpProd extends AbstractAuditingEntity<Long> {
    /**
     * 流程名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 流程id
     */
    @Column(name = "code")
    private String code;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;
    /**
     * 是否删除
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}