package com.iflytek.lynxiao.eval.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 测评字段配置表
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "eval_field_config")
public class GeneratedEvalFieldConfig extends AbstractAuditingEntity<Long> {
    /**
     * 字段名称，例如：标题、内容
     */
    @Column(name = "name")
    private String name;
    /**
     * 字段，例如：title、content
     */
    @Column(name = "field")
    private String field;
    /**
     * 字段类型，String、long等
     */
    @Column(name = "type")
    private String type;
    /**
     * 字段分类，例如：通用字段、医疗字段等
     */
    @Column(name = "category")
    private String category;
    /**
     * 排序
     */
    @Column(name = "idx")
    private Integer idx;
    /**
     * 字段路径，如果为空，默认从doc下取
     */
    @Column(name = "path")
    private String path;
    /**
     * 是否在全链路展示
     */
    @Column(name = "show_in_trace")
    private Boolean showInTrace;
    /**
     * 默认值
     */
    @Column(name = "default_value")
    private String defaultValue;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 样例
     */
    @Column(name = "sample")
    private String sample;
    /**
     * 是否删除
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}