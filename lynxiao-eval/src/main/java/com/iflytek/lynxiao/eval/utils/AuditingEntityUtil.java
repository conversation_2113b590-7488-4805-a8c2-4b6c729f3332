package com.iflytek.lynxiao.eval.utils;


import cn.hutool.core.lang.ObjectId;
import com.iflytek.skybox.contract.domain.BoxUserBase;
import com.iflytek.skybox.contract.service.BoxUserContextHolder;
import org.apache.commons.lang3.RandomStringUtils;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.time.Instant;

/**
 * 数据库实体工具类
 *
 * <AUTHOR>  2024/10/11 11:58
 */
public class AuditingEntityUtil {


    /**
     * 设置创建实体类通用参数：id、时间戳以及用户信息
     *
     * @param auditingEntity 实体类基类
     */
    public static void fillCreateValue(AbstractAuditingEntity<Long> auditingEntity) {
        Instant now = Instant.now();
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        auditingEntity.setId(null); // Ensure ID is null for new entities
        if (currentUser != null) {
            auditingEntity.setCreatedBy(currentUser.getName());
            auditingEntity.setLastModifiedBy(currentUser.getName());
            auditingEntity.setTenantId(currentUser.getTenantId());
        } else {
            auditingEntity.setCreatedBy("System");
            auditingEntity.setLastModifiedBy("System");
            auditingEntity.setTenantId("defaultTenantId"); // Adjust default if needed
        }
        auditingEntity.setCreatedDate(now);
        auditingEntity.setLastModifiedDate(now);
    }

    public static void fillCreateValueMongo(skynet.boot.mongo.domain.AbstractAuditingEntity<String> auditingEntity) {
        Instant now = Instant.now();
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();

        if (currentUser != null) {
            auditingEntity.setCreatedBy(currentUser.getName());
            auditingEntity.setLastModifiedBy(currentUser.getName());
            auditingEntity.setTenantId(currentUser.getTenantId());
        } else {
            auditingEntity.setCreatedBy("System");
            auditingEntity.setLastModifiedBy("System");
            auditingEntity.setTenantId("defaultTenantId"); // Adjust default if needed
        }
        auditingEntity.setId(ObjectId.next());
        auditingEntity.setCreatedDate(now);
        auditingEntity.setLastModifiedDate(now);
    }

    /**
     * 设置更新实体类通用参数，时间戳以及用户信息
     *
     * @param auditingEntity 实体类基类
     */
    public static void fillUpdateValue(AbstractAuditingEntity<Long> auditingEntity) {
        auditingEntity.setLastModifiedDate(Instant.now());
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        auditingEntity.setLastModifiedBy(currentUser == null ? "System" : currentUser.getName());
    }

    public static void fillUpdateValueMongo(skynet.boot.mongo.domain.AbstractAuditingEntity<String> auditingEntity) {
        auditingEntity.setLastModifiedDate(Instant.now());
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        auditingEntity.setLastModifiedBy(currentUser == null ? "System" : currentUser.getName());
    }


    /**
     * 获取当前用户名  没有则返回System
     *
     * @return
     */
    public static String getCurrentUserName() {
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        return currentUser == null ? "System" : currentUser.getName();
    }

    public static String generalRandomCode(String tag) {
        // 生成随机6位字符
        return tag.concat(RandomStringUtils.secure().nextAlphanumeric(6).toUpperCase());
    }

    public static String getCurrentAccount() {
        BoxUserBase currentUser = BoxUserContextHolder.getCurrentUser();
        if (currentUser != null) {
            return currentUser.getAccount();
        } else {
            return "shuaigu2";
//            throw new LynxiaoException("获取用户信息失败。");
        }
    }
}
