package com.iflytek.lynxiao.eval.autogen.generated.service.impl;

import com.iflytek.lynxiao.eval.autogen.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.eval.autogen.repository.EvalExpProdRepository;
import com.iflytek.lynxiao.eval.autogen.generated.service.GeneratedEvalExpProdService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdPatchDTO;
import com.iflytek.lynxiao.eval.autogen.generated.service.mapper.GeneratedEvalExpProdMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedEvalExpProdServiceImpl extends MysqlTemplateServiceImpl<GeneratedEvalExpProd, EvalExpProdDTO, Long> implements GeneratedEvalExpProdService {

    @Resource
    protected EvalExpProdRepository evalExpProdRepository;

    @Resource
    protected GeneratedEvalExpProdMapper evalExpProdMapper;


    public GeneratedEvalExpProdServiceImpl(EntityMapper<EvalExpProdDTO, GeneratedEvalExpProd> entityMapper, JpaSpecificationExecutor<GeneratedEvalExpProd> jpaSpecificationExecutor, JpaRepository<GeneratedEvalExpProd, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<EvalExpProdDTO> findAllByCriteria(EvalExpProdCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<EvalExpProdDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<EvalExpProdDTO> findAllByCriteria(EvalExpProdCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<EvalExpProdDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedEvalExpProd> createSpecification(Criteria criteria) {
        EvalExpProdCriteria evalExpProdCriteria = (EvalExpProdCriteria) criteria;
        Specification<GeneratedEvalExpProd> specification = Specification.where(null);
        if (evalExpProdCriteria != null) {
        }
        return specification;
    }

    @Override
    public EvalExpProdDTO findById(Long id) {
        EvalExpProdDTO evalExpProdDTO = super.findById(id);
        return evalExpProdDTO;
    }

    @Override
    public EvalExpProdDTO save(EvalExpProdDTO evalExpProdDTO) {
        return super.save(evalExpProdDTO);
    }

    @Override
    public EvalExpProdDTO update(EvalExpProdDTO evalExpProdDTO) {

        return super.update(evalExpProdDTO.getId(),evalExpProdDTO);
    }

    @Override
    public EvalExpProdDTO patch(EvalExpProdPatchDTO evalExpProdPatchDTO) {
        return super.patch(evalExpProdPatchDTO.getId(), evalExpProdPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public EvalExpProdDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedEvalExpProd.class);
    }
}