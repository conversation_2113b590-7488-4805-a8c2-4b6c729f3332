package com.iflytek.lynxiao.eval.autogen.generated.controller;

import com.iflytek.lynxiao.eval.autogen.service.EvalExpProdService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdPatchDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 效果体验配置表管理
 */
public class GeneratedEvalExpProdController {

    @Resource
    protected EvalExpProdService evalExpProdService;
}