package com.iflytek.lynxiao.eval.dto.mark.record;

import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultQueryIgnoreDTO;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;


/**
 * 保存测评记录
 */
@Setter
@Getter
public class MarkRecordSaveDTO extends Jsonable {

    /**
     * 测评记录id
     */
    private String id;

    /**
     * 测评的标注对象列表
     */
    private List<MarkTargetRecordSaveDTO> targets;

    /**
     * query类弃标标注
     */
    private MarkResultQueryIgnoreDTO queryIgnore;

    /**
     * 是否进行标注检查
     */
    private boolean completeCheck = false;

}
