package com.iflytek.lynxiao.eval.repository;


import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.lynxiao.eval.domain.MarkResultGroupType;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultRecall;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultRecallAscribe;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultType;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.mongodb.client.result.DeleteResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class MarkResultRepository {

    /**
     * mongo集合名称
     */
    private static final String COL_NAME = "eval_mark_result";

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;


    public List<String> saveList(List<MarkResultEntity> entityList) {
        List<String> ids = new ArrayList<>();
        for (MarkResultEntity entity : entityList) {
            MarkResultEntity saved = platformMongoTemplate.save(entity);
            ids.add(saved.getId());
        }
        return ids;
    }

    public String save(MarkResultEntity entity) {
        MarkResultEntity saved = platformMongoTemplate.save(entity);
        return saved.getId();
    }

    public void saveAscribeResult(MarkResultEntity markResultEntity, MarkResultRecallAscribe ascribe, int ascribeMode) {
        if (markResultEntity == null) {
            return;
        }
        MarkResultRecall markResultRecall = markResultEntity.getData().getRecall();
        markResultEntity.setAscribeMode(ascribeMode);
        markResultRecall.setAscribe(ascribe);
        this.save(markResultEntity);
    }

    public List<MarkResultEntity> findByMarkRecordId(String id) {
        return platformMongoTemplate.find(Query.query(Criteria.where("mark_record_id").is(id)), MarkResultEntity.class);
    }

    public List<MarkResultEntity> findTraceByMarkRecordIdAndTargetId(String markRecordId, String targetId) {
        return platformMongoTemplate.find(Query.query(
                Criteria.where("mark_record_id").is(markRecordId)
                        .and("mark_target_id").is(targetId)
                        .and("type").is(MarkResultType.TRACE.getCode())), MarkResultEntity.class);
    }

    public MarkResultEntity findNoResultByMarkRecordIdAndTargetId(String markRecordId, String targetId) {
        return platformMongoTemplate.findOne(Query.query(Criteria.where("mark_record_id").is(markRecordId)
                .and("target_id").is(targetId)), MarkResultEntity.class);
    }

    public MarkResultEntity findById(String resultId) {
        return platformMongoTemplate.findById(resultId, MarkResultEntity.class);
    }

    public List<MarkResultEntity> findRecallByStrategyId(String recordId, String strategyId) {
        return this.findByTypeAndStrategyId(recordId, strategyId, MarkResultType.RECALL.getCode());
    }


    public MarkResultEntity findChatByTargetId(String targetId) {
        return platformMongoTemplate.findOne(Query.query(Criteria.where("mark_target_id").is(targetId)
                .and("type").is(MarkResultType.CHAT.getCode())), MarkResultEntity.class);
    }


    public void deleteById(String id) {

        if (StringUtils.isBlank(id)) {
            log.warn("Attempted to delete MarkResultEntity with null or empty id.");
            return;
        }

        MarkResultEntity entity = new MarkResultEntity();
        entity.setId(id);
        this.platformMongoTemplate.remove(entity);
    }

    public void deleteByRecordId(String recordId) {
        DeleteResult result = platformMongoTemplate.remove(Query.query(Criteria.where("mark_record_id").is(recordId)), MarkResultEntity.class);
        log.info("delete mark result mark_record_id:{}, count:{}", recordId, result.getDeletedCount());
    }

    public void deleteByRecordIdWithoutQueryIgnore(String recordId) {
        DeleteResult result = platformMongoTemplate.remove(Query.query(Criteria.where("mark_record_id").is(recordId)
                .and("type").ne(MarkResultType.QUERY_IGNORE.getCode())), MarkResultEntity.class);
        log.info("delete mark result mark_record_id:{}, count:{}", recordId, result.getDeletedCount());
    }

    public void deleteQueryIgnoreByRecordId(String recordId) {
        DeleteResult result = platformMongoTemplate.remove(Query.query(Criteria.where("mark_record_id").is(recordId)
                .and("type").is(MarkResultType.QUERY_IGNORE.getCode())), MarkResultEntity.class);
        log.info("delete mark result(mark type is query ignore) mark_record_id:{}, count:{}", recordId, result.getDeletedCount());
    }

    /**
     * 获取策略标注结果列表
     */
    public List<MarkResultEntity> findRecallList(MarkResultQueryDTO dto) {
        Query query = getQueryConditionQuery(dto);

        if (CollectionUtil.isEmpty(dto.getTypes())) {
            query.addCriteria(Criteria.where("type").is(MarkResultType.RECALL.getCode()));
        }
        return platformMongoTemplate.find(query, MarkResultEntity.class, COL_NAME);
    }

    public List<MarkResultEntity> findAllSceneProcessId(MarkResultQueryDTO dto) {
        Query query = getQueryConditionQuery(dto);
        query.fields().include("scene_process_id").include("mission_id");
        return platformMongoTemplate.find(query, MarkResultEntity.class, COL_NAME);
    }


    public List<MarkResultEntity> findPage(MarkResultQueryDTO dto, Pageable pageable) {
        Query query = getQueryConditionQuery(dto);
        query.with(pageable);
        return platformMongoTemplate.find(query, MarkResultEntity.class, COL_NAME);
    }

    public long count(MarkResultQueryDTO dto) {
        Query query = getQueryConditionQuery(dto);
        return platformMongoTemplate.count(query, MarkResultEntity.class, COL_NAME);
    }

    private List<MarkResultEntity> findByTypeAndStrategyId(String recordId, String strategyId, int type) {
        return platformMongoTemplate.find(
                Query.query(Criteria.where("mark_record_id").is(recordId)
                        .and("strategy_id").is(strategyId)
                        .and("type").is(type)),
                MarkResultEntity.class
        );
    }


    private Query getQueryConditionQuery(MarkResultQueryDTO dto) {
        // 构建查询条件
        Query query = new Query();

        // 多query标注任务模式, 从某一列点入查看标注详情
        if (StringUtils.isNotBlank(dto.getColumn())) {
            if (MarkResultGroupType.TYPE_GROUP == dto.getUserType()) {
                query.addCriteria(Criteria.where("user_group").is(dto.getMarkUser()));
            } else if (MarkResultGroupType.TYPE_USER == dto.getUserType()) {
                query.addCriteria(Criteria.where("account").is(dto.getMarkUser()));
            }
        }
        if (dto.getRegionCode() != null) {
            query.addCriteria(Criteria.where("region_code").is(dto.getRegionCode()));
        }

        if (StringUtils.isNotBlank(dto.getUserGroup())) {
            query.addCriteria(Criteria.where("user_group").is(dto.getUserGroup()));
        }

        if (dto.getMarkRecordIds() != null) {
            query.addCriteria(Criteria.where("mark_record_id").in(dto.getMarkRecordIds()));
        }

        if (StringUtils.isNotBlank(dto.getMissionId())) {
            query.addCriteria(Criteria.where("mission_id").is(dto.getMissionId()));
        }

        if (StringUtils.isNotBlank(dto.getSearch())) {
            Criteria criteria1 = Criteria.where("query").regex(".*" + dto.getSearch() + ".*");
            Criteria criteria2 = Criteria.where("account").is(dto.getSearch());
            query.addCriteria(new Criteria().orOperator(criteria1, criteria2));
        }
        if (StringUtils.isNotBlank(dto.getStartTime()) && StringUtils.isNotBlank(dto.getEndTime())) {
            query.addCriteria(new Criteria().andOperator(
                    Criteria.where("created_date").gte(getInstant(dto.getStartTime())),
                    Criteria.where("created_date").lte(getInstant(dto.getEndTime())))
            );
        }
        if (CollectionUtil.isNotEmpty(dto.getTypes())) {
            List<Integer> types = dto.getTypes().stream().map(Integer::valueOf).toList();
            query.addCriteria(Criteria.where("type").in(types));
        }
        if (StringUtils.isNotBlank(dto.getSceneProcessId())) {
            query.addCriteria(Criteria.where("scene_process_id").is(dto.getSceneProcessId()));
        }
        if (CollectionUtil.isNotEmpty(dto.getStrategyIds())) {
            query.addCriteria(Criteria.where("strategy_id").in(dto.getStrategyIds()));
        }

        return query;
    }

    private static Instant getInstant(String time) {
        LocalDate endLocalDate = LocalDate.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
    }
}
