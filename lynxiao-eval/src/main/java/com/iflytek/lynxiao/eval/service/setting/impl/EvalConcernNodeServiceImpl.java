package com.iflytek.lynxiao.eval.service.setting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalConcernNode;
import com.iflytek.lynxiao.eval.repository.EvalConcernNodeRepository;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeSaveDTO;
import com.iflytek.lynxiao.eval.service.setting.EvalConcernNodeService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 关注节点配置表管理实现
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalConcernNodeServiceImpl implements EvalConcernNodeService {

    private final EvalConcernNodeRepository concernNodeRepository;

    public EvalConcernNodeServiceImpl(EvalConcernNodeRepository concernNodeRepository) {
        this.concernNodeRepository = concernNodeRepository;
    }

    @PostConstruct
    public void initCache() {
        log.info("初始化关注节点配置缓存");
        WorkflowUtil.updateCache(list());
        log.info("初始化关注节点配置缓存完成，缓存数量：{}", WorkflowUtil.concernNodeCacheList.size());
    }

    @Override
    public void save(EvalConcernNodeSaveDTO dto) {
        log.info("保存关注节点配置，参数：{}", dto);

        GeneratedEvalConcernNode entity;
        if (StrUtil.isNotBlank(dto.getId())) {
            // 更新
            Optional<GeneratedEvalConcernNode> optional = concernNodeRepository.findById(Long.valueOf(dto.getId()));
            if (optional.isEmpty()) {
                throw new LynxiaoException("记录不存在！");
            }
            entity = optional.get();
            BeanUtil.copyProperties(dto, entity, "id");
            AuditingEntityUtil.fillUpdateValue(entity);
        } else {
            // 创建
            //判断code是否重名
            long sameCodeCount = this.concernNodeRepository.countByCodeAndDeletedFalse(dto.getCode());
            if (sameCodeCount > 0) {
                throw new LynxiaoException("关注流程节点Code已存在，请修改后重试！");
            }
            //查询最大排序值
            Integer maxSort = concernNodeRepository.findMaxSort();

            entity = new GeneratedEvalConcernNode();
            BeanUtil.copyProperties(dto, entity);
            entity.setSort(maxSort == null ? 10 : maxSort + 10); // 默认排序值为最大值加10
            AuditingEntityUtil.fillCreateValue(entity);
        }

        concernNodeRepository.save(entity);
        WorkflowUtil.updateCache(list());
    }

    @Override
    public Page<EvalConcernNodeDTO> findByPage(String search, Pageable pageable) {
        log.info("分页查询关注节点配置，搜索条件：{}，分页参数：{}", search, pageable);

        // 构建查询条件
        Specification<GeneratedEvalConcernNode> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 搜索条件
            if (StrUtil.isNotBlank(search)) {
                Predicate namePredicate = criteriaBuilder.like(root.get("name"), "%" + search + "%");
                Predicate codePredicate = criteriaBuilder.like(root.get("code"), "%" + search + "%");
                predicates.add(criteriaBuilder.or(namePredicate, codePredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<GeneratedEvalConcernNode> entityPage = concernNodeRepository.findAll(spec, pageable);
        List<EvalConcernNodeDTO> dtoList = entityPage.getContent().stream()
                .map(this::convertToPortalDTO)
                .sorted(Comparator.comparingInt(EvalConcernNodeDTO::getSort))
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, entityPage.getTotalElements());
    }

    @Override
    public List<EvalConcernNodeDTO> list() {
        log.info("查询关注节点配置列表");

        List<GeneratedEvalConcernNode> entityList = concernNodeRepository.findAll();
        return entityList.stream()
                .map(this::convertToPortalDTO)
                .sorted(Comparator.comparingInt(EvalConcernNodeDTO::getSort))
                .collect(Collectors.toList());
    }

    @Override
    public EvalConcernNodeDTO findById(String id) {
        log.info("查询关注节点配置详情，id：{}", id);

        Optional<GeneratedEvalConcernNode> optional = concernNodeRepository.findById(Long.valueOf(id));
        if (optional.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }

        return convertToPortalDTO(optional.get());
    }

    @Override
    public void deleteById(String id) {
        log.info("删除关注节点配置，id：{}", id);

        Optional<GeneratedEvalConcernNode> optional = concernNodeRepository.findById(Long.valueOf(id));
        if (optional.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }
        concernNodeRepository.deleteById(Long.valueOf(id));
        WorkflowUtil.updateCache(list());
    }

    @Override
    public void sort(List<String> ids) {
        log.info("排序关注节点配置，ids：{}", ids);

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<GeneratedEvalConcernNode> entities = concernNodeRepository.findAll();
        if (entities.isEmpty()) {
            log.warn("关注节点配置列表为空，无法排序");
            return;
        }

        AtomicReference<Integer> sort = new AtomicReference<>(10);
        ids.forEach(t -> {
            for (GeneratedEvalConcernNode obj : entities) {
                if (String.valueOf(obj.getId()).equals(t)) {
                    obj.setSort(sort.get());
                    break;
                }
            }
            sort.set(sort.get() + 10);
        });

        concernNodeRepository.saveAll(entities);
        WorkflowUtil.updateCache(list());
    }

    /**
     * 转换为Portal DTO
     */
    private EvalConcernNodeDTO convertToPortalDTO(GeneratedEvalConcernNode entity) {
        EvalConcernNodeDTO dto = new EvalConcernNodeDTO();
        BeanUtil.copyProperties(entity, dto);
        dto.setId(entity.getId().toString());
        return dto;
    }
}
