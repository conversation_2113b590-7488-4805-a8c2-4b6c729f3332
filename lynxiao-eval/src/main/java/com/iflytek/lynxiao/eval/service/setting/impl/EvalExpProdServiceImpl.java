package com.iflytek.lynxiao.eval.service.setting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalExpProd;
import com.iflytek.lynxiao.eval.repository.EvalExpProdRepository;
import com.iflytek.lynxiao.eval.dto.standard.EvalExpProdDTO;
import com.iflytek.lynxiao.eval.dto.standard.EvalExpProdSaveDTO;
import com.iflytek.lynxiao.eval.service.setting.EvalExpProdService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 效果体验配置表管理实现
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalExpProdServiceImpl implements EvalExpProdService {

    private final EvalExpProdRepository evalExpProdRepository;

    public EvalExpProdServiceImpl(EvalExpProdRepository evalExpProdRepository) {
        this.evalExpProdRepository = evalExpProdRepository;
    }

    @Override
    public void save(EvalExpProdSaveDTO dto) {
        log.info("保存效果体验配置，参数：{}", dto);

        GeneratedEvalExpProd entity;
        if (StrUtil.isNotBlank(dto.getId())) {
            // 更新
            Optional<GeneratedEvalExpProd> optional = evalExpProdRepository.findById(Long.valueOf(dto.getId()));
            if (optional.isEmpty()) {
                throw new LynxiaoException("记录不存在！");
            }
            entity = optional.get();

            // 检查修改的名字、流程ID是否重复
            if (!dto.getName().equals(entity.getName()) && evalExpProdRepository.existsByNameOrCode(dto.getName(), "")) {
                //查找是否有同名的记录
                throw new LynxiaoException("新名称已存在，请修改后重试！");
            }

            if (!dto.getCode().equals(entity.getCode()) && evalExpProdRepository.existsByNameOrCode("", dto.getCode())) {
                //查找是否有同名的记录
                throw new LynxiaoException("流程id已存在，请修改后重试！");
            }

            BeanUtil.copyProperties(dto, entity, "id");
            AuditingEntityUtil.fillUpdateValue(entity);
        } else {
            // 创建

            //名称 或 流程id不允许重复
            if (evalExpProdRepository.existsByNameOrCode(dto.getName(), dto.getCode())) {
                throw new LynxiaoException("名称或流程ID已存在，请修改后重试！");
            }

            //查询最大排序值
            Integer maxSort = evalExpProdRepository.findMaxSort();

            entity = BeanUtil.copyProperties(dto, GeneratedEvalExpProd.class);
            entity.setSort(maxSort == null ? 10 : maxSort + 10); // 默认排序值为最大值加10
            AuditingEntityUtil.fillCreateValue(entity);
        }

        evalExpProdRepository.save(entity);
    }

    @Override
    public Page<EvalExpProdDTO> findByPage(String search, Pageable pageable) {
        log.info("分页查询效果体验配置，搜索条件：{}，分页参数：{}", search, pageable);

        // 构建查询条件
        Specification<GeneratedEvalExpProd> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 搜索条件
            if (StrUtil.isNotBlank(search)) {
                Predicate namePredicate = criteriaBuilder.like(root.get("name"), "%" + search + "%");
                Predicate processIdPredicate = criteriaBuilder.like(root.get("processId"), "%" + search + "%");
                predicates.add(criteriaBuilder.or(namePredicate, processIdPredicate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<GeneratedEvalExpProd> entityPage = evalExpProdRepository.findAll(spec, pageable);
        List<EvalExpProdDTO> dtoList = entityPage.getContent().stream()
                .map(this::convertToPortalDTO)
                .sorted(Comparator.comparingInt(EvalExpProdDTO::getSort))
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, entityPage.getTotalElements());
    }

    @Override
    public List<EvalExpProdDTO> list() {
        log.info("查询效果体验配置列表");

        List<GeneratedEvalExpProd> entityList = evalExpProdRepository.findAll();
        return entityList.stream()
                .map(this::convertToPortalDTO)
                .filter(EvalExpProdDTO::getEnabled)
                .sorted(Comparator.comparingInt(EvalExpProdDTO::getSort))
                .collect(Collectors.toList());
    }

    @Override
    public EvalExpProdDTO findById(String id) {
        log.info("查询效果体验配置详情，id：{}", id);

        Optional<GeneratedEvalExpProd> optional = evalExpProdRepository.findById(Long.valueOf(id));
        if (optional.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }

        return convertToPortalDTO(optional.get());
    }

    @Override
    public void deleteById(String id) {
        log.info("删除效果体验配置，id：{}", id);

        Optional<GeneratedEvalExpProd> optional = evalExpProdRepository.findById(Long.valueOf(id));
        if (optional.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }

        evalExpProdRepository.deleteById(Long.valueOf(id));
    }

    @Override
    public void updateEnabled(String id, boolean enabled) {
        log.info("更新效果体验配置启用状态，id：{}，enabled：{}", id, enabled);

        Optional<GeneratedEvalExpProd> optional = evalExpProdRepository.findById(Long.valueOf(id));
        if (optional.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }

        GeneratedEvalExpProd entity = optional.get();
        entity.setEnabled(enabled);
        AuditingEntityUtil.fillUpdateValue(entity);
        evalExpProdRepository.save(entity);
    }


    @Override
    public void sort(List<String> ids) {
        log.info("排序效果体验配置，ids：{}", ids);

        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<GeneratedEvalExpProd> entities = evalExpProdRepository.findAll();
        if (entities.isEmpty()) {
            log.warn("没有找到任何效果体验配置记录，无法进行排序");
            return;
        }

        AtomicReference<Integer> sort = new AtomicReference<>(10);
        ids.forEach(t -> {
            for (GeneratedEvalExpProd obj : entities) {
                if (String.valueOf(obj.getId()).equals(t)) {
                    obj.setSort(sort.get());
                    break;
                }
            }
            sort.set(sort.get() + 10);
        });

        evalExpProdRepository.saveAll(entities);
    }

    /**
     * 转换为Portal DTO
     */
    private EvalExpProdDTO convertToPortalDTO(GeneratedEvalExpProd entity) {
        EvalExpProdDTO dto = new EvalExpProdDTO();
        BeanUtil.copyProperties(entity, dto);
        dto.setId(entity.getId().toString());
        return dto;
    }
}
