package com.iflytek.lynxiao.eval.autogen.service.impl;

import com.iflytek.lynxiao.eval.autogen.service.EvalExpProdService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdDTO;
import skynet.boot.common.mapper.EntityMapper;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalExpProd;
import com.iflytek.lynxiao.eval.autogen.generated.service.impl.GeneratedEvalExpProdServiceImpl;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Deprecated
public class EvalExpProdServiceImpl extends GeneratedEvalExpProdServiceImpl implements EvalExpProdService {

    public EvalExpProdServiceImpl(EntityMapper<EvalExpProdDTO, GeneratedEvalExpProd> entityMapper, JpaSpecificationExecutor<GeneratedEvalExpProd> jpaSpecificationExecutor, JpaRepository<GeneratedEvalExpProd, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }
}