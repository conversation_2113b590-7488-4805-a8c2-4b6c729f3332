package com.iflytek.lynxiao.eval.controller.mark;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordCreateDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordSaveDTO;
import com.iflytek.lynxiao.eval.service.mark.MarkRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 测评记录管理
 */
@Tag(name = "测评记录管理")
@RestController
@RequestMapping("/eval/api/v1/mark-record")
@EnableSkynetSwagger2
public class MarkRecordController {

    private final MarkRecordService markRecordService;

    public MarkRecordController(MarkRecordService markRecordService) {
        this.markRecordService = markRecordService;
    }

    @Operation(summary = "创建标注记录-执行搜索")
    @PostMapping(value = "/_create")
    public ApiResponse create(@Validated @RequestBody MarkRecordCreateDTO dto) {
        String id = this.markRecordService.create(dto);
        return new ApiResponse(JSONObject.of("data", id));
    }

    @Operation(summary = "查询")
    @GetMapping(value = "/item/{id}")
    public ApiResponse find(@PathVariable String id, @RequestParam boolean recall, @RequestParam boolean trace, @RequestParam boolean chat) {
        MarkRecordDTO dto = this.markRecordService.find(id, recall, trace, chat);
        return new ApiResponse(JSONObject.of("data", dto));
    }

    @Operation(summary = "查询未完成的标注记录")
    @GetMapping(value = "/unfinished")
    public ApiResponse findUnfinished(@RequestParam(name = "missionId", required = false) String missionId) {
        MarkRecordDTO dto = this.markRecordService.findUnfinished(missionId);
        return new ApiResponse(JSONObject.of("data", dto));
    }

    @Operation(summary = "保存标注记录")
    @PostMapping(value = "/save")
    public ApiResponse save(@Validated @RequestBody MarkRecordSaveDTO dto) {
        return new ApiResponse(JSONObject.of("data", this.markRecordService.save(dto)));
    }

    @Operation(summary = "删除指定标注结果")
    @DeleteMapping(value = "/by-result/{resultId}")
    public ApiResponse deleteMarkResult(@PathVariable String resultId) {
        this.markRecordService.deleteMarkResult(resultId);
        return new ApiResponse();
    }

    @Operation(summary = "归因分析")
    @PostMapping(value = "/ascribe/{id}")
    public ApiResponse ascribe(@PathVariable String id) {
        this.markRecordService.ascribe(id);
        return new ApiResponse();
    }

}
