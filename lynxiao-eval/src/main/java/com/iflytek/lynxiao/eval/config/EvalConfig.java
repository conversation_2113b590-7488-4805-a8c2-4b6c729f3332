package com.iflytek.lynxiao.eval.config;

import com.iflytek.lynxiao.common.annotation.EnableDatashard;
import com.iflytek.lynxiao.common.config.CommonAutoConfiguration;
import com.iflytek.lynxiao.eval.component.core.ElasticClientFactory;
import com.iflytek.skybox.contract.annotation.EnableSkyboxClient;
import com.iflytek.turing.gateway.annotation.EnableTuringGatewayFeign;
import com.iflytek.turing.planet.client.annotation.EnableTuringPlanetClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import skynet.boot.annotation.EnableSkynetMongo;
import skynet.boot.annotation.EnableSkynetSecurity;
import skynet.boot.exception.config.SkynetExceptionAutoConfiguration;
import skynet.boot.pandora.annotation.EnableSkynetPandora;
import skynet.boot.pandora.ogma.annotation.EnableSkynetPandoraOgma;

/**
 * <AUTHOR>
 */
@EnableDatashard
@EnableTuringPlanetClient
@EnableSkyboxClient
@EnableSkynetMongo
@EnableSkynetSecurity
@EnableSkynetPandora
@EnableSkynetPandoraOgma
@EnableTuringGatewayFeign
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter({SkynetExceptionAutoConfiguration.class, CommonAutoConfiguration.class})
public class EvalConfig {

    @Value("${lynxiao.platform-mongo.uri}")
    private String platformMongoUri;

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.eval")
    public EvalProperties evalProperties() {
        return new EvalProperties();
    }

    @Primary
    @Bean
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory, MongoConverter mongoConverter) {
        return new MongoTemplate(mongoDatabaseFactory, mongoConverter);
    }

    @Bean(name = "platformMongoTemplate")
    public MongoTemplate platformMongoTemplate() {
        return new MongoTemplate(new SimpleMongoClientDatabaseFactory(platformMongoUri));
    }


    @Bean(name = "platformGridFsTemplate")
    public GridFsTemplate platformGridFsTemplate(@Qualifier("platformMongoTemplate") MongoTemplate platformMongoTemplate,
                                                 MongoConverter mongoConverter) {
        MongoDatabaseFactory factory = platformMongoTemplate.getMongoDatabaseFactory();
        return new GridFsTemplate(factory, mongoConverter);
    }

    @Bean
    public ElasticClientFactory elasticClientFactory(EvalProperties evalProperties) {
        return new ElasticClientFactory(evalProperties);
    }
}
