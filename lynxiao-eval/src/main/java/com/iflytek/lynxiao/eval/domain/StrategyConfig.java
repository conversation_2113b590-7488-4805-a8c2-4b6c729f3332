package com.iflytek.lynxiao.eval.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 标注目录-策略配置
 */
@Setter
@Getter
public class StrategyConfig extends Jsonable {

    /**
     * 策略id
     */
    private String id;

    /**
     * 策略名词 策略A
     */
    private String name;

    /**
     * 场景策略或产品方案流程id
     */
    private String processId;

    private String processName;

    /**
     * 场景策略或产品方案流程 入参
     */
    private JSONObject processParams = new JSONObject();

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 归因模式   0: 竞品good、1: 自研 bad
     */
    private int ascribeMode;

    /**
     * 归因分析流程id
     */
    private String ascribeProcessId;

    /**
     * 归因分析中需要执行的场景策略id，
     * 如果是badUrl则此id为场景策略id；
     * 如果是goodUrl则此id为用户选择的竞对场景策略id（注意不是本类中的id，本类中的id可能是聚合搜索场景id，也可能没有，比如已关联离线数据）
     */
    private String id4Ascribe;

    /**
     * topK
     */
    private int topK;

    /**
     * 是否为离线策略
     */
    private boolean offline;

    /**
     * 竞品名称
     */
    private String compName;

    /**
     * query集id
     */
    private String queryGroupId;

}
