package com.iflytek.lynxiao.eval.domain.mark;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.utils.EvalUtils;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.iflytek.lynxiao.eval.utils.WorkflowUtil.AI_PROCESS;
import static com.iflytek.lynxiao.eval.utils.WorkflowUtil.AI_RERANK;

/**
 * 最终召回文档
 *
 * <AUTHOR>  2025/3/6 11:00
 */
@Getter
@Setter
public class MarkTargetRecall extends Jsonable {

    /**
     * 原始召回doc
     */
    private JSONObject doc;

    /**
     * 各阶段得分与位次
     */
    private List<ScoringRank> scoringRanks = new ArrayList<>();


    /**
     * 根据组件调用轨迹结果，构建MarkTargetRecall
     *
     * @param recallDoc 召回文档
     * @param traces    全链路调用轨迹
     * @return 对象
     */
    public static MarkTargetRecall of(JSONObject recallDoc, List<TraceLogItem> traces) {
        // doc中没有url则新增
        if (StringUtils.isBlank(recallDoc.getString("url"))) {
            recallDoc.put("url", EvalUtils.buildUrl(recallDoc));
        }

        MarkTargetRecall markTargetRecall = new MarkTargetRecall();
        markTargetRecall.setDoc(recallDoc);

        if (CollectionUtils.isEmpty(traces)) {
            // 没有全链路调用轨迹，直接返回
            return markTargetRecall;
        }

        // 解析召回文档在不同组件中的打分和位次
        for (TraceLogItem trace : traces) {
            Optional<EvalConcernNodeDTO> concernNodeDTOOptional = WorkflowUtil.findByCodeWithScoreAndIndex(trace.getCode());

            if (trace.getCode().equals(AI_PROCESS)) {
                //特殊处理后处理节点
                detailAiProcess(markTargetRecall, trace, recallDoc);
            }

            if (concernNodeDTOOptional.isEmpty()) {
                // 不是需要分析的组件
                continue;
            }

            EvalConcernNodeDTO concernNodeDTO = concernNodeDTOOptional.get();

            // 遍历本组件中的doc列表，取出文档得分和位次， 不在当前组件doc列表中 得分位次设置为默认值
            ScoringRank scoringRank = new ScoringRank();
            scoringRank.setCode(trace.getCode());
            scoringRank.setName(trace.getName());
            for (JSONObject traceDoc : trace.getDocs()) {
                if (!traceDoc.getString("id").equals(recallDoc.getString("id"))) {
                    continue;
                }
                scoringRank.setIndex(traceDoc.getIntValue(concernNodeDTO.getIndexField()));
                scoringRank.setScore(traceDoc.getDoubleValue(concernNodeDTO.getScoreField()));

                //重排节点需要获取重排props
                if (trace.getCode().equals(AI_RERANK)) {
                    ReRankProps reRankProps = ReRankProps.of(traceDoc);
                    scoringRank.setProps(reRankProps);
                }
            }
            markTargetRecall.getScoringRanks().add(scoringRank);
        }
        return markTargetRecall;
    }

    public static List<MarkTargetRecall> of(List<JSONObject> recallDoc, List<TraceLogItem> traces) {

        if (CollectionUtils.isEmpty(recallDoc)) {
            return new ArrayList<>(0);
        }

        List<MarkTargetRecall> markTargetRecalls = new ArrayList<>();
        for (JSONObject doc : recallDoc) {
            markTargetRecalls.add(MarkTargetRecall.of(doc, traces));
        }
        return markTargetRecalls;
    }


    /**
     * 特殊处理后处理环节
     * 特殊点： 后处理环节没有得分和位次字段   只解析召回doc在后处理结果中的位次
     */
    private static void detailAiProcess(MarkTargetRecall markTargetRecall, TraceLogItem trace, JSONObject recallDoc) {
        ScoringRank scoringRank = new ScoringRank();
        scoringRank.setCode(trace.getCode());
        scoringRank.setName(trace.getName());
        List<JSONObject> traceDocs = trace.getDocs();
        for (int i = 0; i < traceDocs.size(); i++) {
            if (!traceDocs.get(i).getString("id").equals(recallDoc.getString("id"))) {
                continue;
            }
            //位次从1开始
            scoringRank.setIndex(i + 1);
            scoringRank.setScore(null);
        }
        markTargetRecall.getScoringRanks().add(scoringRank);
    }

}
