package com.iflytek.lynxiao.eval.dto.mission;

import com.iflytek.lynxiao.eval.domain.DocClassifyRule;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionGroupDetailDTO;
import com.iflytek.lynxiao.eval.service.mission.impl.EvalMissionServiceImpl;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class StandardConfig {
    private List<MarkDimensionGroupDetailDTO> recall;
    private List<MarkDimensionGroupDetailDTO> chat;
    private List<MarkDimensionGroupDetailDTO> queryIgnore;
    private List<MarkDimensionGroupDetailDTO> docIgnore;
    /**
     */
    private List<DocClassifyRule> classifyRules;

    public static StandardConfig of(EvalMissionServiceImpl.StandardConfigBuildParams params) {
        StandardConfig config = new StandardConfig();
//        config.setRecall(params.getRecall() != null ? MarkDimensionDTO.of(params.getRecall()) : null);
        config.setRecall(params.getRecall() != null ? params.getRecall().getDimsTree() : null);
        config.setChat(params.getChat() != null ? params.getChat().getDimsTree() : null);
        config.setQueryIgnore(params.getQueryIgnore() != null ? params.getQueryIgnore().getDimsTree() : null);
        config.setDocIgnore(params.getDocIgnore() != null ? params.getDocIgnore().getDimsTree() : null);
        config.setClassifyRules(params.getStandardConfig().getClassifyRules());
        return config;
    }
}