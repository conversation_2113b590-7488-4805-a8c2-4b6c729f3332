package com.iflytek.lynxiao.eval.domain.mark.result;

import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 标注结果类型
 *
 * <AUTHOR>
 */

public enum MarkResultType {

    RECALL(0, "策略结果"),
    TRACE(1, "全链路结果"),
    CHAT(2, "大模型结果"),
    QUERY_IGNORE(3, "query分类"),
    NO_RESULT(4, "无结果");

    @Getter
    private final Integer code;

    private final String describe;

    MarkResultType(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public static String getName(Integer code) {
        if (ObjectUtils.isNotEmpty(code)) {
            Optional<MarkResultType> first = Arrays.stream(
                    MarkResultType.values()).filter(t -> t.code.equals(code)).findFirst();
            return first.isPresent() ? first.get().describe : StringUtils.EMPTY;
        }
        return StringUtils.EMPTY;
    }


}
