package com.iflytek.lynxiao.eval.service.standard.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalDimensionModule;
import com.iflytek.lynxiao.eval.domain.CatalogTree;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionModuleDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionModuleSaveDTO;
import com.iflytek.lynxiao.eval.repository.EvalDimensionModuleRepository;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionGroupService;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionModuleService;
import com.iflytek.lynxiao.eval.service.standard.MarkStandardService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MarkDimensionModuleServiceImpl implements MarkDimensionModuleService {

    private final EvalDimensionModuleRepository markDimensionRepository;

    private final MarkDimensionGroupService dimensionGroupService;

    private final MarkStandardService markStandardService;

    /**
     * 目录名缓存
     * <模块code,  <catalogCode, catalogName> >
     */
    private final Map<String, Map<String, String>> catalogCodeNameCache = new HashMap<>();

    public MarkDimensionModuleServiceImpl(EvalDimensionModuleRepository markDimensionRepository, @Lazy MarkDimensionGroupService dimensionGroupService,
                                          @Lazy MarkStandardService markStandardService) {
        this.markDimensionRepository = markDimensionRepository;
        this.dimensionGroupService = dimensionGroupService;
        this.markStandardService = markStandardService;
    }


    @Override
    public void save(MarkDimensionModuleSaveDTO dto) {
        log.info("save mark dimension module: {}", dto);

        if (dto.getCatalogTree() == null) {
            throw new LynxiaoException("目录树不能为空");
        }

        GeneratedEvalDimensionModule dimensionModule = this.markDimensionRepository.findByCode(dto.getModuleCode()).orElseThrow(() -> new LynxiaoException("模块不存在: " + dto.getModuleCode()));
        dimensionModule.setCatalogTree(JSONObject.toJSONString(dto.getCatalogTree()));
        updateCatalogCache(dto.getCatalogTree(), dto.getModuleCode());
        markDimensionRepository.save(dimensionModule);
    }

    @Override
    public MarkDimensionModuleDTO find(String moduleCode) {
        log.debug("find mark dimension module by code: {}", moduleCode);
        GeneratedEvalDimensionModule dimensionModule = this.markDimensionRepository.findByCode(moduleCode).orElseThrow(() -> new LynxiaoException("模块不存在: " + moduleCode));
        MarkDimensionModuleDTO markDimensionModuleDTO = BeanUtil.copyProperties(dimensionModule, MarkDimensionModuleDTO.class, "id", "catalogTree");
        markDimensionModuleDTO.setId(String.valueOf(dimensionModule.getId()));
        markDimensionModuleDTO.setCatalogTree(JSONObject.parseObject(dimensionModule.getCatalogTree(), CatalogTree.class));
        return markDimensionModuleDTO;
    }

    @Override
    public void delete(String treeCode, String moduleCode) {
        log.info("delete mark dimension module: {}, treeCode: {}", moduleCode, treeCode);
        MarkDimensionModuleDTO markDimensionModuleDTO = find(moduleCode);

        //从上级目录中删除待删除目录
        CatalogTree parentCatalogTree = findParentByCode(Collections.singletonList(markDimensionModuleDTO.getCatalogTree()), treeCode).orElseThrow(() -> new LynxiaoException("待删除目录的父目录不存在: " + treeCode));
        parentCatalogTree.getChildren().removeIf(child -> StringUtils.equals(child.getCode(), treeCode));

        //删除挂载在当前目录下的维度集或标准
        this.markStandardService.deleteByCode(treeCode);
        this.dimensionGroupService.deleteByCode(treeCode);

        GeneratedEvalDimensionModule dimensionModule = BeanUtil.copyProperties(markDimensionModuleDTO, GeneratedEvalDimensionModule.class);
        dimensionModule.setDeleted(false);
        this.markDimensionRepository.save(dimensionModule);
    }

    @Override
    public String getCatalogName(String treeCode, String moduleCode) {
        if (this.catalogCodeNameCache.isEmpty()) {
            //初始化缓存
            List<GeneratedEvalDimensionModule> dimensionModules = this.markDimensionRepository.findAllUnDeleted();
            dimensionModules.forEach(module -> {
                this.catalogCodeNameCache.put(module.getCode(), new HashMap<>());
                updateCatalogCodeNameCache(JSONObject.parseObject(module.getCatalogTree(), CatalogTree.class), module.getCode());
            });
        }

        Map<String, String> codeNameMap = this.catalogCodeNameCache.get(moduleCode);
        return codeNameMap != null ? codeNameMap.get(treeCode) : null;
    }

    /**
     * 更新目录code - 目录名缓存
     */
    private void updateCatalogCache(CatalogTree catalogTree, String moduleCode) {
        this.catalogCodeNameCache.put(moduleCode, new HashMap<>());
        updateCatalogCodeNameCache(catalogTree, moduleCode);
    }

    private void updateCatalogCodeNameCache(CatalogTree catalogTree, String moduleCode) {
        if (catalogTree == null) {
            return;
        }

        this.catalogCodeNameCache.get(moduleCode).put(catalogTree.getCode(), catalogTree.getName());

        if (catalogTree.getChildren() != null) {
            for (CatalogTree child : catalogTree.getChildren()) {
                updateCatalogCodeNameCache(child, moduleCode);
            }
        }
    }

    /**
     * 根据目录编码查找目录树
     *
     * @param catalogTreeList 目录树列表
     * @param code            待查找目录编码
     * @return 查找的目标目录
     */
    private Optional<CatalogTree> findByCode(List<CatalogTree> catalogTreeList, String code) {
        if (catalogTreeList == null || catalogTreeList.isEmpty()) {
            return Optional.empty();
        }

        for (CatalogTree catalog : catalogTreeList) {
            if (StringUtils.equals(catalog.getCode(), code)) {
                return Optional.of(catalog);
            }
            if (catalog.getChildren() != null) {
                Optional<CatalogTree> found = findByCode(catalog.getChildren(), code);
                if (found.isPresent()) {
                    return found;
                }
            }
        }
        return Optional.empty();
    }

    /**
     * 根据目录编码查找父目录
     *
     * @param catalogTreeList 目录树列表
     * @param code            待查找目录编码
     * @return 当前目录的父目录
     */
    private Optional<CatalogTree> findParentByCode(List<CatalogTree> catalogTreeList, String code) {
        if (catalogTreeList == null || catalogTreeList.isEmpty()) {
            return Optional.empty();
        }

        for (CatalogTree catalog : catalogTreeList) {
            if (catalog.getChildren() != null && !catalog.getChildren().isEmpty()) {
                if (catalog.getChildren().stream()
                        .anyMatch(child -> StringUtils.equals(child.getCode(), code))) {
                    return Optional.of(catalog);
                }
                Optional<CatalogTree> parent = findParentByCode(catalog.getChildren(), code);
                if (parent.isPresent()) {
                    return parent;
                }
            }
        }
        return Optional.empty();
    }
}
