package com.iflytek.lynxiao.eval.service.search.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.component.core.ElasticClientFactory;
import com.iflytek.lynxiao.eval.config.EvalProperties;
import com.iflytek.lynxiao.eval.dto.search.SearchApiResultQueryDTO;
import com.iflytek.lynxiao.eval.service.search.SearchApiResultFetcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.apache.http.nio.entity.NStringEntity;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchApiResultFetcherImpl implements SearchApiResultFetcher {

    private static final String INDEX_PREFIX = "lynxiao_flow-";

    private final ElasticClientFactory elasticClientFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${lynxiao.env-code:prod}")
    private String envCode;

    private final EvalProperties evalProperties;

    public SearchApiResultFetcherImpl(ElasticClientFactory elasticClientFactory, EvalProperties evalProperties) {
        this.elasticClientFactory = elasticClientFactory;
        this.evalProperties = evalProperties;
    }


    /**
     * 分页获取搜索结果日志(最大返回10000条)
     *
     * @param dto 查询参数
     * @return page
     */
    @Override
    public Page<JSONObject> fetchPage(SearchApiResultQueryDTO dto) {

        // 1. 生成索引列表
        List<String> indices = generateIndices(dto.getBeginTime(), dto.getEndTime());
        String targetIndices = String.join(",", indices);

        // 2. 构建查询条件
        int size = Math.min(Math.max(dto.getSize(), 1), 1000);
        int from = (dto.getPage() - 1) * dto.getSize();
        ObjectNode queryBody = buildQueryBody(dto, from, size, Boolean.FALSE);
        queryBody.set("_source", getIncludeFields(dto.getRegion())); // 指定查询的字段

        log.debug("page region: {}, dto: {}, queryBody: {}", dto.getRegion(), dto, queryBody.toString());

        // 3. 创建请求
        Request request = new Request("GET", "/" + targetIndices + "/_search");
        request.setEntity(new NStringEntity(queryBody.toString(), ContentType.APPLICATION_JSON));
        // 4. 执行请求
        try {
            Response response = this.elasticClientFactory.getRestClient(dto.getRegion()).performRequest(request);

            if (null == response || response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new LynxiaoException("查询失败!");
            }
            // 5. 解析响应
            List<JSONObject> list = parseResponse(response);
            // 6. 单独查询总量
            long total = executeCount(indices, dto);
            return new PageImpl<>(list, Pageable.ofSize(size), total);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LynxiaoException("查询异常!", e);
        }
    }

    @Override
    public List<JSONObject> fetchList(SearchApiResultQueryDTO dto, Integer maxRecords) {

        List<JSONObject> allHits = new ArrayList<>();
        String scrollId = null;

        try {
            // 1. 生成索引列表
            List<String> indices = generateIndices(dto.getBeginTime(), dto.getEndTime());
            String targetIndices = String.join(",", indices);

            // 2. 构建查询条件
            ObjectNode queryBody = buildQueryBody(dto, null, null, Boolean.FALSE);
            queryBody.put("size", 500); // 指定每次查询返回的记录数
            queryBody.set("_source", getIncludeFields(dto.getRegion())); // 指定查询的字段

            log.debug("region: {}, dto: {}, queryBody: {}", dto.getRegion(), dto, queryBody.toString());

            // 3. 创建初始请求
            // 使用POST方法，并确保scroll参数在查询字符串中
            Request initialRequest = new Request("POST", "/" + targetIndices + "/_search?scroll=1m");
            initialRequest.setEntity(new NStringEntity(queryBody.toString(), ContentType.APPLICATION_JSON));
            // 4. 执行初始请求
            Response initialResponse = this.elasticClientFactory.getRestClient(dto.getRegion()).performRequest(initialRequest);
            if (null == initialResponse || initialResponse.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new LynxiaoException("查询失败!");
            }
            String json = EntityUtils.toString(initialResponse.getEntity());
            JsonNode root = objectMapper.readTree(json);
            scrollId = root.path("_scroll_id").asText();

            // 处理初始响应
            JsonNode hits = root.path("hits").path("hits");
            if (hits.isEmpty()) {
                log.info("Initial query returned 0 hits.");
                return allHits;
            }
            for (JsonNode hit : hits) {
                allHits.add(objectMapper.convertValue(hit.path("_source"), JSONObject.class));
                if (allHits.size() >= maxRecords) {
                    break;
                }
            }
            log.debug("allHits size: {}", allHits.size());

            // 5. 循环使用游标查询
            while (allHits.size() < maxRecords && StringUtils.isNotBlank(scrollId)) {
                log.debug("allHits size: {}", allHits.size());
                ObjectNode scrollRequest = objectMapper.createObjectNode();
                scrollRequest.put("scroll", "1m");
                scrollRequest.put("scroll_id", scrollId);

                Request scrollRequestObj = new Request("POST", "/_search/scroll");
                scrollRequestObj.setEntity(new NStringEntity(scrollRequest.toString(), ContentType.APPLICATION_JSON));

                Response scrollResponse = this.elasticClientFactory.getRestClient(dto.getRegion()).performRequest(scrollRequestObj);
                if (null == scrollResponse || scrollResponse.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                    continue;
                }
                json = EntityUtils.toString(scrollResponse.getEntity());
                root = objectMapper.readTree(json);
                scrollId = root.path("_scroll_id").asText();

                hits = root.path("hits").path("hits");
                if (hits.isEmpty()) {
                    log.info("Scroll query returned 0 hits.");
                    break; // 没有更多结果，退出循环
                }

                for (JsonNode hit : hits) {
                    allHits.add(objectMapper.convertValue(hit.path("_source"), JSONObject.class));
                    if (allHits.size() >= maxRecords) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LynxiaoException("查询异常: " + e.getMessage(), e);
        } finally {
            // 清除游标
            if (StringUtils.isNotBlank(scrollId)) {
                clearScroll(dto.getRegion(), scrollId);
            }
        }
        return allHits;
    }


    // 解析响应
    private List<JSONObject> parseResponse(Response response) throws IOException {
        String json = EntityUtils.toString(response.getEntity());
        JsonNode root = objectMapper.readTree(json);
//        int total = root.path("hits").path("total").path("value").asInt();
        List<JSONObject> hits = new ArrayList<>();
        for (JsonNode hit : root.path("hits").path("hits")) {
            hits.add(objectMapper.convertValue(hit.path("_source"), JSONObject.class));
        }
        return hits;
    }


    // 生成索引列表
    private List<String> generateIndices(String begin, String end) {
        DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter indexFormat = DateTimeFormatter.ofPattern("yyyy.MM.dd");
        LocalDateTime startDateTime = LocalDateTime.parse(begin, inputFormat);
        LocalDate startDate = startDateTime.toLocalDate();
        LocalDateTime endDateTime = LocalDateTime.parse(end, inputFormat);
        LocalDate endDate = endDateTime.toLocalDate();
        List<String> indices = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            indices.add(INDEX_PREFIX + indexFormat.format(date));
        }
        return indices;
    }

    /**
     * 构建查询条件 DSL
     *
     * @param dto        条件dto
     * @param from       分页开始索引
     * @param size       分页大小
     * @param countQuery 是否是 count 查询,如果是count查询,则不需要排序配置,否则会报错
     * @return 查询条件
     */
    private ObjectNode buildQueryBody(SearchApiResultQueryDTO dto, Integer from, Integer size, Boolean countQuery) {

        // {"from":0,"size":20,"sort":[{"@timestamp":"desc"}],"query":{"bool":{"must":[{"term":{"type.keyword":"SearchAPI-Response"}},{"terms":{"tags.appId":["cc501f15"]}}]}},"_source":{"includes":["traceId","@timestamp","tags"]}}
        ObjectNode root = objectMapper.createObjectNode();

        if (null != from && null != size) {
            root.put("from", from);
            root.put("size", size);
        }
        // 排序配置
        if (!countQuery) {
            ArrayNode sort = root.putArray("sort");
            sort.add(objectMapper.createObjectNode().put("@timestamp", "desc"));
        }
        // 构建复合查询
        ObjectNode boolQuery = objectMapper.createObjectNode();
        root.set("query", objectMapper.createObjectNode().set("bool", boolQuery));
        // 动态添加其他查询条件
        ArrayNode must = boolQuery.putArray("must");
        must.add(buildTermQuery("type.keyword", "SearchAPI-Response"));
        must.add(buildTermQuery("labels.env", envCode));


        if (CollectionUtil.isNotEmpty(dto.getAppIdList())) {
            must.add(buildTermsQuery( "appId", dto.getAppIdList()));
        }
        if (CollectionUtil.isNotEmpty(dto.getProdCode())) {
            must.add(buildTermsQuery( "prodCode.keyword", dto.getProdCode()));
        }
        if (CollectionUtil.isNotEmpty(dto.getPvidList())) {
            must.add(buildTermsQuery( "pvid", dto.getPvidList()));
        }
        if (StringUtils.isNotBlank(dto.getTraceId())) {
            must.add(buildTermQuery("traceId.keyword", dto.getTraceId()));
        }
        if (StringUtils.isNotBlank(dto.getFlowName())) {
            must.add(buildTermQuery( "flowName.keyword", dto.getFlowName()));
        }
        if (StringUtils.isNotBlank(dto.getQuery())) {
            must.add(buildTermQuery( "query.keyword", dto.getQuery()));
        }
        if (StringUtils.isNotBlank(dto.getFromType())) {
            must.add(buildTermQuery( "fromType.keyword", dto.getFromType()));
        }
        if (StringUtils.isNotBlank(dto.getTopK())) {
            must.add(buildTermQuery( "topK", dto.getTopK()));
        }
        if (StringUtils.isNotBlank(dto.getScene())) {
            must.add(buildMatchPhraseQuery("scene", dto.getScene()));
        }
        if (CollectionUtil.isNotEmpty(dto.getIntent())) {
            must.add(buildTermsQuery("intent", dto.getIntent()));
        }
        if (StringUtils.isNotBlank(dto.getDocCount())) {
            must.add(buildTermQuery("docsCount", Integer.parseInt(dto.getDocCount())));
        }
        if (StringUtils.isNotBlank(dto.getPlugin())) {
            must.add(buildTermQuery("plugin.keyword", dto.getPlugin()));
        }
        if (StringUtils.isNotBlank(dto.getFilterDomain())) {
            must.add(buildMatchPhraseQuery("filter.domain", dto.getFilterDomain()));
        }
        if (StringUtils.isNotBlank(dto.getQid())){
            must.add(buildTermQuery("qid.keyword", dto.getQid()));
        }
        if (StringUtils.isNotBlank(dto.getRawQuery())){
            must.add(buildTermQuery("rawQuery.keyword", dto.getRawQuery()));
        }


        if (StringUtils.isNotBlank(dto.getFilterFiledName()) && StringUtils.isNotBlank(dto.getFilterStartTime()) && StringUtils.isNotBlank(dto.getFilterEndTime())) {
            must.add(buildTermQuery("filter." + dto.getFilterFiledName() + ".et", dto.getFilterEndTime()));
            must.add(buildTermQuery("filter." + dto.getFilterFiledName() + ".st", dto.getFilterStartTime()));
        }

        // 时间范围过滤
        must.add(objectMapper.createObjectNode()
                .set("range", objectMapper.createObjectNode()
                        .set("@timestamp", objectMapper.createObjectNode()
                                .put("gte", dto.getBeginTime())
                                .put("lte", dto.getEndTime())
                                .put("format", "yyyy-MM-dd HH:mm:ss"))));


        return root;
    }

    // 各查询类型的构建方法
    private ObjectNode buildTermQuery(String field, Object value) {
        return objectMapper.createObjectNode()
                .set("term", objectMapper.createObjectNode()
                        .put(field, value.toString()));
    }

    private ObjectNode buildTermsQuery(String field, Collection<?> values) {
        ArrayNode termsArray = objectMapper.createArrayNode();
        values.forEach(v -> termsArray.add(v.toString()));
        return objectMapper.createObjectNode()
                .set("terms", objectMapper.createObjectNode()
                        .set(field, termsArray));
    }

    private ObjectNode buildMatchPhraseQuery(String field, Object value) {
        return objectMapper.createObjectNode()
                .set("match_phrase", objectMapper.createObjectNode()
                        .put(field, value.toString()));
    }

    private void clearScroll(String region, String scrollId) {
        ObjectNode clearScrollRequest = objectMapper.createObjectNode();
        clearScrollRequest.putArray("scroll_id").add(scrollId);
        Request clearRequest = new Request("DELETE", "/_search/scroll");
        clearRequest.setEntity(new NStringEntity(clearScrollRequest.toString(), ContentType.APPLICATION_JSON));
        try {
            this.elasticClientFactory.getRestClient(region).performRequest(clearRequest);
        } catch (Exception e) {
            log.error("清除游标失败", e);
        }
    }

    private ObjectNode getIncludeFields(String region) {
        ObjectNode sourceFilter = objectMapper.createObjectNode();
        ArrayNode includes = objectMapper.createArrayNode();
        includes.add("traceId");
        includes.add("@timestamp");
        includes.add("prodCode");
        includes.add("pvid");
        includes.add("topK");
        includes.add("code");
        includes.add("cost");
        includes.add("query");
        includes.add("intent");
        includes.add("flowName");
        includes.add("scene");
        includes.add("fromType");
        includes.add("plugin");
        includes.add("appId");
        includes.add("id");
        includes.add("region");
        includes.add("docsCount");
        includes.add("qid");
        includes.add("rawQuery");
        sourceFilter.set("includes", includes);
        return sourceFilter;
    }

    // 查询指定条件的总数
    private long executeCount(List<String> indices, SearchApiResultQueryDTO dto) {
        String targetIndices = String.join(",", indices);
        ObjectNode countQueryBody = buildQueryBody(dto, null, null, Boolean.TRUE); // 构建 count 查询的查询条件

        Request countRequest = new Request("GET", "/" + targetIndices + "/_count");
        countRequest.setEntity(new NStringEntity(countQueryBody.toString(), ContentType.APPLICATION_JSON));

        try {
            Response countResponse = this.elasticClientFactory.getRestClient(dto.getRegion()).performRequest(countRequest);
            if (countResponse == null || countResponse.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
                throw new LynxiaoException("总数查询失败!");
            }
            // 解析 count 响应
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode countJson = objectMapper.readTree(countResponse.getEntity().getContent());
            return countJson.get("count").asLong();

        } catch (Exception e) {
            throw new LynxiaoException("总数查询异常!", e);
        }
    }
}
