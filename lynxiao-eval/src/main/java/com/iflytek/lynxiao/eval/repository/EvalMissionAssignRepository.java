package com.iflytek.lynxiao.eval.repository;


import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMissionAssign;
import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalMissionAssignRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Repository
public interface EvalMissionAssignRepository extends GeneratedEvalMissionAssignRepository {

    List<GeneratedEvalMissionAssign> findAllByMissionIdAndDeletedFalse(Long missionId);

    @Query("SELECT m FROM GeneratedEvalMissionAssign m WHERE m.deleted is false AND m.missionId = :missionId " +
            "AND (:userGroup IS NULL OR m.userGroup = :userGroup)")
    List<GeneratedEvalMissionAssign> findUndeletedByMissionIdAndUserGroup(@Param("missionId")Long missionId, @Param("userGroup") String userGroup);

    @Query("SELECT m FROM GeneratedEvalMissionAssign m WHERE m.deleted is false AND m.missionId in :missionIds and m.status = 1")
    List<GeneratedEvalMissionAssign> findAllByMissionIdInAndDeletedFalse(@Param("missionIds") List<Long> missionIds);

    @Query("SELECT m FROM GeneratedEvalMissionAssign m WHERE m.deleted is false AND m.account = :account  AND m.status = 1 ")
    List<GeneratedEvalMissionAssign> myList(@Param("account") String account);

    @Query("SELECT m FROM GeneratedEvalMissionAssign m WHERE m.deleted is false AND m.account = :account AND m.missionId = :missionId AND m.status = 1 ")
    GeneratedEvalMissionAssign findUserGroup(@Param("account") String account,
                                             @Param("missionId") Long missionId);

    @Modifying
    @Query("UPDATE GeneratedEvalMissionAssign m SET m.todoIdxs = :todoIdx, m.completedCount = :completedCount WHERE m.id = :id")
    void update(@Param("id") Long id, @Param("todoIdx") String todoIdx, @Param("completedCount") int completedCount);

    void deleteByMissionId(Long missionId);

    @Query("SELECT m FROM GeneratedEvalMissionAssign m WHERE m.deleted is false AND m.missionId = :missionId AND m.userGroup = :userGroup AND m.status = 1 ")
    List<GeneratedEvalMissionAssign> findAllByMissionIdAndUserGroup(@Param("missionId") Long missionId, @Param("userGroup") String userGroup);
}