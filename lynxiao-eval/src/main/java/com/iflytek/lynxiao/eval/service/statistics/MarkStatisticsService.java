package com.iflytek.lynxiao.eval.service.statistics;

import com.iflytek.lynxiao.eval.dto.mission.EvalMissionFilterParamsDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultAnalysisDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkStatisticsViewDTO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 标注结果统计
 *
 * <AUTHOR>
 */
public interface MarkStatisticsService {

    /**
     * 分业查询标注结果
     *
     * @param pageable 分页
     * @return Page<MarkResultDetailDTO>
     */
    Page<MarkStatisticsViewDTO> findByPage(MarkResultQueryDTO dto, Pageable pageable);


    /**
     * @param missionId 测评任务id
     * @return 最大topK
     */
    EvalMissionFilterParamsDTO findFilterParams(String missionId);


    /**
     * 分业查询标注分析结果
     */
    List<MarkResultAnalysisDTO> analysis(MarkResultQueryDTO dto);


    /**
     * 导出标注结果
     */
    void export(MarkResultQueryDTO dto, HttpServletResponse response);


    /**
     * 导出标注分析结果
     */
    void exportAnalysis(MarkResultQueryDTO dto, HttpServletResponse response);

}
