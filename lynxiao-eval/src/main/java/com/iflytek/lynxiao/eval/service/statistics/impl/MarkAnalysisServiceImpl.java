package com.iflytek.lynxiao.eval.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.data.dto.FlowVersionDTO;
import com.iflytek.lynxiao.data.utils.NameUtil;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMissionAssign;
import com.iflytek.lynxiao.eval.domain.DocClassifyRule;
import com.iflytek.lynxiao.eval.domain.MarkResultGroupType;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.domain.UrlEvalType;
import com.iflytek.lynxiao.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultDims;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultGroup;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultType;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultAnalysisDTO;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.eval.repository.EvalMissionAssignRepository;
import com.iflytek.lynxiao.eval.service.statistics.MarkAnalysisService;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>  2025/5/8 15:21
 */
@Slf4j
@Service
public class MarkAnalysisServiceImpl implements MarkAnalysisService {

    private final EvalMissionAssignRepository evalMissionAssignRepository;

    public MarkAnalysisServiceImpl(EvalMissionAssignRepository evalMissionAssignRepository) {
        this.evalMissionAssignRepository = evalMissionAssignRepository;
    }

    @Override
    public List<MarkResultAnalysisDTO> analysis(MarkResultQueryDTO dto, List<MarkResultGroup> groupListList,
                                                EvalMissionDTO missionDTO, Map<String,  LynxiaoFeignClientManager.MetaRegionFeign.MetaRegion> regionMap,
                                                Map<String, FlowVersionDTO> processIdFlowVersionMap, @NotNull List<MarkTargetEntity> targetEntities) {
        List<MarkResultAnalysisDTO> result = new ArrayList<>();

        List<GeneratedEvalMissionAssign> missionAssigns = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getMissionId())) {
            missionAssigns = this.evalMissionAssignRepository.findAllByMissionIdAndDeletedFalse(Long.valueOf(dto.getMissionId()));
        }

        for (MarkResultGroup group : groupListList) {
            // 计算指标
            MarkResultAnalysisDTO markResultAnalysis = analysis(group, dto.getTopK(), missionDTO);
            StrategyConfig strategyConfig = missionDTO.getStrategyConfig()
                    .stream()
                    .filter(item -> item.getId().equals(group.getStrategyId()))
                    .findFirst().orElseThrow(() -> new LynxiaoException("策略配置未找到"));


            markResultAnalysis.setSceneProcessName(getProcessName(strategyConfig, processIdFlowVersionMap));
            markResultAnalysis.setAscribeMode(strategyConfig.getAscribeMode());
            markResultAnalysis.setMarkUser(group.getMarkUser());
            markResultAnalysis.setUserType(group.getUserType());
            markResultAnalysis.setUserGroup(group.getUserGroup());
            markResultAnalysis.setRegionName(regionMap.get(strategyConfig.getRegionCode()).getName());

            if (group.getMode() == MarkMode.MISSION) {
                List<MarkTargetEntity> filtedTargetList;
                List<GeneratedEvalMissionAssign> filtedMissionAssignList;
                if (group.getUserType() == MarkResultGroupType.TYPE_GROUP) {
                    filtedTargetList = targetEntities.stream().filter(item -> item.getUserGroup().equals(group.getMarkUser()) && item.getStrategyId().equals(group.getStrategyId())).toList();
                    filtedMissionAssignList = missionAssigns.stream().filter(item -> group.getMarkUser().equals(item.getUserGroup())).toList();
                } else {
                    filtedTargetList = targetEntities.stream().filter(item -> item.getAccount().equals(group.getMarkUser()) && item.getStrategyId().equals(group.getStrategyId())).toList();
                    filtedMissionAssignList = missionAssigns.stream().filter(item -> group.getMarkUser().equals(item.getAccount())).toList();
                }

                computeQueryCount(markResultAnalysis, filtedMissionAssignList, filtedTargetList);
            }
            result.add(markResultAnalysis);
        }

        return result;
    }

    /**
     * 获取策略名称
     *
     * @param strategyConfig          策略配置
     * @param flowVersionMap 流程id与流程信息映射
     */
    private static String getProcessName(StrategyConfig strategyConfig, Map<String, FlowVersionDTO> flowVersionMap) {
        if (strategyConfig.isOffline()) {
            // 离线策略
            return strategyConfig.getCompName();
        } else {
            // 在线策略
            FlowVersionDTO flowVersion = flowVersionMap.get(strategyConfig.getProcessId());
            if (null != flowVersion) {
                return NameUtil.buildVersionName(flowVersion.getName(), flowVersion.getVersion());
            }
        }
        return "";
    }

    @Override
    public MarkResultAnalysisDTO analysis(MarkResultGroup resultGroup, Integer topK, EvalMissionDTO missionDTO) {
        assert resultGroup != null;
        assert missionDTO != null;

        // 1. 从分组中过滤出topK的标注结果
        List<MarkResultEntity> resultEntities = filterTopK(resultGroup.getMarkResultList(), topK);

        // 2. 指标统计
        MarkResultAnalysisDTO dto = BeanUtil.copyProperties(resultGroup, MarkResultAnalysisDTO.class);
        if (CollectionUtil.isEmpty(resultEntities)) {
            return dto;
        }

        // 3. 赋值基础信息
        dto.setCreatedBy(getMarkUser(resultEntities));
        dto.setQueryCount(getQueryCount(resultEntities));
        dto.setMarkMinTime(getMarkMinTime(resultEntities));
        dto.setMarkMaxTime(getMarkMaxTime(resultEntities));

        // 4. 标注结果分析统计
        analysis(dto, resultEntities, missionDTO.getStandardConfig().getClassifyRules());

        return dto;
    }

    /**
     * 过滤出topK的标注结果
     */
    private static List<MarkResultEntity> filterTopK(List<MarkResultEntity> markResultList, Integer topK) {
        if (CollectionUtil.isEmpty(markResultList)) {
            return new ArrayList<>();
        }
        List<MarkResultEntity> topkList = new ArrayList<>();
        for (MarkResultEntity entity : markResultList) {
            if (!MarkResultType.RECALL.getCode().equals(entity.getType())) {
                continue;
            }
            if (entity.getData() == null || entity.getData().getRecall() == null) {
                continue;
            }
            if (entity.getData().getRecall().getDocIdx() + 1 <= topK) {
                topkList.add(entity);
            }
        }
        return topkList;
    }


    /**
     * 获取标注列表中所有标注人，通过 ","拼接成字符串
     */
    private static String getMarkUser(List<MarkResultEntity> markResultList) {
        // 标注人
        return markResultList.stream()
                .map(MarkResultEntity::getCreatedBy)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(","));
    }

    /**
     * 获取标注列表中去重后的query条数
     */
    private static long getQueryCount(List<MarkResultEntity> markResultList) {
        // Query条数(去重)
        return markResultList.stream()
                .map(MarkResultEntity::getQuery)
                .distinct()
                .count();
    }

    /**
     * 获取标注列表中最早的标注时间
     */
    private static Instant getMarkMinTime(List<MarkResultEntity> markResultList) {
        return markResultList.stream()
                .map(MarkResultEntity::getCreatedDate)
                .filter(Objects::nonNull)
                .min(Instant::compareTo)
                .orElse(null);
    }

    /**
     * 获取标注列表中最晚的标注时间
     */
    private static Instant getMarkMaxTime(List<MarkResultEntity> markResultList) {
        return markResultList.stream()
                .map(MarkResultEntity::getCreatedDate)
                .filter(Objects::nonNull)
                .max(Instant::compareTo)
                .orElse(null);
    }

    /**
     * 结果统计计算与赋值
     */
    private static void analysis(MarkResultAnalysisDTO dto, List<MarkResultEntity> topKResultList, List<DocClassifyRule> classifyRules) {
        // 1. 统计数据
        AnalysisStats stats = calculateStats(topKResultList, classifyRules);

        // 2. 填充统计结果
        fillAnalysisResult(dto, stats);
    }

    /**
     * 统计数据
     */
    private static AnalysisStats calculateStats(List<MarkResultEntity> topKResultList, List<DocClassifyRule> classifyRules) {
        AnalysisStats stats = new AnalysisStats();

        for (MarkResultEntity entity : topKResultList) {
            stats.docCount++;
            stats.queryCountMap.merge(entity.getMarkRecordId(), 1, Integer::sum);

            if (entity.getData().getRecall() == null || CollectionUtils.isEmpty(entity.getData().getRecall().getDimsList())) {
                continue;
            }

            // good 、 bad规则统计
            List<MarkResultDims> dimList = entity.getData().getRecall().getDimsList();
            UrlEvalType urlEvalType = DocClassifyRule.classifyBaseRule(dimList, classifyRules);

            if (UrlEvalType.GOOD_DOC == urlEvalType) {
                stats.oneGoodDocQuery.add(entity.getMarkRecordId());
                stats.goodDocCount++;
                stats.queryGoodCountMap.merge(entity.getMarkRecordId(), 1, Integer::sum);
            } else if (UrlEvalType.BAD_DOC == urlEvalType) {
                stats.badDocCount++;
            }

            //自定义规则统计
            Map<String, Boolean> classifyResultMap = DocClassifyRule.classifyCustomRule(dimList, classifyRules);
            classifyResultMap.forEach((classifyName, classifyResult) -> {
                if (classifyResult) {
                    stats.extendMap.merge(classifyName, 1, (oldVal, newVal) -> (Integer) oldVal + 1);
                }
            });
        }

        //统计topK完全good的query数量 以及 测评记录id
        stats.queryGoodCountMap.forEach((markRecordId, goodDocCount) -> {
            if (Objects.equals(stats.queryCountMap.get(markRecordId), goodDocCount)) {
                stats.goodTopkQueryCount++;
                stats.topKGoodQueryMarkRecordIds.add(markRecordId);
            }
        });

        return stats;
    }

    /**
     * 填充统计结果
     */
    private static void fillAnalysisResult(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        long noDistinctQueryCount = stats.queryCountMap.keySet().size();

        fillOneGoodDocQuery(dto, stats);
        fillOneGoodDocQueryRecordIds(dto, stats);
        fillHasResultAbsorbPercentage(dto, stats, noDistinctQueryCount);

        fillDocCount(dto, stats);
        fillGoodDocCount(dto, stats);
        fillGoodDocPercentage(dto, stats);
        fillBadDocCount(dto, stats);
        fillBadDocPercentage(dto, stats);

        fillTopkGoodQuery(dto, stats);
        fillTopkGoodQueryRecordIds(dto, stats);
        fillTopkGoodQueryPercentage(dto, stats, noDistinctQueryCount);

        fillExtendMap(dto, stats);
    }

    /**
     * 含≥1条good结果的query数量
     * 计算: oneGoodDocQuery集合的大小，表示有多少个query(去重)至少包含1个好结果
     */
    private static void fillOneGoodDocQuery(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setOneGoodDocQuery(stats.oneGoodDocQuery.size());
    }

    /**
     * 含≥1条good结果的query的ID列表
     * 计算: 将oneGoodDocQuery集合转换为ArrayList
     */
    private static void fillOneGoodDocQueryRecordIds(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setOneGoodDocQueryRecordIds(new ArrayList<>(stats.oneGoodDocQuery));
    }

    /**
     * 有结果吸收率
     * 计算: (含≥1条good结果的query数 / 去重后的总query数) * 100%
     */
    private static void fillHasResultAbsorbPercentage(MarkResultAnalysisDTO dto, AnalysisStats stats, long noDistinctQueryCount) {
        dto.setHasResultAbsorbPercentage(calculatePercentage(stats.oneGoodDocQuery.size(), noDistinctQueryCount));
    }

    /**
     * 结果总数
     * 计算: 所有返回文档的总数量
     */
    private static void fillDocCount(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setDocCount(stats.docCount);
    }

    /**
     * good结果数量
     * 计算: 被评为good的文档总数
     */
    private static void fillGoodDocCount(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setGoodDocCount(stats.goodDocCount);
    }

    /**
     * good结果占比
     * 计算: (good文档数 / 总文档数) * 100%
     */
    private static void fillGoodDocPercentage(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setGoodDocPercentage(calculatePercentage(stats.goodDocCount, stats.docCount));
    }

    /**
     * bad结果数量
     * 计算: 被评为bad的文档总数
     */
    private static void fillBadDocCount(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setBadDocCount(stats.badDocCount);
    }

    /**
     * bad结果占比
     * 计算: (bad文档数 / 总文档数) * 100%
     */
    private static void fillBadDocPercentage(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setBadDocPercentage(calculatePercentage(stats.badDocCount, stats.docCount));
    }

    /**
     * TopK全为good的query数量
     * 计算: topkGoodQueryTaskIds集合的大小，表示TopK结果全部为good的query数量
     */
    private static void fillTopkGoodQuery(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setTopKGoodQuery(stats.topKGoodQueryMarkRecordIds.size());
    }

    /**
     * TopK全为good的query ID列表
     * 计算: 将topkGoodQueryTaskIds集合转换为ArrayList
     */
    private static void fillTopkGoodQueryRecordIds(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setTopKGoodQueryRecordIds(new ArrayList<>(stats.topKGoodQueryMarkRecordIds));
    }

    /**
     * TopK全为good的query占比
     * 计算: (TopK全为good的query数 / 去重后的总query数) * 100%
     */
    private static void fillTopkGoodQueryPercentage(MarkResultAnalysisDTO dto, AnalysisStats stats, long noDistinctQueryCount) {
        dto.setTopKGoodQueryPercentage(calculatePercentage(stats.goodTopkQueryCount, noDistinctQueryCount));
    }

    /**
     * 扩展统计信息
     * 计算: 直接设置扩展Map，包含自定义规则的统计结果
     */
    private static void fillExtendMap(MarkResultAnalysisDTO dto, AnalysisStats stats) {
        dto.setExtendMap(stats.extendMap);
    }

    /**
     * 百分比计算
     * 计算: (long1 / long2) * 100%，结果保留2位小数
     */
    private static BigDecimal calculatePercentage(long long1, long long2) {
        if (long2 == 0) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_DOWN);
        }
        BigDecimal bigDecimal1 = BigDecimal.valueOf(long1);
        BigDecimal bigDecimal2 = BigDecimal.valueOf(long2);
        BigDecimal percentage = bigDecimal1.divide(bigDecimal2, 4, RoundingMode.HALF_DOWN).multiply(BigDecimal.valueOf(100));
        return percentage.setScale(2, RoundingMode.HALF_DOWN);
    }

    /**
     * 统计测评任务模式专有指标: 分配query、已完成query、未完成query、完成率
     */
    private static void computeQueryCount(MarkResultAnalysisDTO analysisDTO, List<GeneratedEvalMissionAssign> missionAssignList, List<MarkTargetEntity> filtedTargetList) {
        // 统计标注任务完成的query情况
        if (CollectionUtil.isEmpty(missionAssignList)) {
            return;
        }
        // 统计分配query数
        long assignQueryCount = missionAssignList.stream().mapToLong(GeneratedEvalMissionAssign::getAssignedCount).sum();
        // 统计已完成query数
        long completeQueryCount = missionAssignList.stream().mapToLong(GeneratedEvalMissionAssign::getCompletedCount).sum();

        analysisDTO.setAssignQueryCount(assignQueryCount);
        analysisDTO.setCompleteQueryCount(completeQueryCount);
        analysisDTO.setNoCompleteQueryCount(assignQueryCount - completeQueryCount);
        // 统计query完成率
        if (assignQueryCount > 0) {
            analysisDTO.setCompletePercentage(BigDecimal.valueOf(completeQueryCount).divide(BigDecimal.valueOf(assignQueryCount), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        } else {
            analysisDTO.setCompletePercentage(BigDecimal.ZERO);
        }

        if (CollectionUtil.isEmpty(filtedTargetList)) {
            analysisDTO.setHasResultQueryCount(0);
        } else {
            analysisDTO.setHasResultQueryCount(filtedTargetList.stream()
                    .filter(MarkTargetEntity::isRecalled)
                    .count());
        }
    }

    /**
     * 统计结果
     */
    private static class AnalysisStats {
        // 含≥1条good结果的markRecordId集合
        Set<String> oneGoodDocQuery = new HashSet<>();
        // doc数
        int docCount = 0;
        // good doc数
        int goodDocCount = 0;
        // bad doc数
        int badDocCount = 0;
        // 某个query goodUrl的数量 key:markRecordId, value:good doc数量
        Map<String, Integer> queryGoodCountMap = new HashMap<>();
        // 某个query url的总数量 key:markRecordId,  value:召回doc数量
        Map<String, Integer> queryCountMap = new HashMap<>();
        // 自定义分析字段  key:自定义规则名称  value:满足该规则的doc数
        Map<String, Object> extendMap = new HashMap<>();
        // topK全部是goodUrl的query数
        int goodTopkQueryCount = 0;
        // topK全部是goodUrl的query的任务id集合
        Set<String> topKGoodQueryMarkRecordIds = new HashSet<>();
    }
}
