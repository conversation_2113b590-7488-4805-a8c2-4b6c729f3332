package com.iflytek.lynxiao.eval.dto.mark.record;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.pandora.api.Jsonable;


/**
 * 保存测评记录结果对象
 */
@Setter
@Getter
@Accessors(chain = true)
public class MarkRecordSaveResultDTO extends Jsonable {

    /**
     * 测评记录id
     */
    private String id;

    /**
     * 是否标注完成
     */
    private boolean complete = false;

    /**
     * 提示信息
     */
    private String msg;

}
