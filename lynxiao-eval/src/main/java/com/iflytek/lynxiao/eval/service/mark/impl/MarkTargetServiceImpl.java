package com.iflytek.lynxiao.eval.service.mark.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMission;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalQueryGroupDetail;
import com.iflytek.lynxiao.eval.component.core.impl.ProcTraceFetcher4ProcessId;
import com.iflytek.lynxiao.eval.component.domain.ProcTraceFetcherParam;
import com.iflytek.lynxiao.eval.component.domain.ProcTraceFetcherResult;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetData;
import com.iflytek.lynxiao.eval.domain.mark.ProcessParam;
import com.iflytek.lynxiao.eval.dto.mark.record.ChatRecordDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.MarkRecordCreateDTO;
import com.iflytek.lynxiao.eval.entity.EvalOfflineDocEntity;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.eval.repository.*;
import com.iflytek.lynxiao.eval.service.mark.MarkTargetService;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionAssignService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class MarkTargetServiceImpl implements MarkTargetService {

    private final MarkTargetRepository markTargetRepository;
    private final MarkResultRepository markResultRepository;
    private final ProcTraceFetcher4ProcessId fetcher4ProcessId;
    private final MarkChatPipelineHandler markChatPipelineHandler;
    private final EvalOfflineDocRepository offlineDocRepository;
    private final EvalMissionRepository evalMissionRepository;
    private final EvalQueryGroupDetailRepository queryGroupDetailRepository;

    private final EvalMissionAssignService missionAssignService;
    private final WorkflowProcessService workflowProcessService;


    public MarkTargetServiceImpl(MarkTargetRepository markTargetRepository, WorkflowProcessService workflowProcessService,
                                 MarkResultRepository markResultRepository, ProcTraceFetcher4ProcessId processTraceFetcher,
                                 MarkChatPipelineHandler markChatPipelineHandler, EvalOfflineDocRepository offlineDocRepository,
                                 EvalQueryGroupDetailRepository queryGroupDetailRepository, EvalMissionRepository evalMissionRepository, EvalMissionAssignService missionAssignService) {
        this.markTargetRepository = markTargetRepository;
        this.markResultRepository = markResultRepository;
        this.fetcher4ProcessId = processTraceFetcher;
        this.markChatPipelineHandler = markChatPipelineHandler;
        this.offlineDocRepository = offlineDocRepository;
        this.queryGroupDetailRepository = queryGroupDetailRepository;
        this.evalMissionRepository = evalMissionRepository;
        this.missionAssignService = missionAssignService;
        this.workflowProcessService = workflowProcessService;
    }


    @Override
    public List<MarkTargetEntity> create(String markRecordId, MarkRecordCreateDTO dto) {
        List<MarkTargetEntity> markTargetEntities = new ArrayList<>();

        //1.根据模式执行流程
        if (dto.getMode() == MarkMode.EXPERIENCE) {
            //1.1体验模式
            for (ProcessParam processParam : dto.getProcessList()) {
                assert StringUtils.isNotEmpty(processParam.getProcessId());
                String traceId = "eval_exp_" + UUID.fastUUID().toString().substring(0, 8);
                MarkTargetEntity entity = new MarkTargetEntity();
                entity.setTraceId(traceId);
                MarkTargetData data = execProcess(traceId, dto.getQuery(), processParam);
                entity.setData(data);
                entity.setRecalled(!CollectionUtils.isEmpty(data.getRecallList()));
                entity.setRegionCode(processParam.getRegionCode());
                entity.setProcessId(processParam.getProcessId());
                entity.setProcessParam(processParam.getPayload());
                entity.setMetadata(processParam.getMetadata());
                AuditingEntityUtil.fillCreateValueMongo(entity);
                markTargetEntities.add(entity);
            }
        } else {
            //1.2非体验模式
            GeneratedEvalMission mission = this.evalMissionRepository.findById(Long.valueOf(dto.getMissionId())).orElseThrow(() -> new LynxiaoException("未找到对应的测评任务"));
            List<StrategyConfig> strategyConfigs = JSONArray.parseArray(mission.getStrategyConfig(), StrategyConfig.class);
            for (StrategyConfig strategyConfig : strategyConfigs) {
                String traceId = "eval_mission_" + UUID.fastUUID().toString().substring(0, 8);
                MarkTargetEntity entity = new MarkTargetEntity();
                entity.setTraceId(traceId);
                entity.setStrategyId(strategyConfig.getId());
                entity.setProcessId(strategyConfig.getProcessId());
                entity.setRegionCode(strategyConfig.getRegionCode());
                entity.setProcessParam(strategyConfig.getProcessParams());

                MarkTargetData data;
                if (strategyConfig.isOffline()) {
                    //离线场景
                    data = offlineMarkTargetData(dto.getQuery(), strategyConfig);
                } else {
                    //在线场景
                    ProcessParam processParam = buildProcessParam(dto, strategyConfig);
                    data = execProcess(traceId, dto.getQuery(), processParam);
                    entity.setProcessParam(processParam.getPayload());
                }
                entity.setData(data);
                entity.setRecalled(!CollectionUtils.isEmpty(data.getRecallList()));
                AuditingEntityUtil.fillCreateValueMongo(entity);
                markTargetEntities.add(entity);
            }
        }

        // 2. 保存召回和全链路标注对象
        saveEntityList(markTargetEntities, dto, markRecordId);

        // 3. 保存大模型推理结果
        saveChat(dto, markTargetEntities);

        return markTargetEntities;
    }

    @Override
    public ChatRecordDTO findChat(String id) {
        MarkTargetEntity targetEntity = this.markTargetRepository.findById(id);
        MarkResultEntity markResultEntity = markResultRepository.findChatByTargetId(targetEntity.getId());
        return ChatRecordDTO.of(markResultEntity, targetEntity);
    }

    /**
     * 构建流程执行参数  包含流程信息 + 流程入参
     */
    private ProcessParam buildProcessParam(MarkRecordCreateDTO dto, StrategyConfig strategyConfig) {
        ProcessParam processParam = new ProcessParam();
        processParam.setRegionCode(strategyConfig.getRegionCode());
        processParam.setProcessId(strategyConfig.getProcessId());
        processParam.setStrategyId(strategyConfig.getId());
        processParam.setPayload(strategyConfig.getProcessParams());
        return processParam;
    }

    /**
     * 执行流程
     */
    private MarkTargetData execProcess(String traceId, String query, ProcessParam processParam) {

        JSONObject payload = processParam.getPayload().fluentPut("query", query);

        log.info("do-query process start traceId:{} strategyConfig:{}", traceId, processParam);
        ProcTraceFetcherParam fetcherParam = ProcTraceFetcherParam.of(null, traceId, processParam.getProcessId(), payload, processParam.getRegionCode());
        fetcherParam.setWorkflowProcess(workflowProcessService.getWorkflowProcess(processParam.getProcessId(), false));
        ProcTraceFetcherResult traceFetcherResult = fetcher4ProcessId.process(fetcherParam);
        return MarkTargetData.of(traceFetcherResult.getRecallDocs(), traceFetcherResult.getTraceLogItems());
    }

    /**
     * 构建离线场景的执行上下文
     */
    private MarkTargetData offlineMarkTargetData(String query, StrategyConfig strategyConfig) {
        //根据query获取离线的doc列表
        List<GeneratedEvalQueryGroupDetail> allQuery = queryGroupDetailRepository.findAllByQueryGroupIdAndDeletedFalse(Long.valueOf(strategyConfig.getQueryGroupId()));
        GeneratedEvalQueryGroupDetail targetQuery = allQuery.stream().filter(item -> item.getQuery().equals(query)).findFirst().orElseThrow(() -> new LynxiaoException("离线数据场景没有找到对应的query"));
        EvalOfflineDocEntity offlineDocEntity = offlineDocRepository.findByQueryId(String.valueOf(targetQuery.getId()));

        return MarkTargetData.of(offlineDocEntity.getDocs());
    }


    private void saveChat(MarkRecordCreateDTO dto, List<MarkTargetEntity> entityList) {
        // 异步生成大模型推理结果
        for (MarkTargetEntity entity : entityList) {
            try {
                markChatPipelineHandler.onData(new MarkChatPipelineHandler.MarkChatEvent(dto.getQuery(), entity));
            } catch (InterruptedException e) {
                log.error("start chat verify error, recordId:{} targetId:{}, ex:{}", entity.getMarkRecordId(), entity.getId(), e.getMessage());
                throw new LynxiaoException("执行chat验证失败.");
            }
        }
    }

    private void saveEntityList(List<MarkTargetEntity> entityList, MarkRecordCreateDTO dto, String markRecordId) {
        String userGroup = this.missionAssignService.getMarkUserGroup(dto.getMissionId(), AuditingEntityUtil.getCurrentAccount());
        String account = AuditingEntityUtil.getCurrentAccount();

        for (MarkTargetEntity entity : entityList) {
            entity.setMarkRecordId(markRecordId);
            entity.setMissionId(dto.getMissionId());
            entity.setUserGroup(userGroup);
            entity.setAccount(account);
            AuditingEntityUtil.fillCreateValueMongo(entity);
        }
        markTargetRepository.saveAll(entityList);
    }

}
