package com.iflytek.lynxiao.eval.service.ascribe.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.utils.DownloadUtils;
import com.iflytek.lynxiao.common.utils.ExcelUtils;
import com.iflytek.lynxiao.data.domain.VersionStatus;
import com.iflytek.lynxiao.eval.dto.ascribe.*;
import com.iflytek.lynxiao.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.eval.config.EvalProperties;
import com.iflytek.lynxiao.eval.dto.mark.record.TraceRecordDTO;
import com.iflytek.lynxiao.eval.service.ascribe.EvalPlanService;
import com.iflytek.lynxiao.eval.service.mark.EvalRecordService;
import com.iflytek.lynxiao.eval.utils.TraceLogUtils;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalPlan;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalStrategyVersion;
import com.iflytek.lynxiao.eval.repository.EvalPlanRepository;
import com.iflytek.lynxiao.eval.repository.EvalStrategyVersionRepository;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mapping.MappingException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/2/19 10:19
 */

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalPlanServiceImpl implements EvalPlanService {

    @Resource
    private EvalPlanRepository evalPlanRepository;

    @Resource
    private EvalStrategyVersionRepository evalStrategyVersionRepository;

    @Resource
    private EvalRecordService evalRecordService;

    @Resource
    private EvalPlanPipelineHandler evalPlanPipelineHandler;

    @Resource
    private WorkflowProcessService workflowProcessService;

    @Resource
    private EvalProperties evalProperties;

    @Override
    public String create(EvalPlanCreateDTO dto) {
        try {
            log.info("begin create eval plan");
            if (this.evalPlanRepository.countByName(dto.getName()) > 0) {
                throw new LynxiaoException("计划名称不能重复!");
            }
            Optional<GeneratedEvalStrategyVersion> strategyVersion = this.evalStrategyVersionRepository.
                    findByProcessIdAndCategory(dto.getProcessId(), dto.getMode());
            if (strategyVersion.isEmpty()) {
                throw new LynxiaoException("所选归因模式下的策略流程不存在!");
            }
            if (strategyVersion.get().getStatus() != VersionStatus.PUBLISHED) {
                throw new LynxiaoException("所选归因策略版本未发布!");
            }

            // 解析文件流
            List<EvalRecordDTO> evalRecords = readAndDeduplicateFileData(dto.getFile());
            int total = evalRecords.size();
            GeneratedEvalPlan generatedEvalPlan = BeanUtil.copyProperties(dto, GeneratedEvalPlan.class);
            generatedEvalPlan.setTotal(total);
            generatedEvalPlan.setDeleted(Boolean.FALSE);
            generatedEvalPlan.setStatus(EvalPlanStatus.WAIT);
            AuditingEntityUtil.fillCreateValue(generatedEvalPlan);

            String planId = this.evalPlanRepository.save(generatedEvalPlan).getId().toString();
            evalRecords.forEach(evalRecordDTO -> {
                evalRecordDTO.setPlanId(planId);
                evalRecordDTO.setStatus(EvalRecordStatus.UNPROCESSED);
            });
            this.evalRecordService.batchInsert(evalRecords);
            return planId;
        } catch (MappingException e) {
            log.error("create eval plan error, ex:{}", e.getMessage());
            throw new LynxiaoException("上传的文件解析失败！");
        }
    }

    @Override
    public void update(String id, EvalPlanUpdateDTO dto) {
        log.info("update eval plan, dto={}", dto);
        GeneratedEvalPlan evalPlan = this.evalPlanRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("分析计划不存在!"));
        if (!dto.getName().equals(evalPlan.getName())) {
            // 名称发生变化
            if (this.evalPlanRepository.countByName(dto.getName()) > 0) {
                throw new LynxiaoException("计划名称不能重复!");
            }
        }

        Optional<GeneratedEvalStrategyVersion> strategyVersion = this.evalStrategyVersionRepository.
                findByProcessIdAndCategory(dto.getProcessId(), dto.getMode());
        if (strategyVersion.isEmpty()) {
            throw new LynxiaoException("所选归因模式下的策略流程不存在!");
        }
        if (strategyVersion.get().getStatus() != VersionStatus.PUBLISHED) {
            throw new LynxiaoException("所选归因策略版本未发布!");
        }

        // 如果计划状态不是待分析，不可以更新mode和processId
        if (evalPlan.getStatus() != EvalPlanStatus.WAIT) {
            dto.setMode(evalPlan.getMode());
            dto.setProcessId(evalPlan.getProcessId());
        }
        BeanUtil.copyProperties(dto, evalPlan);
        AuditingEntityUtil.fillUpdateValue(evalPlan);
        this.evalPlanRepository.save(evalPlan);
    }

    @Override
    public void deleteById(String id) {
        log.info("delete eval plan, id={}", id);
        Optional<GeneratedEvalPlan> evalPlanOpt = this.evalPlanRepository.findByIdAndDeletedFalse(Long.valueOf(id));
        if (evalPlanOpt.isEmpty()) {
            throw new LynxiaoException("记录不存在！");
        }
        this.evalRecordService.delete(id);
        this.evalPlanRepository.deleteById(Long.valueOf(id));
    }

    @Override
    public Page<EvalPlanDTO> findByPage(String search, Pageable pageable) {
        log.info("find eval plan by page, search={}, pageable={}", search, pageable);
        Page<GeneratedEvalPlan> generatedEvalPlans = StringUtils.hasText(search) ? this.evalPlanRepository.search(search, pageable) : this.evalPlanRepository.findAllByDeletedFalse(pageable);
        return generatedEvalPlans.map(evalPlan -> {
            EvalPlanDTO evalPlanDTO = BeanUtil.copyProperties(evalPlan, EvalPlanDTO.class);
            evalPlanDTO.setId(String.valueOf(evalPlan.getId()));
            return evalPlanDTO;
        });
    }

    @Override
    public EvalPlanDTO findById(String id) {
        log.info("find eval plan, id={}", id);
        GeneratedEvalPlan generatedEvalPlan = this.evalPlanRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("分析计划不存在!"));
        EvalPlanDTO evalPlanDTO = BeanUtil.copyProperties(generatedEvalPlan, EvalPlanDTO.class);
        evalPlanDTO.setId(String.valueOf(generatedEvalPlan.getId()));
        return evalPlanDTO;
    }

    @Override
    public List<String> findDistinctName() {
        log.info("find distinct eval plan group name");
        return this.evalPlanRepository.findDistinctName();
    }

    @Override
    public void startEval(String id) {
        log.info("start eval plan, id={}", id);
        GeneratedEvalPlan evalPlan = this.evalPlanRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("分析计划不存在!"));
        GeneratedEvalStrategyVersion strategyVersion = this.evalStrategyVersionRepository.findByProcessIdAndCategory(evalPlan.getProcessId(), evalPlan.getMode()).orElseThrow(() -> new LynxiaoException("归因策略版本不存在!"));
        if (strategyVersion.getStatus() != VersionStatus.PUBLISHED) {
            throw new LynxiaoException("归因策略版本未发布!");
        }

        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(strategyVersion.getProcessId());
        if (workflowProcess == null) {
            log.error("No workflow process found for planId: {}", id);
            throw new LynxiaoException("未找到对应归因策略的执行流程！");
        }
        if (this.evalPlanPipelineHandler.getTodoSize() >= evalProperties.getPlanSetting().getLimitCount()) {
            throw new LynxiaoException("当前任务数过多，请稍后再试！");
        }
        this.evalPlanRepository.updateStatusAndResult(Long.valueOf(id), EvalPlanStatus.RUNNING, null, null, null);
        try {
            this.evalPlanPipelineHandler.onData(id);
        } catch (InterruptedException e) {
            log.error("start eval plan error, id={}, ex:{}", id, e.getMessage());
            throw new LynxiaoException("分析计划执行失败！");
        }
    }

    @Override
    public Page<EvalRecordInputDTO> findQueryList(String id, Pageable pageable) {
        log.info("find eval plan detail, id={}", id);
        Page<EvalRecordDTO> page = evalRecordService.find(id, pageable, null);
        if (page.isEmpty()) {
            log.info("find eval plan detail, id={}, page is empty", id);
            return Page.empty();
        }
        return page.map(evalRecordDTO -> BeanUtil.copyProperties(evalRecordDTO, EvalRecordInputDTO.class));
    }

    @Override
    public EvalProgressDTO progressEval(String id) {
        log.info("find eval plan progress, id={}", id);
        return this.evalRecordService.count(id);
    }

    @Override
    public Page<EvalRecordResultDTO> findRecords(String id, int status, Pageable pageable) {
        log.info("find eval plan result, id={}", id);
        Page<EvalRecordDTO> page = evalRecordService.find(id, pageable, Collections.singletonList(status));
        if (page.isEmpty()) {
            log.info("find eval plan result, id={}, mode={}, page is empty", id, status);
            return Page.empty();
        }
        return page.map(evalRecordDTO -> BeanUtil.copyProperties(evalRecordDTO, EvalRecordResultDTO.class));
    }

    @Override
    public void exportQueryList(String id, HttpServletResponse response) {
        log.info("export eval plan query list, id={}", id);
        export(id, EvalPlanExportMode.EXPORT_WITH_NO_RESULT, -1, response);
    }

    @Override
    public void exportRecords(String id, int status, HttpServletResponse response) {
        log.info("export eval plan result, id={}, status={}", id, status);
        export(id, EvalPlanExportMode.EXPORT_WITH_RESULT, status, response);
    }


    @Override
    public List<TraceRecordDTO> findRecordTrace(String recordId) {
        log.info("find eval record trace, recordId={}", recordId);
        EvalRecordDTO evalRecord = this.evalRecordService.findById(recordId);
        if (evalRecord == null) {
            log.error("find eval record trace error, recordId={} is not exist", recordId);
            throw new LynxiaoException("记录不存在！");
        }

        //将全链路调用轨迹转换为前端需要的格式
        List<TraceLogItem> traceLogItems = JSONArray.parseArray(JSONArray.toJSONString(evalRecord.getEvalTrace()), TraceLogItem.class);
        return TraceLogUtils.convertTraceLog2TraceRecordDTO(traceLogItems);
    }

    @Override
    public void downLoadTemplate(Integer mode, HttpServletResponse response) {
        String fileName = "分析计划模板.xlsx";

        // 定义表头，按模式区分
        String[] headers = null;
        if (mode == 0) {
            // 竞品 good
            headers = new String[]{"query", "goodUrl", "region", "traceId"};
        } else if (mode == 1) {
            // 自研bad
            headers = new String[]{"query", "goodUrl", "badUrl", "region", "traceId"};
        }
        try (Workbook workbook = new XSSFWorkbook()) {

            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFileName + "\"");

            Sheet sheet = workbook.createSheet("Template");
            // 设置样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.BLACK.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < Objects.requireNonNull(headers).length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new LynxiaoException("download error", e);
        }
    }

    private List<EvalRecordDTO> readAndDeduplicateFileData(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream(); Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = Optional.ofNullable(workbook.getSheetAt(0))
                    .orElseThrow(() -> new LynxiaoException("Excel文件为空或数据不足"));

            if (sheet.getPhysicalNumberOfRows() < 2) {
                throw new LynxiaoException("Excel文件为空或数据不足");
            }

            // 获取表头字段名称
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new LynxiaoException("Excel文件表头为空");
            }
            List<ExcelHeader> headers = buildExcelHeader(headerRow);
            // 解析数据并去重
            List<EvalRecordDTO> records = parseRecords(sheet, headers);
            Set<String> uniqueSet = new HashSet<>();
            return records.stream().filter(record -> uniqueSet.add(record.getInput().toString())).collect(Collectors.toList());

        } catch (IOException e) {
            throw new LynxiaoException("eval plan parseFile error. ex:", e);
        }
    }

    private List<ExcelHeader> buildExcelHeader(Row headerRow) {
        List<String> headers = ExcelUtils.getHeaders(headerRow);
        // 构建表头对象列表, 当name为”traceId“时， isNullable为true
        return headers.stream().map(header -> new ExcelHeader(header, "traceId".equals(header))).collect(Collectors.toList());

    }

    private List<EvalRecordDTO> parseRecords(Sheet sheet, List<ExcelHeader> headers) {
        List<EvalRecordDTO> records = new ArrayList<>();
        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            EvalRecordDTO record = new EvalRecordDTO();
            JSONObject jsonObject = new JSONObject();

            for (int j = 0; j < headers.size(); j++) {
                Cell cell = row.getCell(j, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                String cellValue = ExcelUtils.getCellValue(cell);
                if (!StringUtils.hasText(cellValue) && !headers.get(j).isNullable()) {
                    log.error("Excel文件第{}行第{}列数据为空", i + 1, j + 1);
                    throw new LynxiaoException("Excel文件第" + (i + 1) + "行第" + (j + 1) + "列数据为空");
                }
                jsonObject.put(headers.get(j).getName(), cellValue);
            }
            record.setInput(jsonObject);
            records.add(record);
        }
        return records;
    }

    private void export(String id, int mode, int status, HttpServletResponse response) {
        GeneratedEvalPlan evalPlan = evalPlanRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("分析计划不存在!"));

        Page<EvalRecordDTO> page = evalRecordService.find(id, Pageable.unpaged(), EvalPlanExportMode.EXPORT_WITH_RESULT == mode ?
                Collections.singletonList(status) : null);
        if (page.isEmpty()) {
            throw new LynxiaoException("没有数据可导出！");
        }

        List<EvalRecordDTO> records = page.getContent();
        List<String> titles = extractTitles(records, mode, status);
        List<List<String>> data = extractData(records, mode);
        String header = evalPlan.getMode() == 0 ? "GoodCase" : "BadCase";
        log.info("title={}, data={}", titles, data);
        DownloadUtils.exportToExcel(response, titles, data, header + "问题定位环节", "归因分析数据");
    }

    /**
     * 提取表头
     */
    private List<String> extractTitles(List<EvalRecordDTO> records, int mode, int status) {
        if (records == null || records.isEmpty()) {
            throw new LynxiaoException("Records list is empty or null");
        }

        Set<String> inputKey = records.getFirst().getInput().keySet();
        List<String> titles = new ArrayList<>(inputKey);
        if (EvalPlanExportMode.EXPORT_WITH_RESULT == mode) {
            if (status == EvalRecordStatus.FAILED) {
                titles.add("errorMsg");
            } else if (status == EvalRecordStatus.SUCCESS) {
                JSONArray evalResults = getEvalResults(records);
                for (int i = 0; i < evalResults.size(); i++) {
                    JSONObject evalResultItem = evalResults.getJSONObject(i);
                    if (evalResultItem == null) {
                        continue;
                    }
                    String name = evalResultItem.getString("name");
                    if (name != null) {
                        titles.add(name);
                    }
                }
            }
        }
        return titles;
    }

    /**
     * 获取分析结果中第一个evalResults不为空的值
     */
    @NotNull
    private static JSONArray getEvalResults(List<EvalRecordDTO> records) {
        EvalRecordDTO evalRecordDTO = null;

        for (EvalRecordDTO record : records) {
            JSONArray evalResults = record.getEvalResult();
            if (evalResults != null && !evalResults.isEmpty()) {
                evalRecordDTO = record;
                break;
            }
        }

        if (evalRecordDTO == null) {
            throw new LynxiaoException("导出数据异常。");
        }

        return evalRecordDTO.getEvalResult();
    }

    private List<List<String>> extractData(List<EvalRecordDTO> records, int mode) {
        if (records == null || records.isEmpty()) {
            throw new LynxiaoException("Records list is empty or null");
        }

        List<List<String>> data = new ArrayList<>();
        Set<String> inputKey = records.getFirst().getInput().keySet();

        for (EvalRecordDTO record : records) {
            List<String> rowList = new ArrayList<>();
            // 取出input值
            for (String key : inputKey) {
                rowList.add(record.getInput().getOrDefault(key, "-").toString());
            }
            if (EvalPlanExportMode.EXPORT_WITH_RESULT == mode) {
                // 判断记录是否处理错误
                if (record.getStatus() == EvalRecordStatus.FAILED) {
                    rowList.add(record.getErrorMsg() == null ? " " : record.getErrorMsg());
                } else {
                    // 取出evalResult值
                    JSONArray evalResult = record.getEvalResult();
                    for (int i = 0; i < evalResult.size(); i++) {
                        if (evalResult.getJSONObject(i) != null) {
                            rowList.add(String.valueOf(evalResult.getJSONObject(i).getOrDefault("value", " ")));
                        }
                    }
                }
            }
            data.add(rowList);
        }
        return data;
    }

    /**
     * excel表头信息
     */
    @Setter
    @Getter
    @AllArgsConstructor
    private static class ExcelHeader {
        /**
         * 表头名称
         */
        private String name;

        /**
         * 表示该字段是否可为空
         */
        private boolean isNullable;
    }

}
