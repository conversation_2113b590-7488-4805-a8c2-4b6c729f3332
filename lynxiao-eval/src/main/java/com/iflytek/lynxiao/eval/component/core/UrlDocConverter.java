package com.iflytek.lynxiao.eval.component.core;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.content.ContentApi;
import com.iflytek.lynxiao.eval.component.domain.PreEvalDoc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据url获取文档内容
 *
 * <AUTHOR>  2025/3/6 09:20
 */
@Slf4j
@Component
public class UrlDocConverter {
    private final ContentApi contentApi;

    public UrlDocConverter( ContentApi contentApi) {
        this.contentApi = contentApi;
    }

    /**
     * 获取文档内容
     */
    public Optional<PreEvalDoc> fetchDoc(String url) {
        long cost = System.currentTimeMillis();
        try {
            return crawlDocContent(url);

        } catch (Exception e) {
            log.error(String.format("爬取文档内容失败. url=%s msg:%s", url, e.getMessage()), e.getMessage());
            return Optional.empty();
        } finally {
            log.info("获取文档内容耗时: {} ms, url: {}", System.currentTimeMillis() - cost, url);
        }
    }

    private Optional<PreEvalDoc> crawlDocContent(String url) {
        log.debug("开始爬取文档内容，url: {}", url);

        Optional<JSONObject> dataOp = this.contentApi.urlDocConvert(url);
        if (dataOp.isEmpty()) {
            log.error("调用内容服务获取文档内容失败，url: {}", url);
            return Optional.empty();
        }
        JSONObject data = dataOp.get();
        JSONArray magazines = data.getJSONArray("magazines");
        if (CollectionUtils.isEmpty(magazines) || magazines.getJSONObject(0) == null) {
            return Optional.empty();
        }
        JSONObject result = magazines.getJSONObject(0);
        Long id = result.getLong("id");
        String title = result.getString("title");
        String content = result.getJSONObject("content").getString("readableText");
        PreEvalDoc preEvalDoc = parseURL(url);
        preEvalDoc.set_id(id).setTitle(title).setContent(content);
        return Optional.of(preEvalDoc);
    }

    private PreEvalDoc parseURL(String url) {
        String regex = "^(https?)://([^/]+)(/.*)$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        PreEvalDoc preEvalDoc = new PreEvalDoc();
        if (matcher.matches()) {
            // 解析出协议
            preEvalDoc.setProtocol(matcher.group(1));
            preEvalDoc.setDomain(matcher.group(2));
            preEvalDoc.setPath(matcher.group(3));
            return preEvalDoc;
        } else {
            throw new LynxiaoException("url解析失败");
        }
    }
}