package com.iflytek.lynxiao.eval.dto.standard;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 测评标注反馈视图对象
 *
 * <AUTHOR> 2025/2/19 10:13
 */
@Getter
@Setter
public class MarkFeedbackDTO extends Jsonable {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 选项类型
     * @see com.iflytek.lynxiao.portal.eval.domain.MarkOptionType
     */
    private int optType;

    /**
     * 是否必填
     */
    private boolean required;

    /**
     * 描述
     */
    private String description;

    /**
     * 反馈内容
     */
    private List<MarkFeedbackDetailDTO> content;

}