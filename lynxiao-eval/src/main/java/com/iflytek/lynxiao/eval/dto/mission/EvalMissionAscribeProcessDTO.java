package com.iflytek.lynxiao.eval.dto.mission;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.time.Instant;


@Getter
@Setter
public class EvalMissionAscribeProcessDTO extends Jsonable {

    /**
     * 成功数量
     */
    private int success;

    /**
     * 失败数量
     */
    private int fail;

    /**
     * 总数量
     */
    private int total;

    /**
     * @see com.iflytek.lynxiao.portal.eval.domain.AscribeStatus
     */
    private Integer status;

    /**
     * 计划的开始执行时间
     */
    private Instant startTime;

    /**
     * 计划的结束执行时间
     */
    private Instant endTime;

}
