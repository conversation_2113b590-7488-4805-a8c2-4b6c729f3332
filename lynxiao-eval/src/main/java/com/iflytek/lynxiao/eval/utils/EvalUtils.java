package com.iflytek.lynxiao.eval.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.domain.WorkflowNode;
import com.iflytek.turing.astrolink.service.dto.WorkflowComponent;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessNode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * 归因分析工具类
 *
 * <AUTHOR> 2025/3/6 15:33
 */

public class EvalUtils {

    /**
     * 判断文档集合中是否有指定的doc
     *
     * @param docs 文档集合
     * @param id   指定文档id
     * @param url  指定文档url
     * @return
     */
    public static boolean isExist(List<JSONObject> docs, String id, String url) {
        if (StringUtils.isNotBlank(id)) {
            return isIdExist(docs, id);
        } else {
            return isUrlExist(docs, url);
        }
    }

    /**
     * url是否在召回的docs中
     */
    public static boolean isUrlExist(List<JSONObject> docs, String url) {
        if (docs != null) {
            for (JSONObject doc : docs) {
                if (url.equals(buildUrl(doc))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * id是否在召回的docs中
     */
    public static boolean isIdExist(List<JSONObject> docs, String id) {
        if (docs != null) {
            for (JSONObject doc : docs) {
                if (id.equals(doc.getString("id"))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 生成完整的url
     */
    public static String buildUrl(JSONObject doc) {
        if (doc != null) {
            if (doc.containsKey("url")) {
                return doc.getString("url");
            } else {
                return doc.getString("protocol") + "://" + doc.getString("domain") + doc.getString("path");
            }
        }
        throw new PandoraException("doc is null.");
    }

    /**
     * 从流程图中获取关注的节点列表
     *
     * @param workflowProcess 流程拓扑
     */
    public static List<WorkflowNode> buildNeedAnalysisNodes(WorkflowProcess workflowProcess) {
        List<WorkflowNode> workflowNodes = new ArrayList<>();
        fillWorkflowNodeIds(workflowNodes, workflowProcess, StringUtils.EMPTY);

        //workflowNodes ids去重
        for (WorkflowNode node : workflowNodes) {
            List<String> uniqueIds = new ArrayList<>(new HashSet<>(node.getIds()));
            node.setIds(uniqueIds);
        }

        //workflowNodes 根据EvalUtils.needAnalysisCodes中进行code排序
        workflowNodes.sort(Comparator.comparingInt(o -> WorkflowUtil.needAnalysisCodes().indexOf(o.getCode())));

        return workflowNodes;
    }

    /**
     * 解析url
     *
     * @param urlStr url字符串
     * @return UrlDomain对象
     */
    public static UrlDomain parseUrl(String urlStr) {
        if (StringUtils.isBlank(urlStr)) {
            throw new LynxiaoException("url is empty.");
        }

        try {
            URI url = new URI(urlStr);
            UrlDomain urlDomain = new UrlDomain();
            urlDomain.setDomain(url.getPort() == -1 ? url.getHost() : url.getHost() + ":" + url.getPort());
            urlDomain.setPath(StringUtils.isBlank(url.getRawQuery()) ? url.getRawPath() : url.getRawPath() + "?" + url.getRawQuery());
            urlDomain.setProtocol(url.getScheme());
            return urlDomain;
        } catch (URISyntaxException e) {
            throw new LynxiaoException(String.format("归因分析解析url失败, url:%s", urlStr));
        }
    }


    /**
     * 递归遍历流程，匹配指定节点，填充节点id
     *
     * @param workflowNodes 需要填充的节点列表
     * @param process       当前流程拓扑
     * @param parentNodeId  父节点id  当为 "" 时表示为最外层流程节点
     */
    private static void fillWorkflowNodeIds(List<WorkflowNode> workflowNodes, WorkflowProcess process, String parentNodeId) {
        if (process == null || process.getNodeList() == null) {
            return;
        }
        for (WorkflowProcessNode node : process.getNodeList()) {
            WorkflowComponent component = node.getComponent();
            String code = component.getCode();
            String id = node.getId();

            // 如果是需要分析的组件，添加到workflowNodes中 如果已经存在该节点，则直接添加id
            if (WorkflowUtil.needAnalysisCodes().contains(code)) {
                if (workflowNodes.stream().anyMatch(item -> item.getCode().equals(code)
                        && item.getName().equals(node.getName())
                        && item.getParentNodeId().equals(parentNodeId))) {
                    // 如果已经存在该节点，则直接添加id
                    workflowNodes.stream()
                            .filter(item -> item.getCode().equals(code) && item.getName().equals(node.getName()))
                            .findFirst()
                            .ifPresent(item -> item.getIds().add(id));
                    continue;
                }

                // 不存在于workflowNodes中，添加
                WorkflowNode workflowNode = new WorkflowNode();
                workflowNode.setCode(code);
                workflowNode.setName(component.getName());
                workflowNode.setParentNodeId(parentNodeId);
                workflowNode.getIds().add(id);
                workflowNodes.add(workflowNode);
            }

            if ("compound".equals(component.getType()) && component.getInputArgs() != null) {
                // 遍历处理输入参数
                component.getInputArgs().stream()
                        .filter(inputArg -> "process".equals(inputArg.getStyle()))
                        .forEach(inputArg -> {
                            WorkflowProcess workflowProcess = JSONObject.parseObject(
                                    JSON.toJSONString(inputArg.getValue()),
                                    WorkflowProcess.class
                            );
                            fillWorkflowNodeIds(workflowNodes, workflowProcess, id);
                        });
            }
        }
    }

    @Getter
    @Setter
    public static class UrlDomain {
        // 地址
        private String domain;

        // 协议
        private String protocol;

        // 路径
        private String path;
    }


    /**
     * 判断两个列表是否包含相同的元素
     */
    public static boolean isSameElement(List<String> list1, List<String> list2) {
        if (list1 == null || list2 == null) {
            return false;
        }
        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);
        return set1.size() == set2.size() && set1.containsAll(set2);
    }

}
