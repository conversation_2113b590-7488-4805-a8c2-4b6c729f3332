package com.iflytek.lynxiao.eval.entity;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import skynet.boot.mongo.domain.AbstractAuditingEntity;

@Getter
@Setter
@Document("eval_mark_target")
public class MarkTargetEntity extends AbstractAuditingEntity<String> {

    @Field("trace_id")
    private String traceId;

    /**
     * 测评记录表id
     */
    @Field("mark_record_id")
    private String markRecordId;

    /**
     * 流程id（支持场景策略和产品方案）
     */
    @Field("process_id")
    private String processId;

    /**
     * 流程请求参数（payload对象数据）
     */
    @Field("process_param")
    private JSONObject processParam;

    /**
     * 标注目录策略配置id
     */
    @Field("strategy_id")
    private String strategyId;

    /**
     * 多query标注任务id
     */
    @Field("mission_id")
    private String missionId;

    /**
     * 用户所属的组
     */
    @Field("user_group")
    private String userGroup;

    /**
     * 区域环境编码
     */
    @Field("region_code")
    private String regionCode;

    /**
     * 用户account
     */
    @Field("account")
    private String account;

    /**
     * 是否有召回结果
     */
    @Field("recalled")
    private boolean recalled;

    /**
     * 标注目标
     */
    @Field("data")
    private MarkTargetData data;

    /**
     * 元数据
     */
    @Field("metadata")
    private JSONObject metadata;

}
