package com.iflytek.lynxiao.eval.dto.mark.record;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetRecall;
import com.iflytek.lynxiao.eval.domain.mark.ScoringRank;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultRecall;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultRecallDTO;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;


/**
 * 最终召回结果的标注记录
 */
@Slf4j
@Setter
@Getter
public class RecallRecordDTO extends Jsonable {

    /**
     * recall doc
     */
    private JSONObject doc;

    /**
     * 当前doc在全链路中各个节点的位次与得分
     */
    private List<ScoringRank> scoringRanks;

    /**
     * 标注结果
     */
    private MarkResultRecallDTO markResult;

    public static List<RecallRecordDTO> of(List<MarkResultEntity> markResultEntities, MarkTargetEntity markTargetEntity) {
        // 先取出 target
        List<MarkTargetRecall> markTargetRecalls = markTargetEntity.getData().getRecallList();
        if (CollectionUtils.isEmpty(markTargetRecalls)) {
            return new ArrayList<>(0);
        }

        // 根据docIdx在标注结果中找到对应的标注结果
        List<RecallRecordDTO> response = new ArrayList<>();
        for (int idx = 0; idx < markTargetRecalls.size(); idx++) {
            MarkTargetRecall target = markTargetRecalls.get(idx);
            RecallRecordDTO record = getRecallRecordDTO(markResultEntities, target, idx);
            response.add(record);
        }

        return response;
    }

    /**
     * 根据docIdx在标注结果中找到对应的标注结果
     *
     * @param markResultEntities 标注结果列表
     * @param targetRecall       待完善的测评对象-召回结果
     * @param idx                doc在召回结果中的排序
     */
    private static RecallRecordDTO getRecallRecordDTO(List<MarkResultEntity> markResultEntities, MarkTargetRecall targetRecall, int idx) {
        RecallRecordDTO record = BeanUtil.copyProperties(targetRecall, RecallRecordDTO.class);

        if (CollectionUtils.isEmpty(markResultEntities)) {
            // 如果没有标注结果，直接返回
            return record;
        }

        for (MarkResultEntity markResultEntity : markResultEntities) {

            if (markResultEntity.getData() == null || markResultEntity.getData().getRecall() == null
                    || idx != markResultEntity.getData().getRecall().getDocIdx()) {
                continue;
            }

            MarkResultRecall recall = markResultEntity.getData().getRecall();
            // 根据doc位次找到对应的标注结果
            MarkResultRecallDTO markResultRecallDTO = BeanUtil.copyProperties(recall, MarkResultRecallDTO.class);
            markResultRecallDTO.setResultId(markResultEntity.getId());
            record.setMarkResult(markResultRecallDTO);
        }

        return record;
    }
}
