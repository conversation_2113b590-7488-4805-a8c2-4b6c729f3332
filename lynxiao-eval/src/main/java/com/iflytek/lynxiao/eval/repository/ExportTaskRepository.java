package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.entity.EvalSearchApiExportTaskEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 搜索结果管理导出任务记录
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class)
public class ExportTaskRepository {

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;

    private final String collectionName = "eval_searchApi_export_task";

    public void save(EvalSearchApiExportTaskEntity entity) {
        platformMongoTemplate.save(entity);
    }

    public Page<EvalSearchApiExportTaskEntity> page(Pageable pageable) {
        Query query = new Query();
        long totalCount = platformMongoTemplate.count(query, EvalSearchApiExportTaskEntity.class, collectionName);
        if (0 == totalCount) {
            return new PageImpl<>(List.of(), pageable, 0);
        }
        query.with(pageable);
        List<EvalSearchApiExportTaskEntity> exportTaskEntities = platformMongoTemplate.find(query, EvalSearchApiExportTaskEntity.class, collectionName);
        return new PageImpl<>(exportTaskEntities, pageable, totalCount);
    }

    public List<EvalSearchApiExportTaskEntity> findByIds(List<String> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(ids));
        return platformMongoTemplate.find(query, EvalSearchApiExportTaskEntity.class, collectionName);
    }

    public void removeByIds(List<String> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(ids));
        platformMongoTemplate.remove(query, EvalSearchApiExportTaskEntity.class, collectionName);
    }


}
