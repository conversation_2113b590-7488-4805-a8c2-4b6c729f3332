package com.iflytek.lynxiao.eval.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 测评记录
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "eval_mark_record")
public class GeneratedEvalMarkRecord extends AbstractAuditingEntity<Long> {
    /**
     * 测评的query
     */
    @Column(name = "query")
    private String query;
    /**
     * 用户account
     */
    @Column(name = "account")
    private String account;
    /**
     * 测评模式，1:体验模式 2:单问题模式  3:测评任务模式
     */
    @Column(name = "mode")
    private Integer mode;
    /**
     * 测评任务id
     */
    @Column(name = "mission_id")
    private Long missionId;
    /**
     * 组名
     */
    @Column(name = "user_group")
    private String userGroup;
    /**
     * 是否完成自动归因
     */
    @Column(name = "ascribed")
    private Boolean ascribed;
    /**
     * 自动归因是否成功
     */
    @Column(name = "ascribe_success")
    private Boolean ascribeSuccess;
    /**
     * 是否标注完成
     */
    @Column(name = "finish")
    private Boolean finish;
    /**
     * query类弃标
     */
    @Column(name = "query_ignore")
    private String queryIgnore;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}