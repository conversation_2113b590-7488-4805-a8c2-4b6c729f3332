package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMarkRecord;
import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalMarkRecordRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EvalMarkRecordRepository extends GeneratedEvalMarkRecordRepository {

    @Query("SELECT m FROM GeneratedEvalMarkRecord m WHERE m.finish is false AND m.missionId is null AND m.account = :account AND m.mode != 1")
    List<GeneratedEvalMarkRecord> findUnfinishedByAccountWithoutMissionId(@Param("account") String account);

    @Query("SELECT m FROM GeneratedEvalMarkRecord m WHERE m.finish is false AND m.missionId = :missionId AND m.account = :account AND m.mode != 1")
    List<GeneratedEvalMarkRecord> findUnfinishedByAccountAndMissionId(@Param("account") String account, @Param("missionId") Long missionId);

    @Query("SELECT m FROM GeneratedEvalMarkRecord m WHERE m.missionId = :missionId and m.account = :account and m.finish is true")
    List<GeneratedEvalMarkRecord> findAllFinishedByMissionIdAndAccount(@Param("missionId") Long missionId, @Param("account") String account);

    @Query("SELECT m FROM GeneratedEvalMarkRecord m WHERE m.missionId = :missionId and m.finish is true")
    List<GeneratedEvalMarkRecord> findByMissionIdAndDeletedFalse(@Param("missionId") Long missionId);

    @Modifying
    @Query("UPDATE GeneratedEvalMarkRecord m SET m.ascribeSuccess = :ascribe, m.ascribed = TRUE WHERE m.id = :id")
    void updateAscribe(@Param("id") Long id, @Param("ascribe") boolean ascribe);
}