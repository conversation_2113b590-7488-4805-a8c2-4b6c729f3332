package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalFieldConfigRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EvalFieldConfigRepository extends GeneratedEvalFieldConfigRepository {

    /**
     * 根据ID查找未删除的记录
     */
    Optional<GeneratedEvalFieldConfig> findByIdAndDeletedFalse(Long id);

    /**
     * 查找所有未删除的记录
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false")
    List<GeneratedEvalFieldConfig> findAllByDeletedFalse();

    /**
     * 分页查找所有未删除的记录
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false")
    Page<GeneratedEvalFieldConfig> findAllByDeletedFalse(Pageable pageable);

    /**
     * 根据搜索条件分页查询
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND " +
            "(LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(m.field) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(m.category) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedEvalFieldConfig> search(@Param("search") String search, Pageable pageable);

    /**
     * 根据分类查询
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND m.category = :category")
    List<GeneratedEvalFieldConfig> findByCategory(@Param("category") String category);

    /**
     * 根据字段名查询
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND m.field = :fieldName")
    Optional<GeneratedEvalFieldConfig> findByFieldName(@Param("fieldName") String fieldName);

    /**
     * 统计字段名的数量（用于检查重复）
     */
    @Query("SELECT COUNT(m) FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND m.field = :fieldName")
    long countByFieldName(@Param("fieldName") String fieldName);

    /**
     * 统计字段名的数量（排除指定ID）
     */
    @Query("SELECT COUNT(m) FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND m.field = :fieldName AND m.id != :id")
    long countByFieldNameAndIdNot(@Param("fieldName") String fieldName, @Param("id") Long id);

    /**
     * 逻辑删除
     */
    @Modifying
    @Query("UPDATE GeneratedEvalFieldConfig m SET m.deleted = TRUE WHERE m.id = :id")
    void deleteById(@Param("id") Long id);

    /**
     * 查询所有分类
     */
    @Query("SELECT DISTINCT m.category FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND m.category IS NOT NULL")
    List<String> findDistinctCategories();

    /**
     * 根据名称和字段分页查询
     */
    @Query("SELECT m FROM GeneratedEvalFieldConfig m WHERE m.deleted is false AND (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.field) LIKE LOWER(CONCAT('%', :search, '%')) )")
    Page<GeneratedEvalFieldConfig> findByNameAndField(@Param("search") String search,
                                                      Pageable pageable);
}