package com.iflytek.lynxiao.eval.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.Instant;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 测评任务表
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "eval_mission")
public class GeneratedEvalMission extends AbstractAuditingEntity<Long> {
    /**
     * 名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 分组
     */
    @Column(name = "label")
    private String label;
    /**
     * 分组个数
     */
    @Column(name = "group_number")
    private Integer groupNumber;
    /**
     * 状态 1：在线  2：离线
     */
    @Column(name = "type")
    private Integer type;
    /**
     * query集id
     */
    @Column(name = "query_group_id")
    private Long queryGroupId;
    /**
     * 活动开始时间
     */
    @Column(name = "begin_time")
    private Instant beginTime;
    /**
     * 活动结束时间
     */
    @Column(name = "end_time")
    private Instant endTime;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 所属树目录code
     */
    @Column(name = "catalog_code")
    private String catalogCode;
    /**
     * 测评标准id
     */
    @Column(name = "standard_id")
    private Long standardId;
    /**
     * 测评标准id的具体测评配置
     */
    @Column(name = "standard_config")
    private String standardConfig;
    /**
     * 归因0：待分析；1：正在分析；2：已完成；3：已完成（但有失败）
     */
    @Column(name = "ascribe_status")
    private Integer ascribeStatus;
    /**
     * 归因开始时间
     */
    @Column(name = "ascribe_start_ts")
    private Instant ascribeStartTs;
    /**
     * 归因结束时间
     */
    @Column(name = "ascribe_end_ts")
    private Instant ascribeEndTs;
    /**
     * 扩展字段
     */
    @Column(name = "extend_fields")
    private String extendFields;
    /**
     * 策略配置
     */
    @Column(name = "strategy_config")
    private String strategyConfig;
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;
    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}