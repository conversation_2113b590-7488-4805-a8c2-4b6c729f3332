package com.iflytek.lynxiao.eval.autogen.generated.domain;

import skynet.boot.mysql.domain.AbstractAuditingEntity;
import skynet.boot.common.utils.IdUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.PrePersist;

/**
 * 关注节点配置表
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "eval_concern_node")
public class GeneratedEvalConcernNode extends AbstractAuditingEntity<Long> {
    /**
     * 节点名称，例如：文本召回
     */
    @Column(name = "name")
    private String name;
    /**
     * 节点编码，例如：recall-textrc
     */
    @Column(name = "code")
    private String code;
    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;
    /**
     * 节点入参，针对doc的入参字段
     */
    @Column(name = "input_filed")
    private String inputFiled;
    /**
     * 节点出参，针对doc的出参字段
     */
    @Column(name = "output_field")
    private String outputField;
    /**
     * 得分字段
     */
    @Column(name = "score_field")
    private String scoreField;
    /**
     * 位次字段
     */
    @Column(name = "index_field")
    private String indexField;
    /**
     * 是否可mock
     */
    @Column(name = "can_mock")
    private Boolean canMock;
    /**
     * 是否在标注页面展示
     */
    @Column(name = "show_in_recall")
    private Boolean showInRecall;
    /**
     * 是否在全链路中展示
     */
    @Column(name = "show_in_trace")
    private Boolean showInTrace;
    /**
     * 默认值
     */
    @Column(name = "default_value")
    private String defaultValue;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 是否删除
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        } 
    }


}