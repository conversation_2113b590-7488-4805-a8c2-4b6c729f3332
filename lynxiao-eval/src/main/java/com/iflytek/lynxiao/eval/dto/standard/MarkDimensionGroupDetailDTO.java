package com.iflytek.lynxiao.eval.dto.standard;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注维度集详情DTO
 */
@Setter
@Getter
public class MarkDimensionGroupDetailDTO extends Jsonable {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 定义
     */
    private String definition;

    /**
     * 选项测评字段
     */
    private String optEvalFields;

    /**
     * 示例
     */
    private String sample;

    /**
     * 默认值  存储选项的code
     */
    private String defaultValue;

    /**
     * 备注
     */
    private String description;

    /**
     * 选项类型
     *
     * @see com.iflytek.lynxiao.portal.eval.domain.MarkOptionType
     */
    private int optType;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 层级
     * @see com.iflytek.lynxiao.portal.eval.domain.DimsGroupDetailLevel
     */
    private int level;

    private boolean value;

    /**
     * 子项列表
     */
    private List<MarkDimensionGroupDetailDTO> children;
}
