package com.iflytek.lynxiao.eval.autogen.generated.service.mapper;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigDTO;
import skynet.boot.common.mapper.EntityMapper;

import org.mapstruct.Builder;
import org.mapstruct.Mapper;

@Mapper(
    componentModel = "spring",
    uses = {},
    builder = @Builder(disableBuilder = true))
public interface GeneratedEvalFieldConfigMapper extends EntityMapper<EvalFieldConfigDTO, GeneratedEvalFieldConfig> {

}