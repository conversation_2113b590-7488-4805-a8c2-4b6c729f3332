package com.iflytek.lynxiao.eval.domain.mark.result;

import com.iflytek.lynxiao.eval.component.domain.EvalResultItem;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class MarkResultRecallAscribe extends Jsonable {

    /**
     * 归因分析结果
     */
    private List<EvalResultItem> ascribeList;

    /**
     * 错误信息
     */
    private String errMsg;
}
