package com.iflytek.lynxiao.eval.dto.statistics;

import com.iflytek.lynxiao.eval.domain.ComparisonOperator;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultType;
import lombok.*;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注结果查询条件，所有字段可选
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkResultQueryDTO extends Jsonable {
    /**
     * topK, 默认5
     */
    private Integer topK = 5;

    /**
     * 检索条件： account 、 query 或者 流程名称
     */
    private String search;

    /**
     * 场景流程id
     */
    private String sceneProcessId;

    /**
     * 场景流程名称
     */
    private String sceneProcessName;

    /**
     * 标注结果类型
     *
     * @see MarkResultType
     */
    private List<String> types;

    /**
     * 标注开始时间
     */
    private String startTime;

    /**
     * 标注结束时间
     */
    private String endTime;

    /**
     * 测评任务id, 非空时表示查询测评任务下的标注结果统计
     */
    private String missionId;

    /**
     * 从分析列表的哪一列点入(针对多query测评任务存在)
     *
     * @see com.iflytek.lynxiao.eval.domain.MarkResultSearchTypeEnum
     */
    private String column;

    /**
     * 标注人  组或人
     */
    private String markUser;

    /**
     * 1:组  2:人
     */
    private Integer userType;

    /**
     * 分组名  分组1 、 分组2 ...
     */
    private String userGroup;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 标注记录id列表, 当column有值时， markRecordIds为分析结果对应行的标注记录id列表
     */
    private List<String> markRecordIds;

    /**
     * 策略id列表
     */
    private List<String> strategyIds;

    /**
     * 对比策略A id
     */
    private String contrastStrategyIdA;
    
    /**
     * 对比策略B id
     */
    private String contrastStrategyIdB;

    /**
     * 对比操作符
     */
    private ComparisonOperator operator;
}
