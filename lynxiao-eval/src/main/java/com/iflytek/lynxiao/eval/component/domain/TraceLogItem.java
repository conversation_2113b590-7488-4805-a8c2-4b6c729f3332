package com.iflytek.lynxiao.eval.component.domain;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;


/**
 * 流程拓扑组件的执行结果（包含需要分析的数据，如召回的文档列表等）
 */
@Getter
@Setter
@Accessors(chain = true)
public class TraceLogItem extends Jsonable {

    /**
     * 节点名
     */
    private String name;

    /**
     * 组件编码 例如：recall-textrc
     */
    private String code;

    /**
     * 流程拓扑组件id
     */
    private String nodeId;

    /**
     * 文档得分字段
     */
    private String scoreField;

    /**
     * 文档排序字段
     */
    private String indexField;

    /**
     * 请求参数文档列表
     * 初始验证请求完整结构
     */
    @JSONField(serialize = false)
    private WorkflowInvokeParseResult reqData;

    /**
     * 初始验证结果文档集合
     */
    private List<JSONObject> docs;

    /**
     * 组件正常返回的文档数量
     */
    private Integer docSize;

    /**
     * mock验证结果文档集合（包含mock文档和真实召回的文档）
     */
    private List<JSONObject> mockDocs;


    /**
     * 召回节点不参与mock，因此不需要记录初始入参
     *
     * @param reqData
     */
    public TraceLogItem setReqData(WorkflowInvokeParseResult reqData) {
        if (!WorkflowUtil.isRecallNode(this.code)) {
            this.reqData = reqData;
        }
        return this;
    }
}
