package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedEvalFieldConfigDTO extends AbstractAuditingEntity<Long> {

    /**
     * 字段名称，例如：标题、内容
     */
    @Schema(title = "字段名称，例如：标题、内容")
    private String name;

    /**
     * 字段，例如：title、content
     */
    @Schema(title = "字段，例如：title、content")
    private String field;

    /**
     * 字段类型，String、long等
     */
    @Schema(title = "字段类型，String、long等")
    private String type;

    /**
     * 字段分类，例如：通用字段、医疗字段等
     */
    @Schema(title = "字段分类，例如：通用字段、医疗字段等")
    private String category;

    /**
     * 排序
     */
    @Schema(title = "排序")
    private Integer idx;

    /**
     * 字段路径，如果为空，默认从doc下取
     */
    @Schema(title = "字段路径，如果为空，默认从doc下取")
    private String path;

    /**
     * 是否在全链路展示
     */
    @Schema(title = "是否在全链路展示")
    private Boolean showInTrace;

    /**
     * 默认值
     */
    @Schema(title = "默认值")
    private String defaultValue;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 样例
     */
    @Schema(title = "样例")
    private String sample;

    /**
     * 是否删除
     */
    @Schema(title = "是否删除")
    private Boolean deleted;
}