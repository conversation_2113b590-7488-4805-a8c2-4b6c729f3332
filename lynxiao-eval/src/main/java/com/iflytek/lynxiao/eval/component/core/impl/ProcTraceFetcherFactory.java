package com.iflytek.lynxiao.eval.component.core.impl;

import com.iflytek.lynxiao.eval.component.core.ProcTraceFetcher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import skynet.boot.pandora.exception.PandoraException;

/**
 * 流程全链路获取器
 */
@Service
public class ProcTraceFetcherFactory {

    private final ApplicationContext applicationContext;

    public ProcTraceFetcherFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取全链路获取器
     * 优先级：traceId > processId
     *
     * @param processId 流程id
     * @param traceId   traceId
     * @return
     */
    public ProcTraceFetcher buildFetcher(String processId, String traceId) {
        if (StringUtils.isNotEmpty(traceId)) {
            return applicationContext.getBean(ProcTraceFetcher4TraceId.class);
        } else if (StringUtils.isNotEmpty(processId)) {
            return applicationContext.getBean(ProcTraceFetcher4ProcessId.class);
        }
        throw new PandoraException("获取全链路获取器失败");
    }

}
