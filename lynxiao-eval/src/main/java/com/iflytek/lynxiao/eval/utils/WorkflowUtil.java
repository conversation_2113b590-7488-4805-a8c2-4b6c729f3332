package com.iflytek.lynxiao.eval.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.component.domain.Docs;
import com.iflytek.lynxiao.eval.component.domain.WorkflowInvokeParseResult;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeDTO;
import com.iflytek.turing.astrolink.service.dto.WorkflowComponent;
import com.iflytek.turing.astrolink.service.dto.WorkflowComponentParameter;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 流程工具类
 *
 * <AUTHOR>  2025/3/6 19:14
 */
@Slf4j
public class WorkflowUtil {

    public final static List<EvalConcernNodeDTO> concernNodeCacheList = new ArrayList<>();

    public static final String REQUEST = "REQUEST";

    public static final String RESPONSE = "RESPONSE";

    public static final String START_NODE = "start-pass-through";

    // 文本召回
    public static final String RECALL_TEXTRC = "recall-textrc";

    // 向量召回
    public static final String RECALL_SEMTRC = "recall-semtrc";

    // 粗排
    public static final String AI_PRERANK = "ai-prerank";

    // 精排
    public static final String AI_RANK = "ai-rank";

    // 重排
    public static final String AI_RERANK = "ai-rerank";

    // 后处理
    public static final String AI_PROCESS = "ai-process";

    public static final List<String> recallNodeCodes = Arrays.asList(RECALL_TEXTRC, RECALL_SEMTRC);

    /**
     * 更新缓存
     */
    public static void updateCache(List<EvalConcernNodeDTO> nodeDTOS) {
        synchronized (concernNodeCacheList) {
            concernNodeCacheList.clear();
            List<EvalConcernNodeDTO> sortedList = nodeDTOS.stream().sorted(Comparator.comparingInt(EvalConcernNodeDTO::getSort)).toList();
            concernNodeCacheList.addAll(sortedList);
        }
    }

    public static List<String> needAnalysisCodes() {
        return concernNodeCacheList.stream().map(EvalConcernNodeDTO::getCode).toList();
    }

    public static Optional<EvalConcernNodeDTO> findByCode(String code) {
        return concernNodeCacheList.stream().filter(item -> item.getCode().equals(code)).findFirst();
    }

    /**
     * 根据节点编码获取节点配置，包含得分字段和位次字段
     *
     * @param code
     * @return
     */
    public static Optional<EvalConcernNodeDTO> findByCodeWithScoreAndIndex(String code) {
        Optional<EvalConcernNodeDTO> concernNodeDTOOp = findByCode(code);

        if (concernNodeDTOOp.isEmpty()) {
            return Optional.empty();
        }

        EvalConcernNodeDTO concernNodeDTO = concernNodeDTOOp.get();

        if (StringUtils.isBlank(concernNodeDTO.getIndexField()) || StringUtils.isBlank(concernNodeDTO.getScoreField())) {
            return Optional.empty();
        }

        return Optional.of(concernNodeDTO);
    }

    /**
     * 根据组件节点编码 从流程中获取组件节点
     */
    public static Optional<WorkflowProcessNode> getNode(WorkflowProcess workflowProcess, String code) {
        if (workflowProcess == null) {
            throw new LynxiaoException("workflowProcess is null");
        }
        List<WorkflowProcessNode> nodeList = workflowProcess.getNodeList();
        for (WorkflowProcessNode node : nodeList) {
            if (node.getComponent().getCode().equals(code)) {
                return Optional.of(node);
            }
        }
        return Optional.empty();
    }

    public static WorkflowProcessNode getNodeNotNull(WorkflowProcess workflowProcess, String code) {
        Optional<WorkflowProcessNode> nodeOptional = WorkflowUtil.getNode(workflowProcess, code);
        if (nodeOptional.isEmpty()) {
            throw new PandoraException(String.format("can not find node. code:%s", code));
        }
        return nodeOptional.get();
    }

    /**
     * 根据节点ID获取节点，如果不存在则返回空
     *
     * @param workflowProcess
     * @param nodeId
     * @return
     */
    public static Optional<WorkflowProcessNode> getNodeById(WorkflowProcess workflowProcess, String nodeId) {
        if (workflowProcess == null) {
            throw new LynxiaoException("workflowProcess is null");
        }
        List<WorkflowProcessNode> nodeList = workflowProcess.getNodeList();
        for (WorkflowProcessNode node : nodeList) {
            if (node.getId().equals(nodeId)) {
                return Optional.of(node);
            }
        }
        return Optional.empty();
    }

    /**
     * 根据节点ID获取节点，如果不存在则抛出异常
     *
     * @param workflowProcess 流程定义
     * @param nodeId          节点ID
     * @return WorkflowProcessNode
     */
    public static WorkflowProcessNode getNodeByIdNotNull(WorkflowProcess workflowProcess, String nodeId) {
        Optional<WorkflowProcessNode> nodeOptional = WorkflowUtil.getNodeById(workflowProcess, nodeId);
        if (nodeOptional.isEmpty()) {
            throw new PandoraException(String.format("can not find node. nodeId:%s", nodeId));
        }
        return nodeOptional.get();
    }

    /**
     * 从节点的入参或出参中解析文档数据
     *
     * @param payload ApiRequest 或 ApiResponse 的 payload
     * @param type    请求类型，REQUEST 或 RESPONSE
     * @Param nodeCode 组件节点编码
     */
    public static WorkflowInvokeParseResult parseDocs(String nodeCode, JSONObject payload, String type) {
        WorkflowInvokeParseResult respParseResult = new WorkflowInvokeParseResult();
        // 获取对应的参数键
        Optional<EvalConcernNodeDTO> concernNodeDTOOptional = findByCode(nodeCode);
        if (concernNodeDTOOptional.isEmpty()) {
            log.warn("未找到节点对应的参数配置. nodeCode:{}", nodeCode);
            return respParseResult;
        }

        EvalConcernNodeDTO concernNodeDTO = concernNodeDTOOptional.get();

        // 根据类型确定使用输入参数键还是输出参数键
        String argKey = REQUEST.equals(type) ? concernNodeDTO.getInputFiled() : concernNodeDTO.getOutputField();
        if (payload == null || CollectionUtils.isEmpty(payload.getJSONArray(argKey))) {
            log.debug("workflow {} payload is null. nodeCode:{}", REQUEST, nodeCode);
            return respParseResult;
        }

        JSONArray dataJsonarray = payload.getJSONArray(argKey);
        List<Docs> dataList = dataJsonarray.toJavaList(Docs.class);

        //将docs数据扁平化
        List<JSONObject> docs = dataList.stream()
                .filter(Objects::nonNull)
                .flatMap(flatMapFunc(nodeCode, type))
                .collect(Collectors.toList());

        AtomicInteger docSize = new AtomicInteger();
        dataList.stream().filter(Objects::nonNull).forEach(item -> docSize.addAndGet(item.getDocs().size()));

        respParseResult.setDocSize(docSize.get());
        respParseResult.setDocs(docs);
        respParseResult.setData(dataList);
        return respParseResult;
    }

    @NotNull
    private static Function<Docs, Stream<? extends JSONObject>> flatMapFunc(String code, String type) {
        return result -> {
            // 根据是否是排序节点，优先取fullDocs中的值，没有再取docs， 非排序节点取docs
            if (WorkflowUtil.isRankNode(code) && RESPONSE.equals(type)) {
                return !CollectionUtils.isEmpty(result.getFullDocs()) ? result.getFullDocs().stream() :
                        CollectionUtils.isEmpty(result.getDocs()) ? Stream.empty() : result.getDocs().stream();
            } else {
                return result.getDocs() != null ? result.getDocs().stream() : Stream.empty();
            }
        };
    }

    /**
     * 嵌套开启所有排序节点full参数
     *
     * @param workflowProcess 流程拓扑
     * @return
     */
    public static WorkflowProcess openRankNodeFullDocs(WorkflowProcess workflowProcess) {
        if (workflowProcess == null || workflowProcess.getNodeList() == null) {
            throw new LynxiaoException("开启排序节点 fullDocs返回失败, 流程拓扑或流程节点为空。");
        }

        List<WorkflowProcessNode> nodeList = workflowProcess.getNodeList();
        for (WorkflowProcessNode node : nodeList) {
            WorkflowComponent component = node.getComponent();
            String code = component.getCode();

            if ("compound".equals(component.getType()) && component.getInputArgs() != null) {
                // 处理嵌套流程
                for (WorkflowComponentParameter inputArg : component.getInputArgs()) {
                    if ("process".equals(inputArg.getStyle())) {
                        WorkflowProcess innerWorkflowProcess = JSONObject.parseObject(
                                JSON.toJSONString(inputArg.getValue()),
                                WorkflowProcess.class
                        );
                        inputArg.setValue(openRankNodeFullDocs(innerWorkflowProcess));
                    }
                }
            }

            if (isRankNode(code)) {
                List<WorkflowComponentParameter> inputArgs = node.getComponent().getInputArgs();

                if (inputArgs != null) {
                    inputArgs.stream().filter(item -> "full".equals(item.getKey()) && item.getType().equals("boolean"))
                            .findFirst()
                            .ifPresentOrElse(item -> item.setValue(true), () -> inputArgs.add(getFullParam()));
                }
            }
        }
        return workflowProcess;
    }


    public static Set<String> findIndexCodes(WorkflowProcess workflowProcess) {
        Set<String> idxCodes = new HashSet<>();
        findIndexCodesRecursive(workflowProcess, idxCodes);

        if (CollectionUtils.isEmpty(idxCodes)) {
            throw new PandoraException(String.format("no idxCodes found in the process:%s", workflowProcess.getCode()));
        }
        return idxCodes;
    }

    /**
     * 递归查找所有节点的indexCodes，包括嵌套的compound组件
     */
    private static void findIndexCodesRecursive(WorkflowProcess workflowProcess, Set<String> idxCodes) {
        if (workflowProcess == null || workflowProcess.getNodeList() == null) {
            return;
        }

        List<WorkflowProcessNode> nodeList = workflowProcess.getNodeList();

        for (WorkflowProcessNode processNode : nodeList) {
            if (processNode.getComponent() == null) {
                continue;
            }

            WorkflowComponent component = processNode.getComponent();
            String code = component.getCode();

            // 处理嵌套的compound组件
            if ("compound".equals(component.getType()) && component.getInputArgs() != null) {
                for (WorkflowComponentParameter inputArg : component.getInputArgs()) {
                    if ("process".equals(inputArg.getStyle())) {
                        try {
                            WorkflowProcess innerWorkflowProcess = JSONObject.parseObject(
                                    JSON.toJSONString(inputArg.getValue()),
                                    WorkflowProcess.class
                            );
                            // 递归处理嵌套流程
                            findIndexCodesRecursive(innerWorkflowProcess, idxCodes);
                        } catch (Exception e) {
                            log.error("parse inner workflow process error.", e);
                        }
                    }
                }
            }

            // 处理召回节点
            if (recallNodeCodes.contains(code)) {
                try {
                    List<WorkflowComponentParameter> params = component.getInputArgs().stream()
                            .filter(item -> "params".equals(item.getKey())).toList();
                    if (!params.isEmpty()) {
                        WorkflowComponentParameter componentParameter = params.getFirst();
                        JSONArray value = JSONArray.from(componentParameter.getValue());
                        value.forEach(item -> {
                            JSONObject valueItemJson = JSONObject.from(item);
                            JSONArray indexCodes = valueItemJson.getJSONArray("indexCodes");
                            idxCodes.addAll(indexCodes.toJavaList(String.class));
                        });
                    }
                } catch (Exception e) {
                    log.error("parse indexCodes error for node: {}", code, e);
                }
            }
        }
    }


    /**
     * 获取流程的开始节点ID
     *
     * @param workflowProcess 流程定义
     * @return 开始节点ID
     */
    public static String findStartNodeId(WorkflowProcess workflowProcess) {
        return getNodeNotNull(workflowProcess, START_NODE).getId();
    }

    /**
     * 判断是否为召回节点
     */
    public static boolean isRecallNode(String compCode) {
        // 是否是召回组件
        return RECALL_TEXTRC.equals(compCode) || RECALL_SEMTRC.equals(compCode);
    }

    /**
     * 判断是否为排序节点
     */
    public static boolean isRankNode(String compCode) {
        return AI_PRERANK.equals(compCode) || AI_RANK.equals(compCode) || AI_RERANK.equals(compCode);
    }

    private static WorkflowComponentParameter getFullParam() {
        WorkflowComponentParameter fullParam = new WorkflowComponentParameter();
        fullParam.setKey("full");
        fullParam.setName("是否返回全量排序结果");
        fullParam.setRequired(false);
        fullParam.setVisible(true);
        fullParam.setValue(true);
        fullParam.setDefaultValue(false);
        fullParam.setType("boolean");
        fullParam.setStyle("switch");
        return fullParam;
    }
}
