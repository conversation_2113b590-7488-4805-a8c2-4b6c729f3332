package com.iflytek.lynxiao.eval.utils;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.dto.MetaLabelDTO;
import com.iflytek.lynxiao.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.eval.domain.mark.ReRankProps;
import com.iflytek.lynxiao.eval.dto.mark.record.TraceDocRecordDTO;
import com.iflytek.lynxiao.eval.dto.mark.record.TraceRecordDTO;
import jakarta.validation.constraints.NotNull;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.iflytek.lynxiao.eval.utils.WorkflowUtil.AI_RERANK;

/**
 * 全链路日志工具类
 */
public class TraceLogUtils {

    /**
     * 全链路日志后处理。
     * 1. 将id从long类型转换为string类型
     * 2. 设置得分与位次字段
     * 3. 重排环节属性解析
     */
    public static void traceLogsProcess(List<TraceLogItem> traceLogs, @NotNull Map<String, MetaLabelDTO> metaLabelDTOMap) {
        // 验证执行轨迹列表不为null
        if (traceLogs == null) {
            throw new PandoraException("构建跟踪结果失败: 执行轨迹列表为null");
        }

        for (TraceLogItem traceLog : traceLogs) {
            // 过滤掉没有文档的执行轨迹项
            JSONObject docItem = getFirstValidDocument(traceLog);
            if (docItem == null) {
                continue;
            }

            //1.将id从long类型转换为string类型
            castLong2String(traceLog.getDocs());
            castLong2String(traceLog.getMockDocs());

            //2.设置得分个位次字段
            setScoringRankFields(traceLog);

            //3.重排环节属性解析
            if (AI_RERANK.equals(traceLog.getCode()) && metaLabelDTOMap != null) {
                TraceLogUtils.fillReRankProps(traceLog, metaLabelDTOMap);
            }
        }
    }

    /**
     * 将id从long类型转换为string类型
     *
     * @param docs 召回文档列表
     */
    public static List<JSONObject> castLong2String(List<JSONObject> docs) {

        if (CollectionUtils.isEmpty(docs)) {
            return new ArrayList<>();
        }

        for (JSONObject doc : docs) {
            if (doc.get("id") instanceof Long) {
                doc.put("id", String.valueOf(doc.get("id")));
            }
        }
        return docs;
    }

    /**
     * 将全链路日志转换为TraceRecordDTO列表
     *
     * @param traceLogs 全链路日志列表
     * @return 转换后的TraceRecordDTO列表
     */
    public static List<TraceRecordDTO> convertTraceLog2TraceRecordDTO(List<TraceLogItem> traceLogs) {
        List<TraceRecordDTO> traceRecordDTOList = new ArrayList<>();
        for (TraceLogItem traceLogItem : traceLogs) {
            TraceRecordDTO traceRecord = new TraceRecordDTO();
            traceRecord.setName(traceLogItem.getName());
            traceRecord.setNodeId(traceLogItem.getNodeId());
            traceRecord.setIndexField(traceLogItem.getIndexField());
            traceRecord.setScoreField(traceLogItem.getScoreField());
            traceRecord.setDocs(TraceDocRecordDTO.of(traceLogItem.getDocs()));
            traceRecord.setMockDocs(TraceDocRecordDTO.of(traceLogItem.getMockDocs()));
            traceRecordDTOList.add(traceRecord);
        }
        return traceRecordDTOList;
    }

    /**
     * 填充后处理属性解析字段 添加的字段key : eval_props
     *
     * @param traceLogItem 全链路中某个节点
     */
    private static void fillReRankProps(TraceLogItem traceLogItem, Map<String, MetaLabelDTO> metaLabelDTOMap) {

        buildEvalReRankProps(traceLogItem.getDocs(), metaLabelDTOMap);

        buildEvalReRankProps(traceLogItem.getMockDocs(), metaLabelDTOMap);
    }

    public static void buildEvalReRankProps(List<JSONObject> docs, Map<String, MetaLabelDTO> metaLabelDTOMap) {
        if (CollectionUtils.isEmpty(docs)) {
            return;
        }

        for (JSONObject doc : docs) {
            ReRankProps reRankProps = ReRankProps.of(doc, metaLabelDTOMap);
            doc.put("_eval_props", JSONObject.from(reRankProps));
        }
    }

    /**
     * 获取第一个有效的文档
     *
     * @param traceLog 执行轨迹项
     * @return 文档对象, 如果没有则返回null
     */
    private static JSONObject getFirstValidDocument(TraceLogItem traceLog) {
        if (!CollectionUtils.isEmpty(traceLog.getDocs())) {
            return traceLog.getDocs().getFirst();
        }
        if (!CollectionUtils.isEmpty(traceLog.getMockDocs())) {
            return traceLog.getMockDocs().getFirst();
        }
        return null;
    }

    /**
     * 设置执行轨迹的得分与位次字段
     *
     * @param traceLog 执行轨迹项
     */
    private static void setScoringRankFields(TraceLogItem traceLog) {
        WorkflowUtil.findByCodeWithScoreAndIndex(traceLog.getCode()).ifPresent(concernNodeDTO -> {
            traceLog.setIndexField(concernNodeDTO.getIndexField());
            traceLog.setScoreField(concernNodeDTO.getScoreField());
        });
    }
}
