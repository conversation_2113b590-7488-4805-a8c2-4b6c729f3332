package com.iflytek.lynxiao.eval.dto.mark.record;

import com.iflytek.lynxiao.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.eval.domain.mark.ProcessParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

@Getter
@Setter
public class MarkRecordCreateDTO extends Jsonable {

    @NotBlank(message = "query不能为空")
    @Schema(title = "query")
    private String query;

    /**
     * 测评任务id，可选
     */
    @Schema(title = "测评任务id")
    private String missionId;

    /**
     * 流程参数  通过processId与流程对应
     * <p>
     * 体验模式： 包含流程id、环境编码与流程参数
     * 非体验模式： 通过strategyId与标注目录策略配置中的Id对应
     */
    @Schema(title = "流程列表")
    private List<ProcessParam> processList;

    public int getMode() {
        // 1:体验模式 2:单问题模式  3:测评任务模式
        if (StringUtils.isNotBlank(missionId)) {
            return MarkMode.MISSION;
        }
        // 体验模式
        return MarkMode.EXPERIENCE;
    }
}
