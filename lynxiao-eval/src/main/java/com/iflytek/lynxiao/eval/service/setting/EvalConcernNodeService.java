package com.iflytek.lynxiao.eval.service.setting;

import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 关注节点配置表管理
 */
public interface EvalConcernNodeService {

    /**
     * 创建或更新
     *
     * @param dto dto
     */
    void save(EvalConcernNodeSaveDTO dto);

    /**
     * 分页查询
     *
     * @param search   搜索条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<EvalConcernNodeDTO> findByPage(String search, Pageable pageable);

    /**
     * 查询列表
     *
     * @return 列表
     */
    List<EvalConcernNodeDTO> list();

    /**
     * 查询详情
     *
     * @param id id
     * @return 详情
     */
    EvalConcernNodeDTO findById(String id);

    /**
     * 删除
     *
     * @param id id
     */
    void deleteById(String id);

    /**
     * 排序
     * @param ids ids
     */
    void sort(List<String> ids);

}
