package com.iflytek.lynxiao.eval.autogen.generated.controller;

import com.iflytek.lynxiao.eval.autogen.service.EvalFieldConfigService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigPatchDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 测评字段配置表管理
 */
public class GeneratedEvalFieldConfigController {

    @Resource
    protected EvalFieldConfigService evalFieldConfigService;
}