//package com.iflytek.lynxiao.eval.component.domain;
//
//import com.iflytek.lynxiao.portal.document.dto.site.SiteDTO;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.experimental.Accessors;
//import skynet.boot.pandora.api.Jsonable;
//
//import java.util.ArrayList;
//import java.util.List;
//
//@Getter
//@Setter
//@Accessors(chain = true)
//public class IdxWithSiteDTO extends Jsonable {
//
//    /**
//     * 索引库id
//     */
//    private String id;
//
//    /**
//     * 索引库编码
//     */
//    private String code;
//
//    /**
//     * 0 爬虫数据集 、1 离线数据集
//     */
//    private Integer type;
//
//    /**
//     * 区域id
//     */
//    private String idRegion;
//
//    private String region;
//
//
//    private String datasetId;
//
//    /**
//     * 数据集版本id
//     */
//    private String datasetVersionId;
//
//    /**
//     * 站点版本id列表
//     */
//    private List<Long> siteVersionIds = new ArrayList<>();
//
//    /**
//     * 站点id列表
//     */
//    private List<SiteDTO> siteIds = new ArrayList<>();
//}