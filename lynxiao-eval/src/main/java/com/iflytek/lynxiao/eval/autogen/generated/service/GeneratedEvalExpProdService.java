package com.iflytek.lynxiao.eval.autogen.generated.service;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalExpProd;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalExpProdPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedEvalExpProdService extends TemplateService<GeneratedEvalExpProd, EvalExpProdDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<EvalExpProdDTO> findAllByCriteria(EvalExpProdCriteria criteria, Pageable pageable);
    Page<EvalExpProdDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<EvalExpProdDTO> findAllByCriteria(EvalExpProdCriteria criteria, Sort sort);
    List<EvalExpProdDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    EvalExpProdDTO findById(Long id);

    /**
     * 创建
     * @param evalExpProdDTO
     * @return
     */
    EvalExpProdDTO save(EvalExpProdDTO evalExpProdDTO);

    /**
     * 修改
     * @param evalExpProdDTO
     */
    EvalExpProdDTO update(EvalExpProdDTO evalExpProdDTO);

    /**
     * 更新
     * @param evalExpProdPatchDTO
     */
    EvalExpProdDTO patch(EvalExpProdPatchDTO evalExpProdPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    EvalExpProdDTO copy(Long id);
}