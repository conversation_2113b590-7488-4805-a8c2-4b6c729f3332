package com.iflytek.lynxiao.eval.dto.mission;

import com.iflytek.lynxiao.data.dto.AuditingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EvalQueryGroupDTO extends AuditingDTO<String> {

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 分组
     */
    @Schema(title = "分组")
    private String label;

    /**
     * query条数
     */
    @Schema(title = "query条数")
    private Integer size;

    /**
     * 状态
     */
    @Schema(title = "状态")
    private Integer status;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    /**
     * 是否为离线数据
     */
    @Schema(title = "是否为离线数据")
    private Boolean offline;
}
