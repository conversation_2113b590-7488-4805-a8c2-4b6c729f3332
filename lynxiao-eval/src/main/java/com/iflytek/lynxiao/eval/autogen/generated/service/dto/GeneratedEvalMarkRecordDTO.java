package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedEvalMarkRecordDTO extends AbstractAuditingEntity<Long> {

    /**
     * 测评的query
     */
    @Schema(title = "测评的query")
    private String query;

    /**
     * 用户account
     */
    @Schema(title = "用户account")
    private String account;

    /**
     * 测评模式，1:体验模式 2:单问题模式  3:测评任务模式
     */
    @Schema(title = "测评模式，1:体验模式 2:单问题模式  3:测评任务模式")
    private Integer mode;

    /**
     * 测评任务id
     */
    @Schema(title = "测评任务id")
    private Long missionId;

    /**
     * 组名
     */
    @Schema(title = "组名")
    private String userGroup;

    /**
     * 是否完成自动归因
     */
    @Schema(title = "是否完成自动归因")
    private Boolean ascribed;

    /**
     * 自动归因是否成功
     */
    @Schema(title = "自动归因是否成功")
    private Boolean ascribeSuccess;

    /**
     * 是否标注完成
     */
    @Schema(title = "是否标注完成")
    private Boolean finish;

    /**
     * query类弃标
     */
    @Schema(title = "query类弃标")
    private String queryIgnore;
}