package com.iflytek.lynxiao.eval.repository;


import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.mongodb.client.result.DeleteResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class)
public class MarkTargetRepository {

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;

    public void save(MarkTargetEntity entity) {
        platformMongoTemplate.save(entity);
    }

    public void saveAll(List<MarkTargetEntity> entities) {
        platformMongoTemplate.insertAll(entities);
    }


    /**
     * 查找某一次搜索的标注对象
     *
     * @param markRecordId 标注记录id
     * @return 标注对象列表
     */
    public List<MarkTargetEntity> findByRecordId(String markRecordId) {
        assert markRecordId != null : "markRecordId is null";
        Query query = Query.query(Criteria.where("mark_record_id").is(markRecordId));
        return platformMongoTemplate.find(query, MarkTargetEntity.class);
    }



    /**
     * 查找某一次搜索的标注对象 但不包含data字段
     *
     * @param markRecordId 标注记录id
     * @return 标注对象列表
     */
    public List<MarkTargetEntity> findByRecordIdWithOutData(String markRecordId) {
        assert markRecordId != null : "markRecordId is null";
        Query query = Query.query(Criteria.where("mark_record_id").is(markRecordId));
        query.fields().exclude("data");
        return platformMongoTemplate.find(query, MarkTargetEntity.class);
    }

    /**
     * 查找标注对象 但不包含data字段
     *
     * @param markRecordIds 标注记录ids
     * @return 标注对象列表
     */
    public List<MarkTargetEntity> findAllByRecordIdsWithOutData(List<String> markRecordIds) {
        if (markRecordIds == null || markRecordIds.isEmpty()) {
            return List.of();
        }
        Query query = Query.query(Criteria.where("mark_record_id").in(markRecordIds));
        query.fields().exclude("data");
        return platformMongoTemplate.find(query, MarkTargetEntity.class);
    }


    public void deleteByRecordId(String markRecordId) {
        assert markRecordId != null : "markRecordId is null";
        DeleteResult result = platformMongoTemplate.remove(Query.query(Criteria.where("mark_record_id").is(markRecordId)), MarkTargetEntity.class);
        log.info("delete mark target markRecordId:{}, count:{}", markRecordId, result.getDeletedCount());
    }

    public MarkTargetEntity findById(String targetId) {
        assert targetId != null : "targetId is null";
        return platformMongoTemplate.findById(targetId, MarkTargetEntity.class);
    }

    /**
     * 根据missionId 和 markRecordIds 查找所有已完成的测评对象
     *
     * @param missionId     测评任务id
     * @param markRecordIds 已完成的测评记录id列表
     */
    public List<MarkTargetEntity> findAllFinishedByMissionIdAndMarkRecordIds(String missionId, List<String> markRecordIds) {
        assert markRecordIds != null : "markRecordIds is null";
        Query query = Query.query(Criteria.where("mark_record_id").in(markRecordIds));
        if (missionId != null) {
            query.addCriteria(Criteria.where("mission_id").is(missionId));
        }
        return platformMongoTemplate.find(query, MarkTargetEntity.class);
    }
}
