package com.iflytek.lynxiao.eval.domain.mark.result;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注结果-最终召回文档
 */
@Setter
@Getter
public class MarkResultRecall extends Jsonable {

    /**
     * 召回文档在召回结果列表中的位次
     */
    private int docIdx;

    /**
     * 是否弃标
     */
    private boolean ignore = false;

    /**
     * 文档标注结果 包含正常标注与弃标标注
     */
    private List<MarkResultDims> dimsList;

    /**
     * 文档标注备注
     */
    private String remark;

    /**
     * 归因分析标注
     */
    private MarkResultRecallAscribe ascribe;

}
