package com.iflytek.lynxiao.eval.dto.search;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 搜索结果分页请求
 */
@Getter
@Setter
public class SearchApiResultQueryDTO extends Jsonable {

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String traceId;

    /**
     * appId集合
     */
    private List<String> appIdList;

    /**
     * 业务应用类型集合
     */
    private List<String> appTypeList;

    /**
     * 策略名称
     */
    private String flowName;

    /**
     * query
     */
    private String query;

    /**
     * query 来源
     */
    private String fromType;

    /**
     * topK
     */
    private String topK;

    /**
     * 场景标签
     */
    private String scene;

    /**
     * 搜索意图
     */
    private List<String> intent;

    /**
     * 结果数量
     */
    private String docCount;

    /**
     * 插件分类
     */
    private String plugin;

    /**
     * 时间过滤字段
     */
    private String filterFiledName;

    /**
     * 时间过滤开始时间
     */
    private String filterStartTime;

    /**
     * 时间过滤结束时间
     */
    private String filterEndTime;

    /**
     * 过滤域
     */
    private String filterDomain;

    private int page = 1;

    private int size = 20;

    @NotBlank(message = "区域不能为空")
    private String region;

    /**
     * 产品方案code
     */
    private List<String> prodCode;

    /**
     * 方案版本id
     */
    private List<String> pvidList;

    /**
     * 医疗业务唯一标识  医疗定制
     */
    private String qid;

    /**
     * 原始查询语句  医疗定制
     */
    private String rawQuery;

}
