package com.iflytek.lynxiao.eval.dto.mission;

import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;
import java.util.Map;

/**
 * 测评任务创建
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class EvalMissionCreateDTO extends Jsonable {

    @NotBlank(message = "名称不能为空")
    @Schema(title = "名称")
    private String name;

    @Schema(title = "描述")
    private String description;

    @NotNull(message = "测评方式不能为空")
    @Schema(title = "在离线 （1：在线  2：离线）")
    private Integer type;

    @NotNull(message = "数据分配不能为空")
    @Schema(title = "数据分配  key:组的序号  value:用户名称集合")
    private Map<String, List<String>> dataAssign;

    @NotNull(message = "query集不能为空")
    @Schema(title = "query集id")
    private String queryGroupId;

    @NotBlank(message = "活动开始时间不能为空")
    @Schema(title = "活动开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @NotBlank(message = "活动结束时间不能为空")
    @Schema(title = "活动结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @NotBlank(message = "所属目录不能为空")
    @Schema(title = "所属目录")
    private String catalogCode;

    @NotBlank(message = "测评标准id不能为空")
    @Schema(title = "测评标准")
    private String standardId;

    @Schema(title = "测评字段配置")
    private List<String> extendFields;

    @NotEmpty(message = "策略配置不能为空")
    @Schema(title = "策略配置")
    private List<StrategyConfig> strategyConfig;
}
