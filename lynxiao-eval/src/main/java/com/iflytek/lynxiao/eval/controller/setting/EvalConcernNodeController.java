package com.iflytek.lynxiao.eval.controller.setting;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeSaveDTO;
import com.iflytek.lynxiao.eval.dto.standard.EvalConcernNodeSortDTO;
import com.iflytek.lynxiao.eval.service.setting.EvalConcernNodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 关注节点配置表管理
 */
@Tag(name = "关注节点配置表管理")
@RestController
@RequestMapping("/eval/api/v1/concern-node")
@EnableSkynetSwagger2
public class EvalConcernNodeController {

    private final EvalConcernNodeService service;

    public EvalConcernNodeController(EvalConcernNodeService service) {
        this.service = service;
    }

    @Operation(summary = "创建或更新")
    @PostMapping("")
    public ApiResponse save(@Validated @RequestBody EvalConcernNodeSaveDTO dto) {
        service.save(dto);
        return new ApiResponse();
    }

    @Operation(summary = "查询列表")
    @GetMapping("/findByPage")
    public ApiResponse findByPage(@RequestParam(value = "search", required = false) String search, Pageable pageable) {
        return new ApiResponse(JSONObject.from(service.findByPage(search, pageable)));
    }

    @Operation(summary = "查询列表")
    @GetMapping("/list")
    public ApiResponse list() {
        return new ApiResponse(JSONObject.of("data", service.list()));
    }

    @Operation(summary = "查询详情")
    @GetMapping("/{id}")
    public ApiResponse findById(@PathVariable String id) {
        return new ApiResponse(JSONObject.from(service.findById(id)));
    }

    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ApiResponse delete(@PathVariable String id) {
        service.deleteById(id);
        return new ApiResponse();
    }

    @Operation(summary = "排序")
    @PostMapping("/sort")
    public ApiResponse sort(@RequestBody EvalConcernNodeSortDTO dto) {
        service.sort(dto.getIds());
        return new ApiResponse();
    }
}
