package com.iflytek.lynxiao.eval.service.standard.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalDimensionGroup;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionGroupDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionGroupDetailDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionGroupSaveDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionModuleDTO;
import com.iflytek.lynxiao.eval.repository.EvalDimensionGroupRepository;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionGroupService;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionModuleService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.iflytek.lynxiao.eval.utils.CatalogTreeUtils.getAllChildrenCode;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MarkDimensionGroupServiceImpl implements MarkDimensionGroupService {

    public static final String DIMS_MODULE_CODE = "WDBZ";
    private final EvalDimensionGroupRepository dimensionGroupRepository;
    private final MarkDimensionModuleService markDimensionModuleService;

    public MarkDimensionGroupServiceImpl(EvalDimensionGroupRepository dimensionGroupRepository, MarkDimensionModuleService markDimensionModuleService) {
        this.dimensionGroupRepository = dimensionGroupRepository;
        this.markDimensionModuleService = markDimensionModuleService;
    }

    @Override
    public void save(MarkDimensionGroupSaveDTO dto) {
        log.info("保存标注维度集: {}", dto);
        if (StringUtils.isBlank(dto.getId())) {
            // 新建维度集
            GeneratedEvalDimensionGroup generatedEvalDimensionGroup = BeanUtil.copyProperties(dto, GeneratedEvalDimensionGroup.class);
            AuditingEntityUtil.fillCreateValue(generatedEvalDimensionGroup);
            generatedEvalDimensionGroup.setDimsTree(JSONArray.toJSONString(dto.getDimsTree()));
            generatedEvalDimensionGroup.setEnabled(false);
            generatedEvalDimensionGroup.setDeleted(false);
            dimensionGroupRepository.save(generatedEvalDimensionGroup);
        } else {
            //更新维度集基本信息
            GeneratedEvalDimensionGroup generatedEvalDimensionGroup = dimensionGroupRepository.findById(Long.valueOf(dto.getId())).orElseThrow(() -> new LynxiaoException("维度集不存在: " + dto.getId()));
            BeanUtil.copyProperties(dto, generatedEvalDimensionGroup);
            String dimsTreeString = JSONArray.toJSONString(dto.getDimsTree());
            generatedEvalDimensionGroup.setDimsTree(dimsTreeString);
            AuditingEntityUtil.fillUpdateValue(generatedEvalDimensionGroup);
            dimensionGroupRepository.save(generatedEvalDimensionGroup);
        }
    }

    @Override
    public void copy(String id) {
        log.info("复制标注维度集: {}", id);
        GeneratedEvalDimensionGroup originalGroup = this.dimensionGroupRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("维度集不存在: " + id));
        GeneratedEvalDimensionGroup newGroup = BeanUtil.copyProperties(originalGroup, GeneratedEvalDimensionGroup.class);

        newGroup.setId(null); // 清除ID以创建新记录
        newGroup.setName(originalGroup.getName() + "-copy" + RandomStringUtils.secureStrong().nextAlphanumeric(4));
        List<MarkDimensionGroupDetailDTO> dimsTree = JSONArray.parseArray(originalGroup.getDimsTree(), MarkDimensionGroupDetailDTO.class);
        dimsTree.forEach(this::updateChildrenRecursively);
        newGroup.setDeleted(false);
        newGroup.setEnabled(false);
        newGroup.setDimsTree(JSONArray.toJSONString(dimsTree));
        AuditingEntityUtil.fillCreateValue(newGroup);
        dimensionGroupRepository.save(newGroup);
    }

    @Override
    public Page<MarkDimensionGroupDTO> findByPage(String code, String search, Pageable pageable) {
        log.debug("分页查询标注维度集 参数:{}", search);
        if (StringUtils.isBlank(code)) {
            throw new LynxiaoException("文件");
        }

        //获取当前code所有子目录的code
        MarkDimensionModuleDTO markDimensionModuleDTO = this.markDimensionModuleService.find(DIMS_MODULE_CODE);
        List<String> allChildrenCode = getAllChildrenCode(code, markDimensionModuleDTO.getCatalogTree());

        Page<GeneratedEvalDimensionGroup> pageResult = StringUtils.isNotBlank(search) ?
                dimensionGroupRepository.findByNameContainingAndDeletedFalse(allChildrenCode, search, pageable) :
                dimensionGroupRepository.findByDeletedFalse(allChildrenCode, pageable);

        return pageResult.map(this::convert2DTO);
    }

    @Override
    public MarkDimensionGroupDTO findById(String id) {
        log.debug("查询标注维度集: {}", id);
        GeneratedEvalDimensionGroup generatedEvalDimensionGroup = this.dimensionGroupRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("维度集不存在: " + id));
        return convert2DTO(generatedEvalDimensionGroup);
    }

    /**
     * 根据目录code删除所有挂载在当前目录以及子目录下的维度集
     *
     * @param code 待删除的目录code
     */
    @Override
    public void deleteByCode(String code) {
        log.info("删除目录下的所有维度集: {}", code);
        MarkDimensionModuleDTO markDimensionModuleDTO = this.markDimensionModuleService.find(DIMS_MODULE_CODE);
        List<String> allChildrenCode = getAllChildrenCode(code, markDimensionModuleDTO.getCatalogTree());
        this.dimensionGroupRepository.deleteByCodeList(allChildrenCode);
    }

    @Override
    public void deleteById(String id) {
        log.info("删除标注维度集: {}", id);
        this.dimensionGroupRepository.findById(Long.valueOf(id))
                .ifPresentOrElse(
                        generatedEvalDimensionGroup -> {
                            generatedEvalDimensionGroup.setDeleted(true);
                            AuditingEntityUtil.fillUpdateValue(generatedEvalDimensionGroup);
                            this.dimensionGroupRepository.save(generatedEvalDimensionGroup);
                        },
                        () -> {
                            throw new LynxiaoException("维度集不存在: " + id);
                        }
                );
    }

    @Override
    public void enabled(String id, boolean enabled) {
        log.info("禁用或启用标注维度集: {}, enabled: {}", id, enabled);
        GeneratedEvalDimensionGroup generatedEvalDimensionGroup = this.dimensionGroupRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("维度集不存在: " + id));
        generatedEvalDimensionGroup.setEnabled(enabled);
        AuditingEntityUtil.fillUpdateValue(generatedEvalDimensionGroup);
        this.dimensionGroupRepository.save(generatedEvalDimensionGroup);
    }

    @Override
    public List<MarkDimensionGroupDTO> list() {
        log.debug("查询标注维度集列表");
        List<GeneratedEvalDimensionGroup> all = this.dimensionGroupRepository.findAllDeletedFalseEnabledTrue();
        return all.stream().map(this::convert2DTO).toList();
    }

    @NotNull
    private MarkDimensionGroupDTO convert2DTO(GeneratedEvalDimensionGroup entity) {
        MarkDimensionGroupDTO markDimensionGroupDTO = BeanUtil.copyProperties(entity, MarkDimensionGroupDTO.class, "id", "dimsTree");
        markDimensionGroupDTO.setId(String.valueOf(entity.getId()));
        markDimensionGroupDTO.setCatalogName(markDimensionModuleService.getCatalogName(entity.getCatalogCode(), DIMS_MODULE_CODE));
        markDimensionGroupDTO.setDimsTree(JSONArray.parseArray(entity.getDimsTree(), MarkDimensionGroupDetailDTO.class));
        return markDimensionGroupDTO;
    }

    /**
     * 递归更新维度集详情的子节点的code 和默认值
     */
    private void updateChildrenRecursively(MarkDimensionGroupDetailDTO groupDetailDTO) {
        groupDetailDTO.setCode(RandomStringUtils.secureStrong().nextAlphanumeric(8));
        groupDetailDTO.setDefaultValue(null);
        if (groupDetailDTO.getChildren() != null) {
            groupDetailDTO.getChildren().forEach(this::updateChildrenRecursively);
        }
    }
}
