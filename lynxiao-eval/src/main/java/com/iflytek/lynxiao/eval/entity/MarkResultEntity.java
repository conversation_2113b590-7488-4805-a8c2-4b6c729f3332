package com.iflytek.lynxiao.eval.entity;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultData;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import skynet.boot.mongo.domain.AbstractAuditingEntity;


/**
 * 给测评对象进行标注的结果，包含最终召回文档、全链路、以及大模型推理结果的标注。
 * 注意：这里的每一条标注结果记录是以用户点击标注点为维度。
 * - 比如用户对某个文档点击了标注，则会产生一条记录
 * - 用户点击了大模型推理结果的标注，产生一条记录
 * - 用户点击了全链路中某个文档的赞或踩，产生一条记录
 */
@Getter
@Setter
@Document("eval_mark_result")
public class MarkResultEntity extends AbstractAuditingEntity<String> {

    /**
     * 标注记录id
     */
    @Field("mark_record_id")
    private String markRecordId;

    /**
     * sceneProcessId
     */
    @Field("scene_process_id")
    private String sceneProcessId;

    /**
     * 测评对象id
     */
    @Field("mark_target_id")
    private String markTargetId;

    /**
     * 全链路id
     */
    @Field("trace_id")
    private String traceId;

    /**
     * 标注目录策略配置id, 可选
     */
    @Field("strategy_id")
    private String strategyId;

    /**
     * 多query标注任务id
     */
    @Field("mission_id")
    private String missionId;

    /**
     * 标注人所属的组
     */
    @Field("user_group")
    private String userGroup;

    /**
     * 标注人account
     */
    @Field("account")
    private String account;

    /**
     * @see com.iflytek.lynxiao.eval.domain.AscribeMode
     */
    @Field("ascribe_mode")
    private int ascribeMode;

    /**
     * url
     */
    @Field("url")
    private String url;

    /**
     * docId
     */
    @Field("doc_id")
    private String docId;

    /**
     * title
     */
    @Field("title")
    private String title;

    /**
     * docIdx
     */
    @Field("doc_idx")
    private Integer docIdx;

    /**
     * regionCode
     */
    @Field("region_code")
    private String regionCode;

    /**
     * query
     */
    @Field("query")
    private String query;

    /**
     * 标注目标类型
     * 0：策略结果（召回的文档）
     * 1：全链路结果
     * 2：大模型结果
     */
    @Field("type")
    private Integer type;

    /**
     * 元数据
     */
    @Field("metadata")
    private JSONObject metadata;

    /**
     * 标注目标
     */
    @Field("data")
    private MarkResultData data;

}
