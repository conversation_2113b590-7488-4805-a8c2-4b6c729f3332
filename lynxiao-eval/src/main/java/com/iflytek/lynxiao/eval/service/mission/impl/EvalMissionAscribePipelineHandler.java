package com.iflytek.lynxiao.eval.service.mission.impl;

import com.iflytek.lynxiao.eval.config.EvalProperties;
import com.iflytek.lynxiao.eval.domain.AscribeStatus;
import com.iflytek.lynxiao.eval.service.mark.MarkRecordService;
import com.iflytek.lynxiao.eval.repository.EvalMarkRecordRepository;
import com.iflytek.lynxiao.eval.repository.EvalMissionRepository;
import jakarta.annotation.PostConstruct;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.AsyncPipelineHandler;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class EvalMissionAscribePipelineHandler extends AsyncPipelineHandler<String, Object> {

    private final EvalProperties evalProperties;

    private final MarkRecordService markRecordService;
    private final EvalMissionRepository evalMissionRepository;
    private final EvalMarkRecordRepository evalMarkRecordRepository;

    public EvalMissionAscribePipelineHandler(EvalProperties evalProperties, MarkRecordService markRecordService, EvalMissionRepository evalMissionRepository, EvalMarkRecordRepository evalMarkRecordRepository) {
        this.evalProperties = evalProperties;
        this.markRecordService = markRecordService;
        this.evalMissionRepository = evalMissionRepository;
        this.evalMarkRecordRepository = evalMarkRecordRepository;
    }

    @SneakyThrows
    @PostConstruct
    public void init() {
        this.init(
                evalProperties.getAscribeSetting().getMissionAscribeLimit(),
                ""
        );
    }

    @Override
    protected void onEvent(String missionId) throws Exception {
        log.info("EvalMissionAscribePipelineHandler start missionId:{}", missionId);
        //1.查出所有测评记录
        List<String> markRecordIds = this.markRecordService.findByMissionId(missionId);

        this.evalMissionRepository.updateAscribeStatus(Long.valueOf(missionId), AscribeStatus.PROCESSING);
        List<String> failedMarkRecordIds = new ArrayList<>();

        //更新开始时间
        this.evalMissionRepository.updateAscribeTimestamps(Long.valueOf(missionId), Instant.now(), null);

        //2.执行自动归因
        for (String markRecordId : markRecordIds) {
            boolean isSuccess = true;
            try {
                this.markRecordService.ascribe(markRecordId);
            } catch (Exception e) {
                log.error("missionId:{} ascribe error, markRecordId:{}", missionId, markRecordId, e);
                failedMarkRecordIds.add(markRecordId);
                isSuccess = false;
            } finally {
                evalMarkRecordRepository.updateAscribe(Long.valueOf(markRecordId), isSuccess);
            }
        }

        int ascribeStatus = CollectionUtils.isEmpty(failedMarkRecordIds) ? AscribeStatus.COMPLETED : AscribeStatus.COMPLETED_WITH_FAILURE;
        this.evalMissionRepository.updateAscribeStatus(Long.valueOf(missionId), ascribeStatus);
        //更新结束时间
        this.evalMissionRepository.updateAscribeTimestamps(Long.valueOf(missionId), null, Instant.now());
    }
}
