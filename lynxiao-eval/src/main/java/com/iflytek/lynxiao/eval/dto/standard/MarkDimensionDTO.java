package com.iflytek.lynxiao.eval.dto.standard;

import cn.hutool.core.bean.BeanUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标注维度创建DTO
 *
 * <AUTHOR>  2025/5/22 13:46
 */
@Getter
@Setter
public class MarkDimensionDTO extends Jsonable {

    /**
     * 维度名称
     */
    private String name;

    /**
     * 维度编码
     */
    private String code;

    /**
     * 维度描述
     */
    private String description;

    /**
     * 选项类型
     *
     * @see com.iflytek.lynxiao.portal.eval.domain.MarkOptionType
     */
    private int optType;

    /**
     * 是否必填
     */
    private boolean required;

    /**
     * 维度选项
     */
    private List<MarkDimOptionDTO> options;

    /**
     * 将维度集转换为给前端的标注维度格式
     *
     * @param dimensionGroupDTO 维度集DTO
     */
    public static List<MarkDimensionDTO> of(MarkDimensionGroupDTO dimensionGroupDTO) {
        if (dimensionGroupDTO == null || CollectionUtils.isEmpty(dimensionGroupDTO.getDimsTree())) {
            return new ArrayList<>();
        }

        return dimensionGroupDTO.getDimsTree().stream()
                .map(MarkDimensionDTO::convertDimension)
                .collect(Collectors.toList());
    }

    private static MarkDimensionDTO convertDimension(MarkDimensionGroupDetailDTO dim) {
        MarkDimensionDTO dimensionDTO = BeanUtil.copyProperties(dim, MarkDimensionDTO.class);

        if (!CollectionUtils.isEmpty(dim.getChildren())) {
            List<MarkDimOptionDTO> options = dim.getChildren().stream()
                    .map(MarkDimensionDTO::convertOption)
                    .collect(Collectors.toList());
            dimensionDTO.setOptions(options);
        }

        return dimensionDTO;
    }

    private static MarkDimOptionDTO convertOption(MarkDimensionGroupDetailDTO option) {
        MarkDimOptionDTO dimOptionDTO = BeanUtil.copyProperties(option, MarkDimOptionDTO.class);

        if (!CollectionUtils.isEmpty(option.getChildren())) {
            List<MarkFeedbackDTO> feedbacks = option.getChildren().stream()
                    .map(MarkDimensionDTO::convertFeedback)
                    .collect(Collectors.toList());
            dimOptionDTO.setFeedbacks(feedbacks);
        }

        return dimOptionDTO;
    }

    private static MarkFeedbackDTO convertFeedback(MarkDimensionGroupDetailDTO feedback) {
        MarkFeedbackDTO markFeedbackDTO = BeanUtil.copyProperties(feedback, MarkFeedbackDTO.class);

        if (!CollectionUtils.isEmpty(feedback.getChildren())) {
            List<MarkFeedbackDetailDTO> feedbackDetails = feedback.getChildren().stream()
                    .map(feedbackDetail -> BeanUtil.copyProperties(feedbackDetail, MarkFeedbackDetailDTO.class))
                    .collect(Collectors.toList());
            markFeedbackDTO.setContent(feedbackDetails);
        }

        return markFeedbackDTO;
    }
}
