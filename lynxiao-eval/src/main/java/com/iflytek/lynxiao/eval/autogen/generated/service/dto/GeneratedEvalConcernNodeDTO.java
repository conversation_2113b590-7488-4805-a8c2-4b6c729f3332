package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedEvalConcernNodeDTO extends AbstractAuditingEntity<Long> {

    /**
     * 节点名称，例如：文本召回
     */
    @Schema(title = "节点名称，例如：文本召回")
    private String name;

    /**
     * 节点编码，例如：recall-textrc
     */
    @Schema(title = "节点编码，例如：recall-textrc")
    private String code;

    /**
     * 排序
     */
    @Schema(title = "排序")
    private Integer sort;

    /**
     * 节点入参，针对doc的入参字段
     */
    @Schema(title = "节点入参，针对doc的入参字段")
    private String inputFiled;

    /**
     * 节点出参，针对doc的出参字段
     */
    @Schema(title = "节点出参，针对doc的出参字段")
    private String outputField;

    /**
     * 得分字段
     */
    @Schema(title = "得分字段")
    private String scoreField;

    /**
     * 位次字段
     */
    @Schema(title = "位次字段")
    private String indexField;

    /**
     * 是否可mock
     */
    @Schema(title = "是否可mock")
    private Boolean canMock;

    /**
     * 是否在标注页面展示
     */
    @Schema(title = "是否在标注页面展示")
    private Boolean showInRecall;

    /**
     * 是否在全链路中展示
     */
    @Schema(title = "是否在全链路中展示")
    private Boolean showInTrace;

    /**
     * 默认值
     */
    @Schema(title = "默认值")
    private String defaultValue;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 是否删除
     */
    @Schema(title = "是否删除")
    private Boolean deleted;
}