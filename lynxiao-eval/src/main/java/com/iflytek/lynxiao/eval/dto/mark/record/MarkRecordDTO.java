package com.iflytek.lynxiao.eval.dto.mark.record;

import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultQueryIgnoreDTO;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMarkRecord;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;


/**
 * 测评记录：单次测评请求生成的全部信息，测评对象 + 标注结果
 */
@Setter
@Getter
public class MarkRecordDTO extends Jsonable {

    /**
     * 测评记录id
     */
    private String id;

    /**
     * 任务id
     */
    private String missionId;

    /**
     * query
     */
    private String query;

    /**
     * 测评的场景列表
     */
    private List<MarkTargetRecordDTO> targets;

    /**
     * query类弃标标注
     */
    private MarkResultQueryIgnoreDTO queryIgnore;

    public static MarkRecordDTO of(String id, GeneratedEvalMarkRecord markRecord, List<MarkTargetRecordDTO> targets, MarkResultQueryIgnoreDTO resultQueryIgnoreDTO) {
        MarkRecordDTO markRecordDTO = new MarkRecordDTO();
        markRecordDTO.setId(id);
        markRecordDTO.setMissionId(String.valueOf(markRecord.getMissionId()));
        markRecordDTO.setQuery(markRecord.getQuery());
        markRecordDTO.setTargets(targets);
        markRecordDTO.setQueryIgnore(resultQueryIgnoreDTO);

        return markRecordDTO;
    }
}
