package com.iflytek.lynxiao.eval.repository;


import com.iflytek.lynxiao.eval.entity.EvalOfflineDocEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class)
public class EvalOfflineDocRepository {

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;


    public void saveAll(List<EvalOfflineDocEntity> offlineDocEntities) {
        for (EvalOfflineDocEntity offlineDocEntity : offlineDocEntities) {
            platformMongoTemplate.save(offlineDocEntity);
        }
    }

    public EvalOfflineDocEntity findByQueryId(String queryId) {
        return platformMongoTemplate.findOne(Query.query(Criteria.where("query_id").is(queryId)), EvalOfflineDocEntity.class);
    }
}
