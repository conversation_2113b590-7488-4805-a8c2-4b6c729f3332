package com.iflytek.lynxiao.eval.autogen.service.impl;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalConcernNode;
import com.iflytek.lynxiao.eval.autogen.generated.service.impl.GeneratedEvalConcernNodeServiceImpl;
import com.iflytek.lynxiao.eval.autogen.service.EvalConcernNodeService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import skynet.boot.common.mapper.EntityMapper;

@Deprecated
public class EvalConcernNodeServiceImpl extends GeneratedEvalConcernNodeServiceImpl implements EvalConcernNodeService {

    public EvalConcernNodeServiceImpl(EntityMapper<EvalConcernNodeDTO, GeneratedEvalConcernNode> entityMapper, JpaSpecificationExecutor<GeneratedEvalConcernNode> jpaSpecificationExecutor, JpaRepository<GeneratedEvalConcernNode, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }
}