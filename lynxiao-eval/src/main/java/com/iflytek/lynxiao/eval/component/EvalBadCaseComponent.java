package com.iflytek.lynxiao.eval.component;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.feign.content.ContentApi;
import com.iflytek.lynxiao.eval.component.core.BadCaseContext;
import com.iflytek.lynxiao.eval.component.core.ProcTraceFetcher;
import com.iflytek.lynxiao.eval.component.core.impl.ProcTraceFetcherFactory;
import com.iflytek.lynxiao.eval.component.domain.AscribeInputBadCase;
import com.iflytek.lynxiao.eval.component.domain.AscribeOutputBadCase;
import com.iflytek.lynxiao.eval.component.domain.ProcTraceFetcherParam;
import com.iflytek.lynxiao.eval.component.domain.ProcTraceFetcherResult;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.FuncServiceHandler;
import skynet.boot.pandora.annotation.SkynetPandoraFuncHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.util.stream.Collectors;


@Slf4j
@SkynetPandoraFuncHandler
public class EvalBadCaseComponent implements FuncServiceHandler {
    private final WorkflowProcessService workflowProcessService;
    private final ProcTraceFetcherFactory procTraceFetcherFactory;
    private final ContentApi urlNormalizer;

    public EvalBadCaseComponent(WorkflowProcessService workflowProcessService, ProcTraceFetcherFactory procTraceFetcherFactory,
                                ContentApi urlNormalizer) {
        this.workflowProcessService = workflowProcessService;
        this.procTraceFetcherFactory = procTraceFetcherFactory;
        this.urlNormalizer = urlNormalizer;
    }

    @Override
    public ApiResponse process(ApiRequest apiRequest) throws PandoraException {
        log.debug("bad-case eval start, apiRequest:{}", apiRequest);

        AscribeInputBadCase ascribeInputBadCase = apiRequest.getPayload().to(AscribeInputBadCase.class);
        checkInput(ascribeInputBadCase);
        ascribeInputBadCase.set_traceId("eval_ascribe_bad" + UUID.fastUUID().toString().substring(0, 8));
        log.info("badcase traceId:{}", ascribeInputBadCase.get_traceId());

        // url规范化
        ascribeInputBadCase.setBadUrl(this.urlNormalizer.normalizeUrl(ascribeInputBadCase.getBadUrl()));
        String goodUrls = ascribeInputBadCase.getGoodUrlList().stream().map(this.urlNormalizer::normalizeUrl).collect(Collectors.joining(","));
        ascribeInputBadCase.setGoodUrl(goodUrls);

        ProcTraceFetcher procTraceFetcher = procTraceFetcherFactory.buildFetcher(ascribeInputBadCase.getProcessId(), ascribeInputBadCase.getTraceId());
        ProcTraceFetcherParam fetcherParam = ProcTraceFetcherParam.of(ascribeInputBadCase, apiRequest);
        String processId = procTraceFetcher.fetchWorkflowProcessId(fetcherParam);

        WorkflowProcess workflowProcess = workflowProcessService.getWorkflowProcess(processId, false);
        if (workflowProcess == null) {
            throw new PandoraException("流程不存在");
        }
        fetcherParam.setProcessId(processId);
        fetcherParam.setWorkflowProcess(workflowProcess);

        ProcTraceFetcherResult fetcherResult = procTraceFetcher.process(fetcherParam);

        BadCaseContext badCaseContext = new BadCaseContext(ascribeInputBadCase);
        badCaseContext.setTraceLogs(fetcherResult.getTraceLogItems());
        AscribeOutputBadCase ascribeOutputBadCase = badCaseContext.computeOutput();
        return new ApiResponse(JSONObject.from(ascribeOutputBadCase));
    }

    private void checkInput(AscribeInputBadCase ascribeInputBadCase) {
        Assert.hasText(ascribeInputBadCase.getQuery(), "query can not be empty");
        Assert.isTrue(StringUtils.isNotBlank(ascribeInputBadCase.getBadId())
                || StringUtils.isNotBlank(ascribeInputBadCase.getBadUrl()), "badId and badUrl can not be empty at the same time");
        Assert.isTrue(StringUtils.isNotBlank(ascribeInputBadCase.getGoodId())
                || StringUtils.isNotBlank(ascribeInputBadCase.getGoodUrl()), "goodId and goodUrl can not be empty at the same time");

        Assert.isTrue(StringUtils.isNotBlank(ascribeInputBadCase.getProcessId())
                || StringUtils.isNotBlank(ascribeInputBadCase.getTraceId()), "processId and traceId can not be empty at the same time");
    }


}
