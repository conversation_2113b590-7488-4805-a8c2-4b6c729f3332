package com.iflytek.lynxiao.eval.autogen.generated.service;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalConcernNode;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodePatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedEvalConcernNodeService extends TemplateService<GeneratedEvalConcernNode, EvalConcernNodeDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<EvalConcernNodeDTO> findAllByCriteria(EvalConcernNodeCriteria criteria, Pageable pageable);
    Page<EvalConcernNodeDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<EvalConcernNodeDTO> findAllByCriteria(EvalConcernNodeCriteria criteria, Sort sort);
    List<EvalConcernNodeDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    EvalConcernNodeDTO findById(Long id);

    /**
     * 创建
     * @param evalConcernNodeDTO
     * @return
     */
    EvalConcernNodeDTO save(EvalConcernNodeDTO evalConcernNodeDTO);

    /**
     * 修改
     * @param evalConcernNodeDTO
     */
    EvalConcernNodeDTO update(EvalConcernNodeDTO evalConcernNodeDTO);

    /**
     * 更新
     * @param evalConcernNodePatchDTO
     */
    EvalConcernNodeDTO patch(EvalConcernNodePatchDTO evalConcernNodePatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    EvalConcernNodeDTO copy(Long id);
}