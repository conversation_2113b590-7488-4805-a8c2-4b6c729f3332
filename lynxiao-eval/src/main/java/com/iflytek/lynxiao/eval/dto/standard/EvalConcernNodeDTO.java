package com.iflytek.lynxiao.eval.dto.standard;

import com.iflytek.lynxiao.data.dto.AuditingDTO;
import lombok.Getter;
import lombok.Setter;

/**
 * 关注节点配置表DTO
 */
@Getter
@Setter
public class EvalConcernNodeDTO extends AuditingDTO<String> {

    /**
     * 节点名称，例如：文本召回
     */
    private String name;

    /**
     * 节点编码，例如：recall-textrc
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 节点入参，针对doc的入参字段
     */
    private String inputFiled;

    /**
     * 节点出参，针对doc的出参字段
     */
    private String outputField;

    /**
     * 得分字段
     */
    private String scoreField;

    /**
     * 位次字段
     */
    private String indexField;

    /**
     * 是否允许mock
     */
    private boolean canMock;

    /**
     * 是否在标注页面展示
     */
    private Boolean showInRecall;

    /**
     * 是否在全链路中展示
     */
    private Boolean showInTrace;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除
     */
    private Boolean deleted;
}
