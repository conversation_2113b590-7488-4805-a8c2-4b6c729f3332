package com.iflytek.lynxiao.eval.utils;

import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultDims;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class MarkComplateCheckUtils {

    /**
     * 判断标注是否完成
     *
     * @param markResultDimsList MarkResultDims列表
     * @return true表示标注完成，false表示标注未完成
     */
    public static boolean isMarkingCompleted(List<MarkResultDims> markResultDimsList) {
        //为空时直接返回true，表示没有标注内容
        if (CollectionUtils.isEmpty(markResultDimsList)) {
            return true;
        }

        // 递归检查所有节点
        return checkAllRequiredNodes(markResultDimsList);
    }

    /**
     * 递归检查所有必填节点是否都已标注
     *
     * @param dimsList 维度列表
     * @return true表示所有必填节点都已标注，false表示存在未标注的必填节点
     */
    private static boolean checkAllRequiredNodes(List<MarkResultDims> dimsList) {
        if (CollectionUtils.isEmpty(dimsList)) {
            return true;
        }

        for (MarkResultDims dims : dimsList) {
            // 检查当前节点
            if (Boolean.TRUE.equals(dims.getRequired()) && !dims.isValue()) {
                // 如果当前节点必填且value为false，则标注未完成
                return false;
            }

            // 递归检查子节点
            if (!CollectionUtils.isEmpty(dims.getChildren())) {
                if (!checkAllRequiredNodes(dims.getChildren())) {
                    return false;
                }
            }
        }

        return true;
    }

}
