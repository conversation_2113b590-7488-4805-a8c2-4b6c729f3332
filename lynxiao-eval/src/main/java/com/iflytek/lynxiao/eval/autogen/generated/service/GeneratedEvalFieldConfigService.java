package com.iflytek.lynxiao.eval.autogen.generated.service;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigPatchDTO;
import skynet.boot.common.service.TemplateService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface GeneratedEvalFieldConfigService extends TemplateService<GeneratedEvalFieldConfig, EvalFieldConfigDTO, Long> {

    /**
     * 查找列表-分页
     * @param pageable
     * @return
     */
    Page<EvalFieldConfigDTO> findAllByCriteria(EvalFieldConfigCriteria criteria, Pageable pageable);
    Page<EvalFieldConfigDTO> findAll(Pageable pageable);

    /**
     * 查找列表-不分页
     * @return
     */
    List<EvalFieldConfigDTO> findAllByCriteria(EvalFieldConfigCriteria criteria, Sort sort);
    List<EvalFieldConfigDTO> findAll(Sort sort);

    /**
     * 查找单条
     * @param id
     * @return
     */
    EvalFieldConfigDTO findById(Long id);

    /**
     * 创建
     * @param evalFieldConfigDTO
     * @return
     */
    EvalFieldConfigDTO save(EvalFieldConfigDTO evalFieldConfigDTO);

    /**
     * 修改
     * @param evalFieldConfigDTO
     */
    EvalFieldConfigDTO update(EvalFieldConfigDTO evalFieldConfigDTO);

    /**
     * 更新
     * @param evalFieldConfigPatchDTO
     */
    EvalFieldConfigDTO patch(EvalFieldConfigPatchDTO evalFieldConfigPatchDTO);

    /**
     * 删除单条
     * @param id
     */
    void delete(Long id);

    /**
    * 复制
    * @param id
    */
    EvalFieldConfigDTO copy(Long id);
}