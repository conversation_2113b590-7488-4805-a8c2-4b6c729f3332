package com.iflytek.lynxiao.eval.service.setting;

import com.iflytek.lynxiao.eval.dto.standard.EvalExpProdDTO;
import com.iflytek.lynxiao.eval.dto.standard.EvalExpProdSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 效果体验配置表管理
 */
public interface EvalExpProdService {

    /**
     * 创建或更新
     *
     * @param dto dto
     */
    void save(EvalExpProdSaveDTO dto);

    /**
     * 分页查询
     *
     * @param search   搜索条件
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<EvalExpProdDTO> findByPage(String search, Pageable pageable);

    /**
     * 查询列表
     *
     * @return 列表
     */
    List<EvalExpProdDTO> list();

    /**
     * 查询详情
     *
     * @param id id
     * @return 详情
     */
    EvalExpProdDTO findById(String id);

    /**
     * 删除
     *
     * @param id id
     */
    void deleteById(String id);

    /**
     * 启、禁用
     *
     * @param id      id
     * @param enabled 是否启用
     */
    void updateEnabled(String id, boolean enabled);

    /**
     * 排序
     * @param ids ids
     */
    void sort(List<String> ids);
}
