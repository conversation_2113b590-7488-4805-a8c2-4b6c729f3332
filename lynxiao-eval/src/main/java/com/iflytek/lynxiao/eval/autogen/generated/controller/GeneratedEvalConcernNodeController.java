package com.iflytek.lynxiao.eval.autogen.generated.controller;

import com.iflytek.lynxiao.eval.autogen.service.EvalConcernNodeService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodePatchDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 关注节点配置表管理
 */
public class GeneratedEvalConcernNodeController {

    @Resource
    protected EvalConcernNodeService evalConcernNodeService;
}