package com.iflytek.lynxiao.eval.domain;

import lombok.Getter;

/**
 * <AUTHOR>
 */

public enum MarkResultSearchTypeEnum {
    ONE_GOOD_DOC("oneGoodDoc", "含≥1条goodUrl结果的query"),
    TOPK_GOOD_QUERY("topKGoodQuery", "TopK完全good的query数"),
    HAS_RESULT_QUERY("hasResultQuery", "有结果query数");

    @Getter
    private final String code;

    private final String describe;

    MarkResultSearchTypeEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
