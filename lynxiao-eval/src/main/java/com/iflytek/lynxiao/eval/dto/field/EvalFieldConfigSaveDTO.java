package com.iflytek.lynxiao.eval.dto.field;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 测评字段配置保存DTO
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class EvalFieldConfigSaveDTO extends Jsonable {

    /**
     * 有id编辑，无id创建
     */
    @Schema(title = "ID")
    private String id;

    /**
     * 字段名称，例如：标题、内容
     */
    @NotBlank(message = "字段名称不能为空")
    @Schema(title = "字段名称")
    private String name;

    /**
     * 字段，例如：title、content
     */
    @NotBlank(message = "字段不能为空")
    @Schema(title = "字段")
    private String field;

    /**
     * 字段类型，String、long等
     */
    @NotBlank(message = "字段类型不能为空")
    @Schema(title = "字段类型")
    private String type;

    /**
     * 字段分类，例如：通用字段、医疗字段等
     */
    @NotBlank(message = "字段分类不能为空")
    @Schema(title = "字段分类")
    private String category;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    @Schema(title = "排序")
    private Integer idx;

    /**
     * 字段路径，如果为空，默认从doc下取
     */
    @Schema(title = "字段路径")
    private String path;

    /**
     * 是否在全链路展示
     */
    @Schema(title = "是否在全链路展示")
    private Boolean showInTrace;

    /**
     * 默认值
     */
    @Schema(title = "默认值")
    private String defaultValue;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 样例
     */
    @Schema(title = "样例")
    private String sample;
}
