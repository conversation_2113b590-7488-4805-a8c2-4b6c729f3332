package com.iflytek.lynxiao.eval.dto.mission;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.data.dto.AuditingDTO;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class EvalMissionDTO extends AuditingDTO<String> {

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 分组个数
     */
    @Schema(title = "分组个数")
    private Integer groupNumber;

    /**
     * 状态 0：在线  1：离线
     */
    @Schema(title = "状态 0：在线  1：离线")
    private Integer type;

    /**
     * query集id
     */
    @Schema(title = "query集id")
    private String queryGroupId;

    /**
     * 活动开始时间
     */
    @Schema(title = "活动开始时间")
    private Instant beginTime;

    /**
     * 活动结束时间
     */
    @Schema(title = "活动结束时间")
    private Instant endTime;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 状态
     * @see com.iflytek.lynxiao.data.domain.VersionStatus
     */
    @Schema(title = "状态")
    private Integer status;

    /**
     * 删除标志
     */
    @Schema(title = "删除标志")
    private Boolean deleted;

    @Schema(title = "所属目录")
    private String catalogCode;

    @Schema(title = "测评标准")
    private String standardId;

    private StandardConfig standardConfig;

    @Schema(title = "测评字段配置")
    private List<String> extendFields;

    @Schema(title = "策略配置")
    private List<StrategyConfig> strategyConfig;

    /**
     * 是否标注完成, 判断依据：关联的任务分配表是否全部完成
     */
    private boolean markFinished;

    /**
     * 归因分析状态
     * @see com.iflytek.lynxiao.portal.eval.domain.AscribeStatus
     */
    private int ascribeStatus;

    /**
     * query集条数
     */
    private long queryCount;

    /**
     * 标注人数
     */
    private long userCount;

    /**
     * 标注人员分配情况，
     */
    private Map<String, List<String>> groupUserAssignments;

    /**
     * 我的任务接口使用--分配的条数
     */
    private long assignQueryCount;

    private long completeQueryCount;


    public static EvalMissionDTO of(GeneratedEvalMission evalMission){
        EvalMissionDTO evalMissionDTO = BeanUtil.copyProperties(evalMission, EvalMissionDTO.class, "strategyConfig", "extendFields", "standardConfig");
        evalMissionDTO.setId(String.valueOf(evalMission.getId()));
        evalMissionDTO.setQueryGroupId(String.valueOf(evalMission.getQueryGroupId()));
        evalMissionDTO.setStandardId(String.valueOf(evalMission.getStandardId()));
        evalMissionDTO.setExtendFields(Arrays.stream(evalMission.getExtendFields().split(","))
                .filter(item-> !item.isBlank())
                .collect(Collectors.toList()));
        evalMissionDTO.setStrategyConfig(JSONArray.parseArray(evalMission.getStrategyConfig(), StrategyConfig.class));
        evalMissionDTO.setStandardConfig(
                JSONObject.parseObject(evalMission.getStandardConfig(), StandardConfig.class));
        return evalMissionDTO;
    }


}
