package com.iflytek.lynxiao.eval.dto.search;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

/**
 * <AUTHOR>
 * <p>
 * 搜索结果列表实体
 */
@Data
public class SearchApiResultDTO {

    @ExcelProperty(value = "appId")
    private String appId; // 应用ID

    @ExcelProperty(value = "appId类型")
    private String appType; // 应用类型

    @ExcelProperty(value = "关联产品")
    private String prodCode; // 关联产品

    @ExcelProperty(value = "产品版本")
    private String productVersion; // 产品版本

    @ExcelProperty(value = "策略名称")
    private String flowName; // 策略名称

    @ExcelProperty(value = "query")
    private String query; // query

    @ExcelProperty(value = "query来源")
    private String fromType; // query 来源

    @ExcelProperty(value = "场景标签")
    private String scene; // 场景标签

    @ExcelProperty(value = "搜索意图")
    private String intent; // 搜索意图

    @ExcelProperty(value = "结果数量")
    private String docsCount; // 搜索结果

    @ExcelProperty(value = "traceId")
    private String traceId; // traceId

    @ExcelProperty(value = "插件分类")
    private String plugin;

    @ExcelProperty(value = "topK")
    private String topK;

    @ExcelProperty(value = "qid")
    private String qid;

    @ExcelProperty(value = "rawQuery")
    private String rawQuery;

    @ExcelProperty(value = "时间过滤字段")
    private String filterFiledName;

    @ExcelProperty(value = "时间过滤开始时间")
    private String filterStartTime;

    @ExcelProperty(value = "时间过滤结束时间")
    private String filterEndTime;

    @ExcelProperty(value = "域名")
    private String filterDomain;

    @ExcelProperty(value = "创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private String createTime; // 创建时间
}
