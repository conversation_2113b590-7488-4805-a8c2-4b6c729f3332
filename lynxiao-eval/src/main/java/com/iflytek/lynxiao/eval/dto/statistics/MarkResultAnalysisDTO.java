package com.iflytek.lynxiao.eval.dto.statistics;


import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 标注结果分析报告
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MarkResultAnalysisDTO {

    private String sceneProcessId;

    /**
     * 策略名称
     */
    private String sceneProcessName;

    /**
     * @see com.iflytek.lynxiao.portal.eval.domain.AscribeMode
     */
    private int ascribeMode;

    /**
     * 环境
     */
    private String regionCode;

    /**
     * 环境
     */
    private String regionName;

    /**
     * 标注人
     */
    private String createdBy;

    /**
     * query条数
     */
    private long queryCount;

    /**
     * 最小的标注时间
     */
    private Instant markMinTime;

    /**
     * 最大的标注时间
     */
    private Instant markMaxTime;

    /**
     * 含≥1条good结果的query数
     */
    private long oneGoodDocQuery;

    /**
     * 含≥1条good结果的query的测评记录id集合
     */
    private List<String> oneGoodDocQueryRecordIds;

    /**
     * 有结果吸收率
     */
    private BigDecimal hasResultAbsorbPercentage;

    /**
     * doc数
     */
    private long docCount;

    /**
     * good回答数
     */
    private long goodDocCount;

    /**
     * good率
     */
    private BigDecimal goodDocPercentage;

    /**
     * bad 回答数
     */
    private long badDocCount;

    /**
     * bad率
     */
    private BigDecimal badDocPercentage;

    /**
     * TopK全部是goodUrl的query数
     */
    private long topKGoodQuery;

    /**
     * TopK全部是goodUrl的query的测评记录id集合
     */
    private List<String> topKGoodQueryRecordIds;

    /**
     * topK全部是goodUrl的query数/query条数
     */
    private BigDecimal topKGoodQueryPercentage;


    /**
     * 自定义字段
     */
    private Map<String, Object> extendMap;

    // ===========================以下为多query标注新增统计字段==============================
    /**
     * 已完成的query数
     */
    private long completeQueryCount;

    /**
     * 未完成的query数
     */
    private long noCompleteQueryCount;

    /**
     * 完成率
     */
    private BigDecimal completePercentage;

    /**
     * 分配query数
     */
    private long assignQueryCount;

    /**
     * 分组名  分组1 、 分组2 ...
     */
    private String userGroup;

    /**
     * 标注人   组或人
     */
    private String markUser;

    /**
     * 1:组  2:人
     */
    private Integer userType;

    /**
     * 有结果query数量
     */
    private long hasResultQueryCount;
}


