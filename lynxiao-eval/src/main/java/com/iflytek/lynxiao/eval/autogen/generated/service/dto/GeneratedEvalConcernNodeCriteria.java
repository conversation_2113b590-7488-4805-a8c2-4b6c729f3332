package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedEvalConcernNodeCriteria implements Serializable, Criteria  {

    public GeneratedEvalConcernNodeCriteria() {}

    public GeneratedEvalConcernNodeCriteria(GeneratedEvalConcernNodeCriteria other) {
    }

    @Override
    public GeneratedEvalConcernNodeCriteria copy() {
        return new GeneratedEvalConcernNodeCriteria(this);
    }
}
