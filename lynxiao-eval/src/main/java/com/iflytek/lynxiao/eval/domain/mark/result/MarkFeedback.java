package com.iflytek.lynxiao.eval.domain.mark.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注结果-反馈信息
 */
@Setter
@Getter
public class MarkFeedback extends Jsonable {

    /**
     * 反馈问题
     */
    private String name;

    /**
     * 反馈内容
     */
    @Schema(title = "反馈内容")
    private List<String> content;

    /**
     * 备注
     */
    private String remark;
}
