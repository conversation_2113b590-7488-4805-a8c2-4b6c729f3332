package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import lombok.*;
import skynet.boot.mongo.domain.AbstractAuditingEntity;
import io.swagger.v3.oas.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class GeneratedEvalExpProdDTO extends AbstractAuditingEntity<Long> {

    /**
     * 流程名称
     */
    @Schema(title = "流程名称")
    private String name;

    /**
     * 流程id
     */
    @Schema(title = "流程id")
    private String code;

    /**
     * 排序
     */
    @Schema(title = "排序")
    private Integer sort;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 是否启用
     */
    @Schema(title = "是否启用")
    private Boolean enabled;

    /**
     * 是否删除
     */
    @Schema(title = "是否删除")
    private Boolean deleted;
}