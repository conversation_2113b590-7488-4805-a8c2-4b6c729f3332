package com.iflytek.lynxiao.eval.service.standard.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalDimensionGroup;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalStandard;
import com.iflytek.lynxiao.eval.domain.DocClassifyRule;
import com.iflytek.lynxiao.eval.dto.standard.MarkDimensionModuleDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkStandardDTO;
import com.iflytek.lynxiao.eval.dto.standard.MarkStandardSaveDTO;
import com.iflytek.lynxiao.eval.repository.EvalDimensionGroupRepository;
import com.iflytek.lynxiao.eval.repository.EvalStandardRepository;
import com.iflytek.lynxiao.eval.service.standard.MarkDimensionModuleService;
import com.iflytek.lynxiao.eval.service.standard.MarkStandardService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.iflytek.lynxiao.eval.utils.CatalogTreeUtils.getAllChildrenCode;

/**
 * <AUTHOR>  2025/5/22 14:18
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MarkStandardServiceImpl implements MarkStandardService {

    public static final String STANDARD_MODULE_CODE = "CPBZ";
    private final EvalStandardRepository evalStandardRepository;
    private final EvalDimensionGroupRepository evalDimensionGroupRepository;
    private final MarkDimensionModuleService markDimensionModuleService;

    public MarkStandardServiceImpl(EvalStandardRepository evalStandardRepository, EvalDimensionGroupRepository dimensionGroupRepository,
                                   MarkDimensionModuleService markDimensionModuleService) {
        this.evalStandardRepository = evalStandardRepository;
        this.evalDimensionGroupRepository = dimensionGroupRepository;
        this.markDimensionModuleService = markDimensionModuleService;
    }

    @Override
    public void save(MarkStandardSaveDTO dto) {

        if (StringUtils.isNotEmpty(dto.getId())) {
            // update
            GeneratedEvalStandard evalStandard = evalStandardRepository.findById(Long.valueOf(dto.getId())).orElseThrow(() -> new LynxiaoException("测评标准不存在: " + dto.getId()));
            fillEntityInfo(dto, evalStandard);
            AuditingEntityUtil.fillUpdateValue(evalStandard);
            evalStandardRepository.save(evalStandard);
        } else {
            // Create
            GeneratedEvalStandard evalStandard = new GeneratedEvalStandard();
            evalStandard.setDeleted(false);
            evalStandard.setEnabled(false);
            fillEntityInfo(dto, evalStandard);
            AuditingEntityUtil.fillCreateValue(evalStandard);
            BeanUtil.copyProperties(dto, evalStandard);
            evalStandardRepository.save(evalStandard);
        }
    }

    @Override
    public void copy(String id) {
        log.info("复制标注维度集: {}", id);
        GeneratedEvalStandard originalEvalStandard = this.evalStandardRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("测评标准不存在: " + id));
        GeneratedEvalStandard newEvalStandard = BeanUtil.copyProperties(originalEvalStandard, GeneratedEvalStandard.class);
        newEvalStandard.setId(null); // 清除ID以创建新记录
        newEvalStandard.setName(originalEvalStandard.getName() + "-copy" + RandomStringUtils.secureStrong().nextAlphanumeric(4));
        newEvalStandard.setDeleted(false);
        newEvalStandard.setEnabled(false);
        this.evalStandardRepository.save(newEvalStandard);
    }

    @Override
    public Page<MarkStandardDTO> findByPage(String code, String search, Pageable pageable) {
        log.debug("查询标注标准列表, 搜索条件: {}, 分页参数: {}", search, pageable);

        MarkDimensionModuleDTO markDimensionModuleDTO = this.markDimensionModuleService.find(STANDARD_MODULE_CODE);
        List<String> allChildrenCode = getAllChildrenCode(code, markDimensionModuleDTO.getCatalogTree());

        Page<GeneratedEvalStandard> evalStandardPage = StringUtils.isNotBlank(search) ?
                evalStandardRepository.findByNameContainingAndDeletedFalse(allChildrenCode, search, pageable) :
                evalStandardRepository.findByDeletedFalse(allChildrenCode, pageable);
        return evalStandardPage.map(evalStandard -> {
            MarkStandardDTO markStandardDTO = BeanUtil.copyProperties(evalStandard, MarkStandardDTO.class);
            convert(markStandardDTO, evalStandard);
            return markStandardDTO;
        });
    }

    @Override
    public List<MarkStandardDTO> list(String search) {
        log.debug("查询标注标准列表, 搜索条件: {}", search);

        List<GeneratedEvalStandard> list = StringUtils.isBlank(search) ? this.evalStandardRepository.findAllDeletedFalseEnabledTrue() : this.evalStandardRepository.findBySearch(search) ;
        return list.stream().map( evalStandard -> {
            MarkStandardDTO markStandardDTO = BeanUtil.copyProperties(evalStandard, MarkStandardDTO.class);
            convert(markStandardDTO, evalStandard);
            return markStandardDTO;
        }).toList();
    }

    @Override
    public MarkStandardDTO findById(String id) {
        log.debug("查询标注标准: {}", id);
        GeneratedEvalStandard evalStandard = this.evalStandardRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("测评标准不存在: " + id));
        MarkStandardDTO markStandardDTO = BeanUtil.copyProperties(evalStandard, MarkStandardDTO.class);
        convert(markStandardDTO, evalStandard);
        return markStandardDTO;
    }

    /**
     * 根据目录code删除所有挂载在当前目录以及子目录下的维度集
     *
     * @param code 待删除的目录code
     */
    @Override
    public void deleteByCode(String code) {
        log.info("删除目录下的所有维度集: {}", code);
        MarkDimensionModuleDTO markDimensionModuleDTO = this.markDimensionModuleService.find(STANDARD_MODULE_CODE);
        List<String> allChildrenCode = getAllChildrenCode(code, markDimensionModuleDTO.getCatalogTree());
        this.evalStandardRepository.deleteByCodeList(allChildrenCode);
    }

    @Override
    public void deleteById(String id) {
        log.info("删除标注标准: {}", id);
        GeneratedEvalStandard evalStandard = this.evalStandardRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("测评标准不存在: " + id));
        evalStandard.setDeleted(true);
        AuditingEntityUtil.fillUpdateValue(evalStandard);
        this.evalStandardRepository.save(evalStandard);
    }

    @Override
    public void enabled(String id, boolean enabled) {
        log.info("禁用或启用标注标准: {}, enabled: {}", id, enabled);
        GeneratedEvalStandard evalStandard = this.evalStandardRepository.findById(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("测评标准不存在: " + id));
        evalStandard.setEnabled(enabled);
        AuditingEntityUtil.fillUpdateValue(evalStandard);
        this.evalStandardRepository.save(evalStandard);
    }


    private void fillEntityInfo(MarkStandardSaveDTO dto, GeneratedEvalStandard evalStandard) {
        evalStandard.setName(dto.getName());
        evalStandard.setCatalogCode(dto.getCatalogCode());
        evalStandard.setDescription(dto.getDescription());
        evalStandard.setRecall(dto.getRecallDimsGroupId() != null ? Long.valueOf(dto.getRecallDimsGroupId()) : null);
        evalStandard.setChat(dto.getChatDimsGroupId() != null ? Long.valueOf(dto.getChatDimsGroupId()) : null);
        evalStandard.setIgnoreDoc(dto.getIgnoreDocDimsGroupId() != null ? Long.valueOf(dto.getIgnoreDocDimsGroupId()) : null);
        evalStandard.setIgnoreQuery(dto.getIgnoreQueryDimsGroupId() != null ? Long.valueOf(dto.getIgnoreQueryDimsGroupId()) : null);
        evalStandard.setClassifyRule(JSONArray.toJSONString(dto.getClassifyRules()));
    }

    private String getGroupNameById(Long id) {
        if (id == null){
            return StringUtils.EMPTY;
        }
        return this.evalDimensionGroupRepository.findById(id)
                .map(GeneratedEvalDimensionGroup::getName)
                .orElse("未知维度集");
    }

    private void convert(MarkStandardDTO markStandardDTO, GeneratedEvalStandard evalStandard) {
        markStandardDTO.setCatalogName(this.markDimensionModuleService.getCatalogName(evalStandard.getCatalogCode(), STANDARD_MODULE_CODE));
        markStandardDTO.setRecallDimsGroupId(evalStandard.getRecall() != null ? String.valueOf(evalStandard.getRecall()) : null);
        markStandardDTO.setRecallDimsGroupName(this.getGroupNameById(evalStandard.getRecall()));
        markStandardDTO.setChatDimsGroupId(evalStandard.getChat() != null ? String.valueOf(evalStandard.getChat()) : null);
        markStandardDTO.setChatDimsGroupName(this.getGroupNameById(evalStandard.getChat()));
        markStandardDTO.setIgnoreDocDimsGroupId(evalStandard.getIgnoreDoc() != null ? String.valueOf(evalStandard.getIgnoreDoc()) : null);
        markStandardDTO.setIgnoreDocDimsGroupName(this.getGroupNameById(evalStandard.getIgnoreDoc()));
        markStandardDTO.setIgnoreQueryDimsGroupId(evalStandard.getIgnoreQuery() != null ? String.valueOf(evalStandard.getIgnoreQuery()) : null);
        markStandardDTO.setIgnoreQueryDimsGroupName(this.getGroupNameById(evalStandard.getIgnoreQuery()));

        List<DocClassifyRule> classifyRules = JSONArray.parseArray(evalStandard.getClassifyRule(), DocClassifyRule.class);
        markStandardDTO.setClassifyRules(classifyRules);
    }
}
