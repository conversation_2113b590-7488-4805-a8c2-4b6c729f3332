package com.iflytek.lynxiao.eval.service.setting;

import com.iflytek.lynxiao.eval.dto.field.EvalFieldConfigDTO;
import com.iflytek.lynxiao.eval.dto.field.EvalFieldConfigSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 测评字段配置服务接口
 *
 * <AUTHOR>
 */
public interface EvalFieldConfigService {

    /**
     * 创建或更新字段配置
     *
     * @param dto 字段配置保存DTO
     */
    void save(EvalFieldConfigSaveDTO dto);

    /**
     * 分页查询字段配置
     *
     * @param search 字段名称（可选）
     * @return 分页结果
     */
    Page<EvalFieldConfigDTO> findByPage(String search, Pageable pageable);

    /**
     * 查询所有字段配置
     *
     * @param category 字段分类（可选）
     * @return 字段配置列表
     */
    List<EvalFieldConfigDTO> findAll(String category);

    /**
     * 根据ID查询字段配置详情
     *
     * @param id 字段配置ID
     * @return 字段配置详情
     */
    EvalFieldConfigDTO findById(String id);

    /**
     * 删除字段配置
     *
     * @param id 字段配置ID
     */
    void deleteById(String id);

    /**
     * 根据字段名查询字段配置
     *
     * @param fieldName 字段名
     * @return 字段配置
     */
    EvalFieldConfigDTO findByFieldName(String fieldName);

    /**
     * 查询所有字段分类
     *
     * @return 分类列表
     */
    List<String> findAllCategories();

    /**
     * 根据分类查询字段配置
     *
     * @param category 字段分类
     * @return 字段配置列表
     */
    List<EvalFieldConfigDTO> findByCategory(String category);

    /**
     * 检查字段名是否已存在
     *
     * @param fieldName 字段名
     * @param excludeId 排除的ID（编辑时使用）
     * @return 是否存在
     */
    boolean isFieldNameExists(String fieldName, String excludeId);
}
