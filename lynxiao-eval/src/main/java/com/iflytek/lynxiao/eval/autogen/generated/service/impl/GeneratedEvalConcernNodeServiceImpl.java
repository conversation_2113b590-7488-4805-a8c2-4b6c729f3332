package com.iflytek.lynxiao.eval.autogen.generated.service.impl;

import com.iflytek.lynxiao.eval.autogen.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.eval.autogen.repository.EvalConcernNodeRepository;
import com.iflytek.lynxiao.eval.autogen.generated.service.GeneratedEvalConcernNodeService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodeDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalConcernNodePatchDTO;
import com.iflytek.lynxiao.eval.autogen.generated.service.mapper.GeneratedEvalConcernNodeMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedEvalConcernNodeServiceImpl extends MysqlTemplateServiceImpl<GeneratedEvalConcernNode, EvalConcernNodeDTO, Long> implements GeneratedEvalConcernNodeService {

    @Resource
    protected EvalConcernNodeRepository evalConcernNodeRepository;

    @Resource
    protected GeneratedEvalConcernNodeMapper evalConcernNodeMapper;


    public GeneratedEvalConcernNodeServiceImpl(EntityMapper<EvalConcernNodeDTO, GeneratedEvalConcernNode> entityMapper, JpaSpecificationExecutor<GeneratedEvalConcernNode> jpaSpecificationExecutor, JpaRepository<GeneratedEvalConcernNode, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<EvalConcernNodeDTO> findAllByCriteria(EvalConcernNodeCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<EvalConcernNodeDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<EvalConcernNodeDTO> findAllByCriteria(EvalConcernNodeCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<EvalConcernNodeDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedEvalConcernNode> createSpecification(Criteria criteria) {
        EvalConcernNodeCriteria evalConcernNodeCriteria = (EvalConcernNodeCriteria) criteria;
        Specification<GeneratedEvalConcernNode> specification = Specification.where(null);
        if (evalConcernNodeCriteria != null) {
        }
        return specification;
    }

    @Override
    public EvalConcernNodeDTO findById(Long id) {
        EvalConcernNodeDTO evalConcernNodeDTO = super.findById(id);
        return evalConcernNodeDTO;
    }

    @Override
    public EvalConcernNodeDTO save(EvalConcernNodeDTO evalConcernNodeDTO) {
        return super.save(evalConcernNodeDTO);
    }

    @Override
    public EvalConcernNodeDTO update(EvalConcernNodeDTO evalConcernNodeDTO) {

        return super.update(evalConcernNodeDTO.getId(),evalConcernNodeDTO);
    }

    @Override
    public EvalConcernNodeDTO patch(EvalConcernNodePatchDTO evalConcernNodePatchDTO) {
        return super.patch(evalConcernNodePatchDTO.getId(), evalConcernNodePatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public EvalConcernNodeDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedEvalConcernNode.class);
    }
}