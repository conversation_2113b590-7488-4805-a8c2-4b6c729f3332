package com.iflytek.lynxiao.eval.autogen.generated.service.impl;

import com.iflytek.lynxiao.eval.autogen.generated.domain.*; // for static metamodels
import com.iflytek.lynxiao.eval.autogen.repository.EvalFieldConfigRepository;
import com.iflytek.lynxiao.eval.autogen.generated.service.GeneratedEvalFieldConfigService;
import skynet.boot.exception.DuplicateNameException;
import skynet.boot.common.mapper.EntityMapper;
import skynet.boot.common.service.Criteria;
import skynet.boot.mysql.service.MysqlTemplateServiceImpl;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigCriteria;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigDTO;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigPatchDTO;
import com.iflytek.lynxiao.eval.autogen.generated.service.mapper.GeneratedEvalFieldConfigMapper;

import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class GeneratedEvalFieldConfigServiceImpl extends MysqlTemplateServiceImpl<GeneratedEvalFieldConfig, EvalFieldConfigDTO, Long> implements GeneratedEvalFieldConfigService {

    @Resource
    protected EvalFieldConfigRepository evalFieldConfigRepository;

    @Resource
    protected GeneratedEvalFieldConfigMapper evalFieldConfigMapper;


    public GeneratedEvalFieldConfigServiceImpl(EntityMapper<EvalFieldConfigDTO, GeneratedEvalFieldConfig> entityMapper, JpaSpecificationExecutor<GeneratedEvalFieldConfig> jpaSpecificationExecutor, JpaRepository<GeneratedEvalFieldConfig, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }

    @Override
    public Page<EvalFieldConfigDTO> findAllByCriteria(EvalFieldConfigCriteria criteria, Pageable pageable) {
        return super.findAllByCriteria(criteria, null, pageable);
    }

    @Override
    public Page<EvalFieldConfigDTO> findAll(Pageable pageable) {
        return super.findAllByCriteria(null, null, pageable);
    }

    @Override
    public List<EvalFieldConfigDTO> findAllByCriteria(EvalFieldConfigCriteria criteria, Sort sort) {
        return super.findAllByCriteria(criteria, null, sort);
    }

    @Override
    public List<EvalFieldConfigDTO> findAll(Sort sort) {
        return super.findAllByCriteria(null, null, sort);
    }

    @Override
    public Specification<GeneratedEvalFieldConfig> createSpecification(Criteria criteria) {
        EvalFieldConfigCriteria evalFieldConfigCriteria = (EvalFieldConfigCriteria) criteria;
        Specification<GeneratedEvalFieldConfig> specification = Specification.where(null);
        if (evalFieldConfigCriteria != null) {
        }
        return specification;
    }

    @Override
    public EvalFieldConfigDTO findById(Long id) {
        EvalFieldConfigDTO evalFieldConfigDTO = super.findById(id);
        return evalFieldConfigDTO;
    }

    @Override
    public EvalFieldConfigDTO save(EvalFieldConfigDTO evalFieldConfigDTO) {
        return super.save(evalFieldConfigDTO);
    }

    @Override
    public EvalFieldConfigDTO update(EvalFieldConfigDTO evalFieldConfigDTO) {

        return super.update(evalFieldConfigDTO.getId(),evalFieldConfigDTO);
    }

    @Override
    public EvalFieldConfigDTO patch(EvalFieldConfigPatchDTO evalFieldConfigPatchDTO) {
        return super.patch(evalFieldConfigPatchDTO.getId(), evalFieldConfigPatchDTO);
    }

    @Override
    public void delete(Long id) {
        super.delete(id);
    }


    @Override
    public EvalFieldConfigDTO copy(Long id) {
        List<String> renameFields = new ArrayList<>();
        return super.copy(id, renameFields, GeneratedEvalFieldConfig.class);
    }
}