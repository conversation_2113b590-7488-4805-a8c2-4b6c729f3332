package com.iflytek.lynxiao.eval.autogen.service.impl;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import com.iflytek.lynxiao.eval.autogen.generated.service.impl.GeneratedEvalFieldConfigServiceImpl;
import com.iflytek.lynxiao.eval.autogen.service.EvalFieldConfigService;
import com.iflytek.lynxiao.eval.autogen.service.dto.EvalFieldConfigDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import skynet.boot.common.mapper.EntityMapper;

@Deprecated
public class EvalFieldConfigServiceImpl extends GeneratedEvalFieldConfigServiceImpl implements EvalFieldConfigService {

    public EvalFieldConfigServiceImpl(EntityMapper<EvalFieldConfigDTO, GeneratedEvalFieldConfig> entityMapper, JpaSpecificationExecutor<GeneratedEvalFieldConfig> jpaSpecificationExecutor, JpaRepository<GeneratedEvalFieldConfig, Long> jpaRepository) {
        super(entityMapper, jpaSpecificationExecutor, jpaRepository);
    }
}