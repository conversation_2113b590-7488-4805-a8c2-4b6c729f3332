package com.iflytek.lynxiao.eval.autogen.generated.repository;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface GeneratedEvalFieldConfigRepository extends JpaRepository<GeneratedEvalFieldConfig, Long>, JpaSpecificationExecutor<GeneratedEvalFieldConfig> {
}