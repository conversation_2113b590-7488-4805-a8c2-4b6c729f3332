package com.iflytek.lynxiao.eval.component.core.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.portal.MetaLabelApi;
import com.iflytek.lynxiao.data.dto.MetaLabelDTO;
import com.iflytek.lynxiao.eval.component.core.ElasticClientFactory;
import com.iflytek.lynxiao.eval.component.core.ProcTraceFetcher;
import com.iflytek.lynxiao.eval.component.domain.*;
import com.iflytek.lynxiao.eval.domain.WorkflowNode;
import com.iflytek.lynxiao.eval.utils.EvalUtils;
import com.iflytek.lynxiao.eval.utils.TraceLogUtils;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.exception.PandoraException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * traceId 全链路获取器
 * 根据traceId在ELK中获取全链路
 */

@Slf4j
@Component
public class ProcTraceFetcher4TraceId implements ProcTraceFetcher {

    private final ElasticClientFactory elasticClientFactory;
    private final MetaLabelApi metaLabelApi;
    private final String PATH = "/lynxiao_flow-*/_search";

    private static final String TRACE_ID_QUERY_TEMPLATE =
            "{\"query\": {\"bool\": {\"must\": [{\"term\": {\"traceId.keyword\": {\"value\": \"%s\"}}}, {\"terms\": {\"spanId.keyword\": [%s]}}], \"should\": [{\"wildcard\": {\"type.keyword\": {\"value\": \"API_RESPONSE*\"}}}, {\"wildcard\": {\"type.keyword\": {\"value\": \"API_REQUEST*\"}}}]}}, \"size\": 1000}";

    private static final String PROCESS_ID_QUERY_TEMPLATE =
            "{\"query\":{\"bool\":{\"must\":[{\"term\":{\"traceId.keyword\":{\"value\":\"%s\"}}},{\"term\":{\"type.keyword\":{\"value\":\"SearchAPI-Request\"}}}]}},\"size\":1000}";


    public ProcTraceFetcher4TraceId(ElasticClientFactory elasticClientFactory, MetaLabelApi metaLabelApi) {
        this.elasticClientFactory = elasticClientFactory;
        this.metaLabelApi = metaLabelApi;
    }

    @Override
    public ProcTraceFetcherResult process(ProcTraceFetcherParam fetcherParam) {
        log.info("trace fetch for traceId start, historyTraceId:{}, region:{}", fetcherParam.getHistoryTraceId(), fetcherParam.getRegionCode());
        long cost = System.currentTimeMillis();
        // 1. 获取流程拓扑
        WorkflowProcess workflowProcess = fetcherParam.getWorkflowProcess();

        // 2.获取关注节点的列表
        List<WorkflowNode> workflowNodes = EvalUtils.buildNeedAnalysisNodes(workflowProcess);

        // 3.从日志数据库中获取节点返回列表
        List<TraceFetchClientResult> apiResponseList = fetch(fetcherParam.getHistoryTraceId(), fetcherParam.getRegionCode(), workflowNodes);

        // 4.解析拼接TraceLogItem 列表
        List<TraceLogItem> traceLogItems = buildTraceLogItems(apiResponseList, workflowNodes);

        // 处理q_level和q_score
        Map<String, MetaLabelDTO> metaLabelDTOMap = metaLabelApi.findAll().stream()
                .collect(Collectors.toMap(MetaLabelDTO::getCode, metaLabelDTO -> metaLabelDTO));

        //5.全链路后处理
        TraceLogUtils.traceLogsProcess(traceLogItems, metaLabelDTOMap);

        log.info("trace fetch for traceId  end, historyTraceId:{}, region:{}, cost:{}", fetcherParam.getHistoryTraceId(), fetcherParam.getRegionCode(), System.currentTimeMillis() - cost);
        return new ProcTraceFetcherResult(traceLogItems, workflowProcess);
    }


    @Override
    public String fetchWorkflowProcessId(ProcTraceFetcherParam fetcherParam) {

        if (StringUtils.isNotBlank(fetcherParam.getProcessId())) {
            return fetcherParam.getProcessId();
        }

        return findProcessIdByTraceId(fetcherParam.getHistoryTraceId(), fetcherParam.getRegionCode());
    }

    /**
     * 根据召回的各节点的 apiResponse 拼接全链路列表
     * <p>
     * 只有日志信息的关注节点才会被构建
     *
     * @param apiResponseList 从日志数据库中获取的节点返回列表
     * @param workflowNodes   关注的工作流节点列表
     * @return 拼接后的全链路列表
     * @throws IllegalArgumentException 如果传入的 apiResponseList 或 workflowNodes 为 null
     */
    private List<TraceLogItem> buildTraceLogItems(List<TraceFetchClientResult> apiResponseList, List<WorkflowNode> workflowNodes) {
        // 参数非空检查
        if (apiResponseList == null || workflowNodes == null) {
            throw new IllegalArgumentException("apiResponseList 和 workflowNodes 不能为 null");
        }

        List<TraceLogItem> traceLogItems = new ArrayList<>();

        // 以 workflowNodes 为基准进行遍历  构造全链路列表
        for (WorkflowNode node : workflowNodes) {
            for (TraceFetchClientResult fetchClientResult : apiResponseList) {
                String nodeId = fetchClientResult.getNodeId();
                if (node.getIds().contains(nodeId)) {
                    // 解析 apiResponse 中的 doc
                    ApiResponse nodeRequest = fetchClientResult.getReqData().to(ApiResponse.class);
                    ApiResponse nodeResponse = fetchClientResult.getData().to(ApiResponse.class);
                    WorkflowInvokeParseResult req = WorkflowUtil.parseDocs(node.getCode(), nodeRequest.getPayload(), "REQUEST");
                    WorkflowInvokeParseResult resp = WorkflowUtil.parseDocs(node.getCode(), nodeResponse.getPayload(), "RESPONSE");

                    TraceLogItem traceLogItem = createTraceLogItem(node, nodeId, resp, req);
                    traceLogItems.add(traceLogItem);
                }
            }
        }
        return traceLogItems;
    }

    private List<TraceFetchClientResult> fetch(String historyTraceId, String region, List<WorkflowNode> workflowNodes) {
        //1.构建es client request
        Request request = new Request("GET", PATH);
        String spanIdsStr = buildSpanIds(workflowNodes);
        String requestJson = String.format(TRACE_ID_QUERY_TEMPLATE, historyTraceId, spanIdsStr);
        request.setJsonEntity(requestJson);

        log.debug("historyTraceId:{}, region:{}, dsl:{}", historyTraceId, region, requestJson);

        //2.获取es client
        RestClient restClient = elasticClientFactory.getRestClient(region);

        //3.执行
        try {
            return exec(request, restClient);
        } catch (Exception e) {
            log.error("从es中获取链路日志失败。historyTraceId:{}，region:{}, msg:{}", historyTraceId, region, e.getMessage(), e);
            throw new LynxiaoException("从ES中获取链路信息失败。");
        }
    }

    private String findProcessIdByTraceId(String traceId, String region) {
        //构建 request
        Request request = new Request("GET", PATH);
        String requestJson = String.format(PROCESS_ID_QUERY_TEMPLATE, traceId);
        request.setJsonEntity(requestJson);

        log.debug("traceId:{}, region:{} dsl:{}", traceId, region, requestJson);

        RestClient restClient = elasticClientFactory.getRestClient(region);

        try {
            List<TraceFetchClientResult> clientResults = exec(request, restClient);
            validateSearchApiResults(clientResults, traceId);

            TraceFetchClientResult searchApiResult = clientResults.getFirst();
            if (log.isTraceEnabled()) {
                log.trace("traceId:{}, region:{}, searchApiResult:{}", traceId, region, searchApiResult);
            }

            ApiRequest searchApiRequest = searchApiResult.getData().to(ApiRequest.class);
            return searchApiRequest.getParameter().getString("id");

        } catch (IOException e) {
            log.error("从ES中获取ProcessId失败。 traceId:{}, region:{}, msg:{}", traceId, region, e.getMessage(), e);
            throw new LynxiaoException("从ES中获取ProcessId失败。");
        }
    }

    /**
     * 验证从ES查询到的SearchAPI-Request日志结果
     * <p>
     * 该方法用于确保查询结果符合预期：
     * 1. 必须有且仅有一条SearchAPI-Request日志记录
     * 2. 如果结果为空或多条记录，则抛出异常
     *
     * @param clientResults 从ES查询到的结果列表
     * @param traceId       当前查询的traceId，用于错误信息
     * @throws PandoraException 当结果为空或多条记录时抛出
     */
    private void validateSearchApiResults(List<TraceFetchClientResult> clientResults, String traceId) {
        // 检查结果是否为空
        if (CollectionUtils.isEmpty(clientResults)) {
            throw new LynxiaoException(String.format("没有找到 type=SearchAPI-Request 日志。traceId:%s", traceId));
        }

        // 检查结果是否唯一
        if (clientResults.size() > 1) {
            throw new LynxiaoException(String.format("type=SearchAPI-Request 日志不唯一。traceId:%s", traceId));
        }
    }

    @NotNull
    private static String buildSpanIds(List<WorkflowNode> workflowNodes) {
        // 提取所有 WorkflowNode 中的 ids 并合并为一个列表
        List<String> allIds = workflowNodes.stream()
                .flatMap(node -> node.getIds().stream())
                .toList();

        // 将 allIds 列表转换为用双引号包裹并用逗号分隔的字符串
        return allIds.stream()
                .map(id -> "\"" + id + "\"")
                .collect(Collectors.joining(","));
    }

    @NotNull
    private List<TraceFetchClientResult> exec(Request request, RestClient restClient) throws IOException {
        long cost = System.currentTimeMillis();
        Response response;
        try {
            response = restClient.performRequest(request);
        } catch (Exception e) {
            log.error("es performRequest error", e);
            throw new PandoraException("es performRequest error");
        }

        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode < HttpStatus.SC_OK || statusCode >= HttpStatus.SC_MULTIPLE_CHOICES) {
            String msg = String.format("Error when request:%s, response:%s ", request, response);
            throw new PandoraException(statusCode, msg);
        }
        String result = EntityUtils.toString(response.getEntity());


        JSONObject resultJson = JSONObject.parseObject(result);

        if (log.isTraceEnabled()) {
            log.trace("es rest client resultJson:{}", resultJson);
        }

        boolean isInvalid = resultJson == null || !resultJson.containsKey("hits") || resultJson.getJSONObject("hits") == null
                || resultJson.getJSONObject("hits").getJSONArray("hits") == null;
        if (isInvalid) {
            log.error("fetch es trace log error\n result:{}", resultJson);
            throw new PandoraException("es trace log");
        }
        JSONArray hitsArray = resultJson.getJSONObject("hits").getJSONArray("hits");

        //获取_source.data 与 _source.spanId
        Map<String, TraceFetchClientResult> results = new LinkedHashMap<>();
        if (hitsArray.isEmpty()) {
            return new ArrayList<>();
        }

        for (int i = 0; i < hitsArray.size(); i++) {
            JSONObject item = hitsArray.getJSONObject(i);
            JSONObject source = item.getJSONObject("_source");
            String nodeId = source.getString("spanId");

            TraceFetchClientResult traceFetchClientResult = results.computeIfAbsent(nodeId, id -> new TraceFetchClientResult().setNodeId(id));
            if (source.getString("type").contains("API_REQUEST")) {
                traceFetchClientResult.setReqData(source.getJSONObject("data"));
            } else {
                traceFetchClientResult.setData(source.getJSONObject("data"));
            }
            results.put(nodeId, traceFetchClientResult);
        }
        log.debug("es rest client exec end, cost:{}", System.currentTimeMillis() - cost);
        return new ArrayList<>(results.values());
    }

    private TraceLogItem createTraceLogItem(WorkflowNode node, String nodeId, WorkflowInvokeParseResult respParseResult, WorkflowInvokeParseResult reqParseResult) {
        return new TraceLogItem()
                .setCode(node.getCode())
                .setNodeId(nodeId)
                .setName(node.getName())
                .setReqData(reqParseResult)
                .setDocs(respParseResult.getDocs())
                .setDocSize(respParseResult.getDocSize());
    }
}
