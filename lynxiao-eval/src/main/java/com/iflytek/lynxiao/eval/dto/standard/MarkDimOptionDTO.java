package com.iflytek.lynxiao.eval.dto.standard;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.List;

/**
 * 标注维度选项
 *
 * <AUTHOR>  2025/5/22 13:55
 */
@Setter
@Getter
public class MarkDimOptionDTO extends Jsonable {

    /**
     * 选项名称：不好
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 选项类型
     * @see com.iflytek.lynxiao.portal.eval.domain.MarkOptionType
     */
    private int optType;

    /**
     * 是否必填
     */
    private boolean required;

    /**
     * 反馈细则列表
     */
    private List<MarkFeedbackDTO> feedbacks;
}
