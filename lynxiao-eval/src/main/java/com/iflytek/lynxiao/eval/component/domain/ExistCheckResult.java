package com.iflytek.lynxiao.eval.component.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ExistCheckResult {

    private List<EvalResultItem> result = new ArrayList<>();

    // 数据是否被爬取
    private boolean crawled = true;

    public ExistCheckResult() {
        result.add(new EvalResultItem("被去重"));
        result.add(new EvalResultItem("被站点规则筛除"));
        result.add(new EvalResultItem("未爬取"));
        result.add(new EvalResultItem("爬取失败"));
        result.add(new EvalResultItem("被黑名单过滤"));
    }


    public void setReason4Disabled() {
        setReason("被黑名单过滤", 1);
    }

    public void setReason4Dup() {
        setReason("被去重", 1);
    }

    public void setReason4Filtered() {
        setReason("被站点规则筛除", 1);
    }

    public void setReason4UnCrawled() {
        this.crawled = false;
        setReason("未爬取", 1);
    }

    public void setReason4CrawledFailed() {
        this.crawled = true;
        setReason("爬取失败", 1);
    }

    private void setReason(String name, int value) {
        if (result != null) {
            result.forEach(item -> {
                if (item.getName().equals(name)) {
                    item.setValue(value);
                }
            });
        }
    }
}