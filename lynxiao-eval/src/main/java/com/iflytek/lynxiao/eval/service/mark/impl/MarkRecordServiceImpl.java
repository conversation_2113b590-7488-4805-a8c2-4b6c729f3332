package com.iflytek.lynxiao.eval.service.mark.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMarkRecord;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalMission;
import com.iflytek.lynxiao.eval.component.EvalBadCaseComponent;
import com.iflytek.lynxiao.eval.component.EvalGoodCaseComponent;
import com.iflytek.lynxiao.eval.component.domain.AscribeInputBadCase;
import com.iflytek.lynxiao.eval.component.domain.AscribeInputGoodCase;
import com.iflytek.lynxiao.eval.component.domain.AscribeOutputBase;
import com.iflytek.lynxiao.eval.component.domain.EvalResultItem;
import com.iflytek.lynxiao.eval.config.EvalProperties;
import com.iflytek.lynxiao.eval.domain.AscribeMode;
import com.iflytek.lynxiao.eval.domain.DocClassifyRule;
import com.iflytek.lynxiao.eval.domain.StrategyConfig;
import com.iflytek.lynxiao.eval.domain.UrlEvalType;
import com.iflytek.lynxiao.eval.domain.mark.MarkMode;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetRecall;
import com.iflytek.lynxiao.eval.domain.mark.result.*;
import com.iflytek.lynxiao.eval.dto.mark.record.*;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultChatDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultQueryIgnoreDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultRecallDTO;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultTraceDTO;
import com.iflytek.lynxiao.eval.dto.mission.EvalMissionDTO;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import com.iflytek.lynxiao.eval.repository.EvalMarkRecordRepository;
import com.iflytek.lynxiao.eval.repository.EvalMissionRepository;
import com.iflytek.lynxiao.eval.repository.MarkResultRepository;
import com.iflytek.lynxiao.eval.repository.MarkTargetRepository;
import com.iflytek.lynxiao.eval.service.mark.MarkRecordService;
import com.iflytek.lynxiao.eval.service.mark.MarkTargetService;
import com.iflytek.lynxiao.eval.service.mission.EvalMissionAssignService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.eval.utils.MarkComplateCheckUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.pandora.FuncServiceHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MarkRecordServiceImpl implements MarkRecordService {

    private final EvalMarkRecordRepository markRecordRepository;
    private final MarkTargetRepository markTargetRepository;
    private final MarkTargetService markTargetService;
    private final MarkResultRepository markResultRepository;
    private final EvalMissionRepository evalMissionRepository;
    private final EvalMissionAssignService missionAssignService;
    private final EvalGoodCaseComponent evalGoodCaseComponent;
    private final EvalBadCaseComponent evalBadCaseComponent;
    private final ExecutorService executorService;


    public MarkRecordServiceImpl(EvalMarkRecordRepository markRecordRepository, MarkTargetRepository markTargetRepository, MarkTargetService markTargetService,
                                 MarkResultRepository markResultRepository, EvalMissionRepository evalMissionRepository, EvalMissionAssignService missionAssignService,
                                 EvalGoodCaseComponent evalGoodCaseComponent, EvalBadCaseComponent evalBadCaseComponent, EvalProperties evalProperties) {
        this.markRecordRepository = markRecordRepository;
        this.markTargetRepository = markTargetRepository;
        this.markTargetService = markTargetService;
        this.markResultRepository = markResultRepository;
        this.evalMissionRepository = evalMissionRepository;
        this.missionAssignService = missionAssignService;
        this.evalGoodCaseComponent = evalGoodCaseComponent;
        this.evalBadCaseComponent = evalBadCaseComponent;
        this.executorService = this.initExecutorService(evalProperties);
    }

    private ExecutorService initExecutorService(EvalProperties evalProperties) {
        return Executors.newFixedThreadPool(evalProperties.getAscribeSetting().getConcurrency(), r -> {
            Thread t = new Thread(r);
            t.setName("auto-ascribe-" + t.threadId());
            return t;
        });
    }

    @Override
    public String create(MarkRecordCreateDTO dto) {
        log.info("create mark record, dto: {}", dto);
        // 参数校验
        checkParams(dto);
        // 1. 清除未完成的标注记录
        deleteUnfinishedRecord(dto.getMissionId());
        // 2. 保存测评记录
        GeneratedEvalMarkRecord markRecord = new GeneratedEvalMarkRecord();
        markRecord.setQuery(dto.getQuery());
        markRecord.setAccount(AuditingEntityUtil.getCurrentAccount());
        markRecord.setMode(dto.getMode());
        markRecord.setUserGroup(this.missionAssignService.getMarkUserGroup(dto.getMissionId(), AuditingEntityUtil.getCurrentAccount()));
        markRecord.setMissionId(StringUtils.isEmpty(dto.getMissionId()) ? null : Long.valueOf(dto.getMissionId()));
        markRecord.setAscribed(false);
        markRecord.setFinish(false);
        AuditingEntityUtil.fillCreateValue(markRecord);
        GeneratedEvalMarkRecord record = this.markRecordRepository.save(markRecord);
        String recordId = String.valueOf(record.getId());
        // 3. 生成标注对象
        this.markTargetService.create(recordId, dto);

        return recordId;
    }

    @Override
    public MarkRecordDTO find(String id, boolean recall, boolean trace, boolean chat) {
        //获取测评记录
        GeneratedEvalMarkRecord markRecord = this.markRecordRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("测评记录不存在"));
        List<MarkTargetEntity> markTargetEntities = this.markTargetRepository.findByRecordId(id);
        List<MarkResultEntity> markResultEntities = this.markResultRepository.findByMarkRecordId(id);

        List<MarkTargetRecordDTO> markTargetRecordDTOs = new ArrayList<>();
        for (MarkTargetEntity markTargetEntity : markTargetEntities) {
            markTargetRecordDTOs.add(MarkTargetRecordDTO.of(markTargetEntity, markResultEntities, recall, trace, chat));
        }
        return MarkRecordDTO.of(id, markRecord, markTargetRecordDTOs, buildQueryIgnore(markRecord));
    }

    @Override
    public MarkRecordDTO findUnfinished(String missionId) {
        log.debug("find unfinished mark record, missionId: {}", missionId);
        Optional<GeneratedEvalMarkRecord> markRecordOp = findUnfinishedRecord(missionId);
        if (markRecordOp.isEmpty()) {
            return null;
        }
        GeneratedEvalMarkRecord generatedEvalMarkRecord = markRecordOp.get();
        return find(String.valueOf(generatedEvalMarkRecord.getId()), true, false, true);
    }

    @Override
    public List<String> findByMissionId(String missionId) {
        log.debug("find mark record by missionId: {}", missionId);
        if (StringUtils.isBlank(missionId)) {
            log.warn("missionId is blank, return empty list");
            return new ArrayList<>();
        }
        List<GeneratedEvalMarkRecord> markRecords = this.markRecordRepository.findByMissionIdAndDeletedFalse(Long.valueOf(missionId));
        return markRecords.stream().map(item -> String.valueOf(item.getId())).toList();
    }

    @Override
    public MarkRecordSaveResultDTO save(MarkRecordSaveDTO dto) {
        log.debug("save mark record, dto: {}", dto);

        GeneratedEvalMarkRecord markRecord = this.markRecordRepository.findById(Long.valueOf(dto.getId())).orElseThrow(() -> new LynxiaoException("测评记录不存在"));

        //保存时做权限验证
        checkMarkAuth(markRecord.getAccount(), dto);

        if (markRecord.getMode() == MarkMode.MISSION && dto.isCompleteCheck()) {
            // 测评任务模式下，校验标注是否完成
            try {
                completeCheck(dto);
            } catch (LynxiaoException e) {
                log.warn("标注结果校验失败: {}", e.getMessage());
                MarkRecordSaveResultDTO resultDTO = new MarkRecordSaveResultDTO();
                resultDTO.setId(dto.getId()).setComplete(false).setMsg(e.getMessage());
                return resultDTO;
            }
        }

        //保存标注结果
        saveMarkResult(dto, markRecord);

        // 校验标注完成后，当前标注记录状态更新为已完成
        updateFinished(dto.isCompleteCheck(), markRecord);

        //仅当dto.isCompleteCheck()为true时，返回的结果才有实际意义
        if (dto.isCompleteCheck()) {
            return new MarkRecordSaveResultDTO().setId(dto.getId())
                    .setComplete(true)
                    .setMsg("标注结果保存成功");
        }

        return new MarkRecordSaveResultDTO();
    }

    @Override
    public void deleteMarkResult(String resultId) {
        log.info("delete mark result, resultId: {}", resultId);
        this.markResultRepository.deleteById(resultId);
    }

    @Override
    public void ascribe(String id) {
        log.info("auto ascribe start. mark record, id: {}", id);

        GeneratedEvalMarkRecord markRecord = this.markRecordRepository.findById(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("测评记录不存在"));
        if (markRecord.getMissionId() == null) {
            log.error("mark record {} not associate with mission, can not ascribe", id);
            throw new LynxiaoException("测评记录未关联测评任务，不能进行归因分析");
        }

        GeneratedEvalMission evalMission = this.evalMissionRepository.findById(markRecord.getMissionId()).orElseThrow(() -> new LynxiaoException("测评任务不存在"));
        EvalMissionDTO missionDTO = EvalMissionDTO.of(evalMission);
        List<MarkTargetEntity> targetEntities = this.markTargetRepository.findByRecordId(id);
        missionDTO.getStrategyConfig().forEach(strategyConfig -> ascribeStrategy(markRecord, strategyConfig, missionDTO, targetEntities));

        markRecord.setAscribed(true);
        this.markRecordRepository.save(markRecord);
    }


    /**
     * 全链路保存时不需要验证权限，其余标注保存需要与当前标注记录账户相同
     */
    private void checkMarkAuth(String markRecordAccount, MarkRecordSaveDTO dto) {
        boolean isSameAccount = AuditingEntityUtil.getCurrentAccount().equals(markRecordAccount);

        if (dto.getQueryIgnore() != null && !isSameAccount) {
            throw new LynxiaoException("没有标注权限");
        }

        if (CollectionUtil.isEmpty(dto.getTargets())) {
            return;
        }

        for (MarkTargetRecordSaveDTO target : dto.getTargets()) {
            boolean hasRecallOrChat = CollectionUtil.isNotEmpty(target.getRecallList()) || target.getChat() != null;
            if (hasRecallOrChat && !isSameAccount) {
                throw new LynxiaoException("没有标注权限");
            }
        }
    }

    private void saveMarkResult(MarkRecordSaveDTO dto, GeneratedEvalMarkRecord markRecord) {

        if (dto.getQueryIgnore() != null) {
            //query 弃标
            saveQueryIgnore(dto.getQueryIgnore(), markRecord);
        } else {
            //删除query类弃标标注结果
            deleteQueryIgnoreMarkResult(dto, markRecord);
            for (MarkTargetRecordSaveDTO targetRecordSaveDTO : dto.getTargets()) {
                MarkTargetEntity markTargetEntity = this.markTargetRepository.findById(targetRecordSaveDTO.getTargetId());
                // 保存标注结果
                saveRecall(targetRecordSaveDTO.getRecallList(), markTargetEntity, markRecord);
                saveTrace(targetRecordSaveDTO.getTraceList(), markTargetEntity, markRecord);
                saveChat(targetRecordSaveDTO.getChat(), markTargetEntity, markRecord);
            }
        }
    }

    /**
     * 对recall 和 chat标注结果保存时 需要先删除 query类弃标标注结果
     *
     * @param dto
     * @param markRecord
     */
    private void deleteQueryIgnoreMarkResult(MarkRecordSaveDTO dto, GeneratedEvalMarkRecord markRecord) {
        for (MarkTargetRecordSaveDTO targetRecordSaveDTO : dto.getTargets()) {
            if (!CollectionUtils.isEmpty(targetRecordSaveDTO.getRecallList()) || targetRecordSaveDTO.getChat() != null) {
                this.markResultRepository.deleteQueryIgnoreByRecordId(String.valueOf(markRecord.getId()));
                return;
            }
        }
    }


    private void checkParams(MarkRecordCreateDTO dto) {
        if (dto.getMode() == MarkMode.EXPERIENCE && CollectionUtils.isEmpty(dto.getProcessList())) {
            throw new LynxiaoException("体验模式时流程列表不能为空");
        }
    }

    /**
     * 从测评记录中获取query类弃标标注
     *
     * @param markRecord 测评记录
     */
    private MarkResultQueryIgnoreDTO buildQueryIgnore(GeneratedEvalMarkRecord markRecord) {
        //从测评记录中获取query类弃标
        MarkResultQueryIgnoreDTO resultQueryIgnoreDTO = null;
        Optional<MarkResultEntity> markResultEntityOp = this.markResultRepository.findByMarkRecordId(String.valueOf(markRecord.getId())).stream()
                .filter(item -> Objects.equals(item.getType(), MarkResultType.QUERY_IGNORE.getCode()))
                .findFirst();

        if (markResultEntityOp.isPresent()) {
            MarkResultEntity queryIgnoreMarkResult = markResultEntityOp.get();
            resultQueryIgnoreDTO = JSONObject.parseObject(JSONObject.toJSONString(queryIgnoreMarkResult.getData().getQueryIgnore()), MarkResultQueryIgnoreDTO.class);
            resultQueryIgnoreDTO.setResultId(queryIgnoreMarkResult.getId());
        }
        return resultQueryIgnoreDTO;
    }

    /**
     * 校验标注是否完成
     * 1.若query类弃标有值，则检查query类弃标标注是否完成
     * 2.若query类弃标无值，则检查recall标注是否完成
     */
    private void completeCheck(MarkRecordSaveDTO dto) {
        if (dto.getQueryIgnore() != null) {
            //query类弃标标注
            if (!MarkComplateCheckUtils.isMarkingCompleted(dto.getQueryIgnore().getIgnore())) {
                throw new LynxiaoException("query类弃标标注未完成，请检查标注结果");
            }
        } else {
            completeCheck4Recall(dto);
        }
    }

    /**
     * 检验recall标注是否完成
     */
    private void completeCheck4Recall(MarkRecordSaveDTO dto) {
        if (CollectionUtils.isEmpty(dto.getTargets())) {
            dto.setTargets(new ArrayList<>());
            return;
        }
        for (MarkTargetRecordSaveDTO target : dto.getTargets()) {
            for (MarkResultRecallDTO resultRecallDTO : target.getRecallList()) {
                if (!MarkComplateCheckUtils.isMarkingCompleted(resultRecallDTO.getDimsList())) {
                    throw new LynxiaoException("recall标注未完成，请检查标注结果");
                }
            }
        }
    }

    /**
     * 保存query类弃标标注
     * 1.删除当前标注记录下的所有标注结果
     * 2.保存queryIgnore到标注记录
     *
     * @param queryIgnore query弃标内容
     * @param markRecord  标注记录
     */
    private void saveQueryIgnore(MarkResultQueryIgnoreDTO queryIgnore, GeneratedEvalMarkRecord markRecord) {
        log.debug("save query ignore, queryIgnore: {}, markRecordId: {}", queryIgnore, markRecord.getId());
        //1.删除所有非query分类标注记录
        this.markResultRepository.deleteByRecordIdWithoutQueryIgnore(String.valueOf(markRecord.getId()));

        //2.保存queryIgnore到标注记录
        List<MarkTargetEntity> targetEntities = this.markTargetRepository.findByRecordIdWithOutData(String.valueOf(markRecord.getId()));
        MarkResultEntity markResultEntity = buildMarkResultEntity(queryIgnore.getResultId(), targetEntities.getFirst(), markRecord);
        markResultEntity.setType(MarkResultType.QUERY_IGNORE.getCode());
        markResultEntity.setSceneProcessId(StringUtils.EMPTY);
        markResultEntity.setStrategyId(StringUtils.EMPTY);
        markResultEntity.setData(MarkResultData.of(queryIgnore));
        this.markResultRepository.save(markResultEntity);
    }

    private void saveTrace(List<MarkResultTraceDTO> traceList, MarkTargetEntity markTargetEntity, GeneratedEvalMarkRecord markRecord) {
        if (CollectionUtils.isEmpty(traceList)) {
            return;
        }

        validMarkResultTraceDTO(traceList, String.valueOf(markRecord.getId()), markTargetEntity.getId());

        List<MarkResultEntity> toSaveList = new ArrayList<>();
        for (MarkResultTraceDTO saveTraceDto : traceList) {
            MarkResultEntity markResultEntity = buildMarkResultEntity(saveTraceDto.getResultId(), markTargetEntity, markRecord);
            markResultEntity.setTitle(saveTraceDto.getTitle());
            markResultEntity.setDocId(saveTraceDto.getDocId());
            markResultEntity.setUrl(saveTraceDto.getUrl());
            markResultEntity.setType(MarkResultType.TRACE.getCode());
            markResultEntity.setData(MarkResultData.of(saveTraceDto));
            toSaveList.add(markResultEntity);
        }
        markResultRepository.saveList(toSaveList);
    }

    private void validMarkResultTraceDTO(List<MarkResultTraceDTO> traceList, String markRecordId, String markTargetId) {
        //1.如果存在dto中存在resultId 但是数据库中不存在的记录，将resultId置为空；
        for (MarkResultTraceDTO traceDTO : traceList) {
            if (StringUtils.isNotEmpty(traceDTO.getResultId())) {
                MarkResultEntity markResultEntity = this.markResultRepository.findById(traceDTO.getResultId());
                if (markResultEntity == null) {
                    log.warn("标注结果不存在，置空resultId. resultId: {}", traceDTO.getResultId());
                    traceDTO.setResultId(null);
                }
            }
        }

        //2.如果当前待保存的标注结果与库中已有的重复，则更新库中的记录 判定相同的条件：docId相同
        List<MarkResultEntity> savedTraceResults = this.markResultRepository.findTraceByMarkRecordIdAndTargetId(markRecordId, markTargetId);
        for (MarkResultEntity traceResult : savedTraceResults) {
            for (MarkResultTraceDTO toSaveTrace : traceList) {
                if (Objects.equals(toSaveTrace.getDocId(), traceResult.getDocId())) {
                    //更新dto中的resultId
                    toSaveTrace.setResultId(traceResult.getId());
                }
            }
        }
    }

    private void saveRecall(List<MarkResultRecallDTO> recallList, MarkTargetEntity markTargetEntity, GeneratedEvalMarkRecord markRecord) {

        // 召回doc为空时保存一条type 为no_result的记录
        saveNoResult(markTargetEntity, markRecord);

        if (CollectionUtils.isEmpty(recallList)) {
            return;
        }
        List<MarkResultEntity> toSaveList = new ArrayList<>();
        for (MarkResultRecallDTO recallDto : recallList) {
            MarkResultEntity markResultEntity = buildMarkResultEntity(recallDto.getResultId(), markTargetEntity, markRecord);
            markResultEntity.setTitle(recallDto.getTitle());
            markResultEntity.setDocId(recallDto.getDocId());
            markResultEntity.setUrl(recallDto.getUrl());
            markResultEntity.setType(MarkResultType.RECALL.getCode());
            markResultEntity.setData(MarkResultData.of(recallDto));
            markResultEntity.setDocIdx(recallDto.getDocIdx());
            toSaveList.add(markResultEntity);
        }
        markResultRepository.saveList(toSaveList);
    }

    private void saveChat(MarkResultChatDTO chatDto, MarkTargetEntity markTargetEntity, GeneratedEvalMarkRecord markRecord) {
        if (chatDto == null || CollectionUtils.isEmpty(chatDto.getFeedbackList())) {
            return;
        }

        //当没有标注结果被选中时，不保存，且清空已有的记录
        if (chatDto.getFeedbackList().stream().noneMatch(MarkResultDims::isValue)) {
            log.debug("chat标注结果未选中，清空已有记录. markTargetId: {}, markRecordId: {}", markTargetEntity.getId(), markRecord.getId());
            deleteMarkResult(chatDto.getResultId());
            return;
        }

        MarkResultEntity markResultEntity = buildMarkResultEntity(chatDto.getResultId(), markTargetEntity, markRecord);
        markResultEntity.setType(MarkResultType.CHAT.getCode());
        markResultEntity.setData(MarkResultData.of(chatDto));
        this.markResultRepository.save(markResultEntity);
    }

    private void updateFinished(boolean completeCheck, GeneratedEvalMarkRecord markRecord) {
        if (completeCheck) {
            log.info("测评记录:{} 标注完成.", markRecord.getId());
            markRecord.setFinish(true);
            this.markRecordRepository.save(markRecord);
            //更新任务分配记录
            missionAssignService.updateCompletedQuery(markRecord.getMissionId(), markRecord.getQuery());
        }
    }

    private MarkResultEntity buildMarkResultEntity(String resultId, MarkTargetEntity markTargetEntity, GeneratedEvalMarkRecord markRecord) {
        // 生成标注结果-共性字段
        MarkResultEntity markResultEntity;
        if (StringUtils.isEmpty(resultId)) {
            //查询
            markResultEntity = new MarkResultEntity();
            markResultEntity.setId(StringUtils.isEmpty(resultId) ? null : resultId);
            markResultEntity.setMarkRecordId(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getMarkRecordId());
            markResultEntity.setMarkTargetId(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getId());
            markResultEntity.setTraceId(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getTraceId());
            markResultEntity.setMetadata(markTargetEntity == null ? new JSONObject() : markTargetEntity.getMetadata());
            markResultEntity.setQuery(markRecord.getQuery());
            markResultEntity.setAccount(AuditingEntityUtil.getCurrentAccount());
            markResultEntity.setMissionId(markRecord.getMissionId() == null ? null : String.valueOf(markRecord.getMissionId()));
            markResultEntity.setStrategyId(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getStrategyId());
            markResultEntity.setSceneProcessId(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getProcessId());
            markResultEntity.setUserGroup(markRecord.getUserGroup());
            markResultEntity.setRegionCode(markTargetEntity == null ? StringUtils.EMPTY : markTargetEntity.getRegionCode());
            AuditingEntityUtil.fillCreateValueMongo(markResultEntity);
        } else {
            markResultEntity = this.markResultRepository.findById(resultId);
            AuditingEntityUtil.fillUpdateValueMongo(markResultEntity);
        }
        return markResultEntity;
    }

    /**
     * 保存没有召回doc的记录
     */
    private void saveNoResult(MarkTargetEntity markTargetEntity, GeneratedEvalMarkRecord markRecord) {
        if (markTargetEntity != null && !markTargetEntity.isRecalled()) {

            //判断是否已经有no_result记录
            MarkResultEntity noResultEntity = this.markResultRepository.findNoResultByMarkRecordIdAndTargetId(String.valueOf(markRecord.getId()), markTargetEntity.getId());
            if (noResultEntity != null) {
                log.debug("no_result记录已存在. markTargetId: {}, markRecordId: {}", markTargetEntity.getId(), markRecord.getId());
                return;
            }

            log.info("保存没有召回doc记录. markTargetId: {}, markRecordId: {}", markTargetEntity.getId(), markRecord.getId());
            MarkResultEntity markResultEntity = buildMarkResultEntity(null, markTargetEntity, markRecord);
            markResultEntity.setType(MarkResultType.NO_RESULT.getCode());
            markResultRepository.save(markResultEntity);
        }
    }

    //########################################### 归因分析 start  #########################################
    private void ascribeStrategy(GeneratedEvalMarkRecord markRecord, StrategyConfig strategy, EvalMissionDTO missionDTO, List<MarkTargetEntity> targetEntities) {
        String strategyId = strategy.getId();
        List<DocClassifyRule> docClassifyRules = missionDTO.getStandardConfig().getClassifyRules();
        try {
            // 获取recall的标注结果
            List<MarkResultEntity> resultEntities = markResultRepository.findRecallByStrategyId(String.valueOf(markRecord.getId()), strategyId);

            //获取流程调用参数
            JSONObject payload = new JSONObject();
            Optional<MarkTargetEntity> targetEntityOp = targetEntities.stream().filter(markTargetEntity -> markTargetEntity.getStrategyId().equals(strategy.getId())).findFirst();
            if (targetEntityOp.isPresent()) {
                MarkTargetEntity markTargetEntity = targetEntityOp.get();
                if (markTargetEntity.getProcessParam() != null) {
                    payload = markTargetEntity.getProcessParam();
                }
            }
            payload.put("query", markRecord.getQuery());

            if (CollectionUtils.isEmpty(resultEntities)) {
                return;
            }

            // 根据标注结果 判定good和bad文档
            List<MarkResultEntity> good = new ArrayList<>();
            List<MarkResultEntity> bad = new ArrayList<>();
            for (MarkResultEntity entity : resultEntities) {
                MarkResultRecall recall = entity.getData().getRecall();
                if (recall == null) {
                    continue;
                }
                UrlEvalType urlEvalType = DocClassifyRule.classifyBaseRule(recall.getDimsList(), docClassifyRules);
                if (UrlEvalType.GOOD_DOC == urlEvalType) {
                    good.add(entity);
                } else if (UrlEvalType.BAD_DOC == urlEvalType) {
                    bad.add(entity);
                }
            }

            List<Future<DocAscribeResult>> futures = new ArrayList<>();
            if (strategy.getAscribeMode() == AscribeMode.GOOD) {

                //1.判断与当前策略配置竞对的策略中是否召回good doc
                List<MarkResultEntity> needAscribeMarkResultEntities = filterByCompareStrategy(good, missionDTO.getStrategyConfig(), strategy, targetEntities);
                futures = processGoodCase(payload, strategy, needAscribeMarkResultEntities);

            } else if (strategy.getAscribeMode() == AscribeMode.BAD) {
                futures = processBadCase(payload, strategy, good, bad);
            }

            //获取并保存归因结果
            for (Future<DocAscribeResult> future : futures) {
                DocAscribeResult docAscribeResult = future.get();
                if (docAscribeResult == null) {
                    continue;
                }

                if (StringUtils.isNotBlank(docAscribeResult.getErrMsg())) {
                    log.error("归因分析失败，标注结果id:{}, 失败原因:{}", docAscribeResult.resultId, docAscribeResult.getErrMsg());
                }
                MarkResultRecallAscribe ascribe = new MarkResultRecallAscribe(docAscribeResult.getAscribeResult(), docAscribeResult.errMsg);
                this.markResultRepository.saveAscribeResult(findById(docAscribeResult.resultId, resultEntities), ascribe, strategy.getAscribeMode());
            }
        } catch (Exception e) {
            throw new LynxiaoException(e.getMessage());
        }
    }

    private MarkResultEntity findById(String id, List<MarkResultEntity> entities) {
        for (MarkResultEntity entity : entities) {
            if (entity.getId().equals(id)) {
                return entity;
            }
        }
        log.error("归因分析失败，找不到对应的标注结果:{}", id);
        throw new LynxiaoException("归因分析失败，找不到对应的标注结果");
    }

    /**
     * 判断与当前策略配置竞对的策略中是否召回good doc
     *
     * @param goods
     * @param strategyConfigs
     * @param strategy
     */
    private List<MarkResultEntity> filterByCompareStrategy(List<MarkResultEntity> goods, List<StrategyConfig> strategyConfigs, StrategyConfig strategy, List<MarkTargetEntity> targetEntities) {
        //1.确定当前策略的竞对策略
        Optional<StrategyConfig> compareStrategyOp = strategyConfigs.stream().filter(item -> item.getAscribeMode() == AscribeMode.BAD && item.getProcessId().equals(strategy.getId4Ascribe())).findFirst();
        if (compareStrategyOp.isEmpty()) {
            return goods;
        }

        StrategyConfig compareStrategy = compareStrategyOp.get();
        //2.获取竞对策略召回的结果
        Optional<MarkTargetEntity> markTargetEntityOp = targetEntities.stream().filter(item -> item.getStrategyId().equals(compareStrategy.getId())).findFirst();
        if (markTargetEntityOp.isEmpty()) {
            return goods;
        }
        MarkTargetEntity markTargetEntity = markTargetEntityOp.get();

        List<MarkTargetRecall> recallList = markTargetEntity.getData().getRecallList();

        List<MarkResultEntity> resultEntities = new ArrayList<>();
        List<MarkResultEntity> ascribeFreeList = new ArrayList<>();
        for (MarkResultEntity resultEntity : goods) {
            if (isGoodDocRecalled(recallList, resultEntity)) {
                //如果已存在于竞对策略的召回中 该gooddoc无需再自动归因
                log.info("good doc 已存在于竞对策略的召回中，无需再自动归因. goodDocId: {}, goodUrl: {}", resultEntity.getDocId(), resultEntity.getUrl());
                ascribeFreeList.add(resultEntity);
            } else {
                resultEntities.add(resultEntity);
            }
        }

        try {
            for (MarkResultEntity markResultEntity : ascribeFreeList) {
                this.markResultRepository.saveAscribeResult(markResultEntity, new MarkResultRecallAscribe(), AscribeMode.GOOD);
            }
        } catch (Exception e) {
            log.error("保存无需归因的good doc失败", e);
        }
        return resultEntities;
    }

    /**
     * 判断doc是否存在于召回列表中
     * 判断依据： 使用id 和 url进行比对 满足任意条件则判定存在。
     *
     * @param recallList
     * @param goodEntity
     * @return
     */
    private boolean isGoodDocRecalled(List<MarkTargetRecall> recallList, MarkResultEntity goodEntity) {
        if (CollectionUtils.isEmpty(recallList)) {
            return false;
        }
        for (MarkTargetRecall recall : recallList) {
            if (StringUtils.isNotEmpty(goodEntity.getDocId()) && recall.getDoc().get("id") != null && goodEntity.getDocId().equals(String.valueOf(recall.getDoc().get("id")))) {
                return true;
            }
            if (StringUtils.isNotEmpty(goodEntity.getUrl()) && goodEntity.getUrl().equals(recall.getDoc().getString("url"))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理goodcase归因
     *
     * @param payload
     * @param strategy
     * @param good
     * @return
     */
    private List<Future<DocAscribeResult>> processGoodCase(JSONObject payload, StrategyConfig strategy, List<MarkResultEntity> good) {

        List<Future<DocAscribeResult>> futures = new ArrayList<>();
        for (MarkResultEntity entity : good) {

            AscribeInputGoodCase ascribeInputGoodCase = new AscribeInputGoodCase()
                    .setQuery(payload.getString("query"))
                    .setPayload(payload)
                    .setProcessId(strategy.getId4Ascribe())
                    .setGoodUrl(entity.getUrl())
                    .setGoodId(entity.getDocId())
                    .setRegion(strategy.getRegionCode());

            Future<DocAscribeResult> future = execAscribe(JSONObject.from(ascribeInputGoodCase), entity.getId(), evalGoodCaseComponent);
            futures.add(future);
        }
        return futures;
    }

    private List<Future<DocAscribeResult>> processBadCase(JSONObject payload, StrategyConfig strategy, List<MarkResultEntity> good, List<MarkResultEntity> bad) {
        if (CollectionUtils.isEmpty(good)) {
            return new ArrayList<>(0);
        }
        String goodUrls = good.stream().map(MarkResultEntity::getUrl).collect(Collectors.joining(","));
        List<Future<DocAscribeResult>> futures = new ArrayList<>();
        for (MarkResultEntity entity : bad) {
            AscribeInputBadCase ascribeInputBadCase = new AscribeInputBadCase()
                    .setQuery(payload.getString("query"))
                    .setPayload(payload)
                    .setProcessId(strategy.getId4Ascribe())
                    .setBadUrl(entity.getUrl())
                    .setGoodUrl(goodUrls)
                    .setRegion(strategy.getRegionCode());

            Future<DocAscribeResult> future = execAscribe(JSONObject.from(ascribeInputBadCase), entity.getId(), evalBadCaseComponent);
            futures.add(future);
        }
        return futures;
    }

    private Future<DocAscribeResult> execAscribe(JSONObject payload, String resultId, FuncServiceHandler evalComponent) {
        return executorService.submit(() -> {
            DocAscribeResult docAscribeResult = new DocAscribeResult();
            docAscribeResult.setResultId(resultId);
            try {
                ApiRequest apiRequest = new ApiRequest();
                apiRequest.setPayload(payload);
                ApiResponse response = evalComponent.process(apiRequest);
                AscribeOutputBase output = response.getPayload().to(AscribeOutputBase.class);
                List<EvalResultItem> evalResult = output.getEvalResult();
                log.debug("归因参数:{}, 归因分析结果: {}", payload, evalResult);
                docAscribeResult.setAscribeResult(evalResult);
                return docAscribeResult;
            } catch (Exception e) {
                //保存失败原因
                docAscribeResult.setErrMsg(e.getMessage());
                return docAscribeResult;
            }
        });
    }

    //########################################### 归因分析 end  #########################################

    private void deleteUnfinishedRecord(String missionId) {
        // 删除未完成的标注记录（不再继续标注）
        Optional<GeneratedEvalMarkRecord> markRecordOpt = findUnfinishedRecord(missionId);
        if (markRecordOpt.isEmpty()) {
            return;
        }
        log.info("delete unfinished mark record, id: {}", markRecordOpt.get().getId());
        GeneratedEvalMarkRecord markRecord = markRecordOpt.get();
        this.markRecordRepository.deleteById(markRecord.getId());
        this.markTargetRepository.deleteByRecordId(String.valueOf(markRecord.getId()));
        this.markResultRepository.deleteByRecordId(String.valueOf(markRecord.getId()));
    }

    private Optional<GeneratedEvalMarkRecord> findUnfinishedRecord(String missionId) {
        String account = AuditingEntityUtil.getCurrentAccount();
        return missionId == null ?
                this.markRecordRepository.findUnfinishedByAccountWithoutMissionId(account).stream().findFirst()
                : this.markRecordRepository.findUnfinishedByAccountAndMissionId(account, Long.valueOf(missionId)).stream().findFirst();
    }

    @Setter
    @Getter
    static class DocAscribeResult extends Jsonable {
        /**
         * 标注结果id
         */
        private String resultId;

        /**
         * 归因分析结果
         */
        private List<EvalResultItem> ascribeResult;

        /**
         * 错误信息
         */
        private String errMsg;
    }
}
