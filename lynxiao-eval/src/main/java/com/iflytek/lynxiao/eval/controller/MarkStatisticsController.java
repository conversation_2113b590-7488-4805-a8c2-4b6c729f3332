package com.iflytek.lynxiao.eval.controller;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.statistics.MarkResultQueryDTO;
import com.iflytek.lynxiao.eval.service.statistics.MarkStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;


/**
 * 标注结果统计：列表展示、导出、分析
 *
 * <AUTHOR>
 */
@Tag(name = "标注结果统计")
@RestController
@RequestMapping("/eval/api/v1/mark-stats")
@EnableSkynetSwagger2
public class MarkStatisticsController {

    private final MarkStatisticsService markStatisticsService;

    public MarkStatisticsController(MarkStatisticsService markStatisticsService) {
        this.markStatisticsService = markStatisticsService;
    }


    @Operation(summary = "分页查询标注结果")
    @GetMapping(value = "/findByPage")
    public ApiResponse findByPage(MarkResultQueryDTO dto, Pageable pageable) {
        return new ApiResponse(JSONObject.from(markStatisticsService.findByPage(dto, pageable)));
    }


    @Operation(summary = "导出标注结果")
    @GetMapping("/export")
    public void export(MarkResultQueryDTO dto, HttpServletResponse response) {
        markStatisticsService.export(dto, response);
    }


    @Operation(summary = "获取测评任务过滤参数")
    @GetMapping(value = "/filter-params/{missionId}")
    public ApiResponse findMaxTopK(@PathVariable String missionId) {
        return new ApiResponse(JSONObject.of("data", markStatisticsService.findFilterParams(missionId)));
    }


    @Operation(summary = "标注分析")
    @GetMapping(value = "/analysis")
    public ApiResponse analysis(MarkResultQueryDTO dto) {
        return new ApiResponse(JSONObject.of("data", markStatisticsService.analysis(dto)));
    }


    @Operation(summary = "导出标注分析结果")
    @GetMapping(value = "/analysis/export")
    public void exportAnalysis(MarkResultQueryDTO dto, HttpServletResponse response) {
        markStatisticsService.exportAnalysis(dto, response);
    }

}
