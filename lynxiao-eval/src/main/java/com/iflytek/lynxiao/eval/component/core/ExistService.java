package com.iflytek.lynxiao.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.feign.asset.AssetPortalApi;
import com.iflytek.lynxiao.common.feign.region.DomainBlackApi;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetKindCode;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.data.dto.asset.IdxBucketExtConfig;
import com.iflytek.lynxiao.eval.component.domain.ExistCheckResult;
import com.iflytek.lynxiao.eval.component.domain.PreEvalDoc;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.*;

/**
 * 分析文档Url是否存在数据桶中
 *
 * <AUTHOR>  2025/3/6 09:12
 */
@Slf4j
@Service
public class ExistService {

    private final UrlDocConverter urlDocConverter;
    private final AssetPortalApi assetPortalApi;
    private final DomainBlackApi domainBlackApi;

    public ExistService(UrlDocConverter urlDocConverter, AssetPortalApi assetPortalApi, DomainBlackApi domainBlackApi) {
        this.urlDocConverter = urlDocConverter;
        this.assetPortalApi = assetPortalApi;
        this.domainBlackApi = domainBlackApi;
    }

    public ExistCheckResult process(String id, String url, WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        ExistCheckResult existCheckResult = new ExistCheckResult();

        // 获取索引库桶编码
        Set<String> indexCodes = WorkflowUtil.findIndexCodes(workflowProcess);
        log.debug("indexCodes:{}", indexCodes);

        // 根据索引库桶编码获取关联的数据集桶，根据数据集桶获取源数据桶 todo 为什么只取一个
        List<AssetBucketDTO> bucketList = this.assetPortalApi.findAllSourceBuckets(indexCodes.stream().findFirst().orElseThrow(() -> new PandoraException("No index codes found.")));

        // 进行存在性检查
        process(existCheckResult, id, url, bucketList, goodCaseContext);

        if (StringUtils.isNotBlank(url) && goodCaseContext.getDoc() == null) {
            // 平台没有存储，去dataapi获取
            Optional<PreEvalDoc> crawledDoc = urlDocConverter.fetchDoc(url);
            goodCaseContext.setDoc(JSONObject.from(crawledDoc.orElse(null)));
        }

        log.debug("id:{}, url:{} url exist check result:{}", id, url, existCheckResult);
        return existCheckResult;
    }

    private void process(ExistCheckResult existCheckResult, String id, String url, List<AssetBucketDTO> bucketList, GoodCaseContext goodCaseContext) {
        // 将 bucketList 重新排序，根据kindCode类型排，依次为 IDX，SET, WEB, DOC
        List<AssetBucketDTO> bucketDTOList = new ArrayList<>();
        bucketDTOList.addAll(group(bucketList, AssetKindCode.IDX));
        bucketDTOList.addAll(group(bucketList, AssetKindCode.SET));
        bucketDTOList.addAll(group(bucketList, AssetKindCode.WEB));
        bucketDTOList.addAll(group(bucketList, AssetKindCode.DOC));

        for (AssetBucketDTO bucket : bucketDTOList) {
            Optional<AssetCell> cellOptional = getAssetCell(id, url, goodCaseContext.getInput().getRegion(),  bucket);
            if (cellOptional.isEmpty()) {
                // 没有找到继续循环
                continue;
            }
            existCheckResult.setCrawled(true);
            AssetCell cell = cellOptional.get();
            if (goodCaseContext.getDoc() == null) {
                // 查询到文档，在上下文中赋值
                goodCaseContext.setDoc(cell);
            }
            if (AssetKindCode.IDX.equals(bucket.getKindCode()) && isInBlackList(bucket, cell)) {
                // 黑名单过滤了
                existCheckResult.setReason4Disabled();
                break;
            }
            if (AssetKindCode.SET.equals(bucket.getKindCode()) && isAssetCellRejected(cell)) {
                // 数据集被拒绝
                existCheckResult.setReason4Disabled();
                break;
            }
            if (AssetKindCode.WEB.equals(bucket.getKindCode()) && isAssetCellRejected(cell)) {
                // 站点被拒绝
                existCheckResult.setReason4Filtered();
                break;
            }
        }
    }

    private boolean isInBlackList(AssetBucketDTO bucket, AssetCell assetCell) {
        IdxBucketExtConfig extConfig = bucket.getExtConfig().to(IdxBucketExtConfig.class);
        List<String> domainList = domainBlackApi.getDomainList(extConfig.getRegion(), bucket.getCode());
        // 计算被禁用的文档数量
        String domain = assetCell.getString("domain");

        return isAssetCellRejected(assetCell) || domain != null && domainList.contains(domain);
    }

    private boolean isAssetCellRejected(AssetCell assetCell) {
        AssetAuditStatus status = null;
        Optional<AssetCellProps> propsOptional = assetCell.getPropsOptional();
        if (propsOptional.isPresent()) {
            status = propsOptional.get().getAuditStatus();
        }
        return AssetAuditStatus.REJECT.equals(status);
    }

    private Optional<AssetCell> getAssetCell(String id, String url, String region, AssetBucketDTO bucket) {
        // 查询文档
        CellQueryDTO cellQueryDTO = new CellQueryDTO();
        cellQueryDTO.setRegionCode(region);
        cellQueryDTO.setBucketCode(bucket.getCode());
        if (StringUtils.isNotBlank(id)) {
            cellQueryDTO.setIds(Collections.singletonList(id));
        }
        if (StringUtils.isNotBlank(url)) {
            cellQueryDTO.setUrls(Collections.singletonList(url));
        }
        List<AssetCell> docs = this.assetPortalApi.listAssetCell(cellQueryDTO);
        if (CollectionUtils.isEmpty(docs)) {
            return Optional.empty();
        }
        return Optional.of(docs.getFirst());
    }

    private List<AssetBucketDTO> group(List<AssetBucketDTO> bucketList, String kindCode) {
        List<AssetBucketDTO> groupedList = new ArrayList<>();
        for (AssetBucketDTO bucket : bucketList) {
            if (kindCode.equals(bucket.getKindCode())) {
                groupedList.add(bucket);
            }
        }
        return groupedList;
    }
}
