package com.iflytek.lynxiao.eval.repository;

import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalConcernNode;
import com.iflytek.lynxiao.eval.autogen.generated.repository.GeneratedEvalConcernNodeRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EvalConcernNodeRepository extends GeneratedEvalConcernNodeRepository {

    /**
     * 根据名称和字段分页查询
     */
    @Query("SELECT m FROM GeneratedEvalConcernNode m WHERE m.deleted is false AND (LOWER(m.name) LIKE LOWER(CONCAT('%', :search, '%')) OR LOWER(m.code) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<GeneratedEvalConcernNode> findBySearch(@Param("search") String search,
                                                Pageable pageable);

    /**
     * 分页查找所有未删除的记录
     */
    @Query("SELECT m FROM GeneratedEvalConcernNode m WHERE m.deleted is false")
    Page<GeneratedEvalConcernNode> findAllByDeletedFalse(Pageable pageable);


    /**
     * 查找所有未删除的记录
     */
    @Query("SELECT m FROM GeneratedEvalConcernNode m WHERE m.deleted is false")
    List<GeneratedEvalConcernNode> list();

    /**
     * 根据code查询 检查是否有相同code
     */
    @Query("SELECT COUNT(*) FROM GeneratedEvalConcernNode m WHERE m.deleted is false AND m.code = :code")
    long countByCodeAndDeletedFalse(@Param("code") String code);

    @Query("SELECT MAX(m.sort) FROM GeneratedEvalConcernNode m")
    Integer findMaxSort();
}