package com.iflytek.lynxiao.eval.service.mark.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalProgressDTO;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalRecordDTO;
import com.iflytek.lynxiao.eval.entity.EvalRecordEntity;
import com.iflytek.lynxiao.eval.service.mark.EvalRecordService;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalPlan;
import com.iflytek.lynxiao.eval.repository.EvalPlanRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConditionalOperators;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalRecordServiceImpl implements EvalRecordService {

    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;

    @Resource
    private EvalPlanRepository evalPlanRepository;

    @Override
    public EvalRecordDTO findById(String id) {
        return convertToDTO(platformMongoTemplate.findById(id, EvalRecordEntity.class));
    }

    @Override
    public Page<EvalRecordDTO> find(String planId, Pageable pageable, List<Integer> filterStatus) {
        Query query = new Query().addCriteria(Criteria.where("plan_id").is(planId));
        if (!CollectionUtils.isEmpty(filterStatus)) {
            query.addCriteria(Criteria.where("status").in(filterStatus));
        }
        query.fields().exclude("eval_trace");

        // 获取分页的总记录数
        long count = platformMongoTemplate.count(query, EvalRecordEntity.class);
        // 设置分页
        query.with(pageable);
        List<EvalRecordEntity> recordEntities = platformMongoTemplate.find(query, EvalRecordEntity.class);
        List<EvalRecordDTO> recordDTOS = recordEntities.stream().map(this::convertToDTO).collect(Collectors.toList());
        return new PageImpl<>(recordDTOS, pageable, count);
    }

    @Override
    public EvalProgressDTO count(String planId) {
        // 定义聚合操作
        Aggregation aggregation = Aggregation.newAggregation(
                // 第一步：过滤出符合 planId 的文档
                Aggregation.match(Criteria.where("plan_id").is(planId)),
                // 第二步：按状态分组并统计数量
                Aggregation.group().count().as("total")
                        .sum(ConditionalOperators.when(Criteria.where("status").is(1))
                                .then(1).otherwise(0)).as("successCount")
                        .sum(ConditionalOperators.when(Criteria.where("status").is(2))
                                .then(1).otherwise(0)).as("failureCount")
        );

        // 执行聚合操作
        AggregationResults<Document> results = platformMongoTemplate.aggregate(aggregation, "eval_record", Document.class);

        GeneratedEvalPlan evalPlan = evalPlanRepository.findById(Long.valueOf(planId)).orElseThrow(() -> new RuntimeException("计划不存在"));
        // 提取统计结果
        Document result = results.getUniqueMappedResult();
        int total = result.getInteger("total");
        int successCount = result.getInteger("successCount");
        int failureCount = result.getInteger("failureCount");
        // 返回统计结果
        return new EvalProgressDTO(successCount, failureCount, total, JSONObject.parseObject(evalPlan.getExecResult()), evalPlan.getStatus(),
                evalPlan.getPlanStartTs(), evalPlan.getPlanEndTs());
    }

    @Override
    public void batchInsert(List<EvalRecordDTO> evalRecords) {
        if (CollectionUtils.isEmpty(evalRecords)) {
            return;
        }
        List<EvalRecordEntity> recordEntities = evalRecords.stream().map(this::convertToEntity).collect(Collectors.toList());
        recordEntities.forEach(entity -> entity.setCreateTime(Instant.now()));
        platformMongoTemplate.insertAll(recordEntities);
    }

    @Override
    public void saveResult(String id, JSONArray evalResult, JSONArray traceResult, String errorMsg, Integer status) {
        Query query = new Query(Criteria.where("_id").is(id));
        // 构建更新逻辑
        Update update = new Update();
        if (evalResult != null) {
            update.set("eval_result", evalResult);
        }
        if (traceResult != null) {
            update.set("eval_trace", traceResult);
        }
        if (errorMsg != null) {
            update.set("error_msg", errorMsg);
        }
        if (status != null) {
            update.set("status", status);
        }
        update.set("end_time", Instant.now());
        platformMongoTemplate.updateFirst(query, update, EvalRecordEntity.class);
    }

    @Override
    public void updateTime(String id, Instant startTime, Instant endTime) {
        Query query = new Query(Criteria.where("_id").is(id));
        Update update = new Update();
        if (startTime != null) {
            update.set("start_time", startTime);
        }
        if (endTime != null) {
            update.set("end_time", endTime);
        }
        platformMongoTemplate.updateFirst(query, update, EvalRecordEntity.class);
    }

    @Override
    public void delete(String planId) {
        Query query = new Query(Criteria.where("plan_id").is(planId));
        platformMongoTemplate.remove(query, EvalRecordEntity.class);
    }

    private EvalRecordDTO convertToDTO(EvalRecordEntity entity) {
        return BeanUtil.copyProperties(entity, EvalRecordDTO.class);
    }

    private EvalRecordEntity convertToEntity(EvalRecordDTO dto) {
        return BeanUtil.copyProperties(dto, EvalRecordEntity.class);
    }
}