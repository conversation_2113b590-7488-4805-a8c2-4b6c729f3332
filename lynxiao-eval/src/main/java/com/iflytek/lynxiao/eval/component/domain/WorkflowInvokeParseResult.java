package com.iflytek.lynxiao.eval.component.domain;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;


/**
 * 经过解析的流程节点 result
 */
@Setter
@Getter
public class WorkflowInvokeParseResult extends Jsonable {

    /**
     * 文档总数 组件正常返回的doc数量
     */
    private int docSize;

    //原始响应
    private List<Docs> data = new ArrayList<>();

    //所有docs列表
    private List<JSONObject> docs = new ArrayList<>();

}
