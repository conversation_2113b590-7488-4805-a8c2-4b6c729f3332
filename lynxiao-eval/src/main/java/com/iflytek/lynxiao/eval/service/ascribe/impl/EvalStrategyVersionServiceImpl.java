package com.iflytek.lynxiao.eval.service.ascribe.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.data.domain.VersionStatus;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalStrategyVersionCreateDTO;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalStrategyVersionDTO;
import com.iflytek.lynxiao.eval.dto.ascribe.EvalStrategyVersionUpdateDTO;
import com.iflytek.lynxiao.eval.service.ascribe.EvalStrategyVersionService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalStrategyVersion;
import com.iflytek.lynxiao.eval.repository.EvalStrategyRepository;
import com.iflytek.lynxiao.eval.repository.EvalStrategyVersionRepository;
import com.iflytek.turing.astrolink.service.WorkflowProcessService;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcessDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/2/20 11:07
 */

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalStrategyVersionServiceImpl implements EvalStrategyVersionService {
    private static final int FIRST_SITE_VERSION = 1;

    @Resource
    private EvalStrategyRepository evalStrategyRepository;

    @Resource
    private EvalStrategyVersionRepository evalStrategyVersionRepository;

    @Resource
    private WorkflowProcessService workflowProcessService;

    @Override
    public String create(EvalStrategyVersionCreateDTO dto) {
        log.info("create eval strategy version begin, dto={}", dto);
        Optional<Integer> maxVersion = this.evalStrategyVersionRepository.findMaxVersionByStrategyId(Long.valueOf(dto.getStrategyId()), dto.getCategory());

        WorkflowProcessDTO workflowProcessDTO = this.workflowProcessService.save(new WorkflowProcessDTO());
        // 创建草稿版本
        GeneratedEvalStrategyVersion strategyVersion = new GeneratedEvalStrategyVersion();
        BeanUtil.copyProperties(dto, strategyVersion);
        strategyVersion.setProcessId(workflowProcessDTO.getId());
        Integer version = maxVersion.map(integer -> integer + 1).orElse(FIRST_SITE_VERSION);
        strategyVersion.setVersion(version);
        strategyVersion.setStatus(VersionStatus.DRAFT);
        strategyVersion.setDeleted(Boolean.FALSE);
        AuditingEntityUtil.fillCreateValue(strategyVersion);
        log.info("create eval strategy version end, strategyVersion={}", strategyVersion);
        return this.evalStrategyVersionRepository.save(strategyVersion).getId().toString();
    }

    @Override
    public String copy(String id) {
        log.info("copy eval strategy version, id={}", id);
        // 以选中的版本为基础创建新版本
        GeneratedEvalStrategyVersion evalStrategyVersion = this.evalStrategyVersionRepository.findByIdAndDeletedFalse(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("策略版本不存在!"));
        Integer maxVersion = this.evalStrategyVersionRepository.findMaxVersionByStrategyId(evalStrategyVersion.getStrategyId(), evalStrategyVersion.getCategory()).orElseThrow(() -> new LynxiaoException("查询策略版本异常!"));

        // 创建草稿版本
        GeneratedEvalStrategyVersion newStrategyVersion = new GeneratedEvalStrategyVersion();
        BeanUtil.copyProperties(evalStrategyVersion, newStrategyVersion);
        newStrategyVersion.setVersion(maxVersion + 1);
        newStrategyVersion.setStatus(VersionStatus.DRAFT);
        newStrategyVersion.setDeleted(Boolean.FALSE);
        AuditingEntityUtil.fillCreateValue(newStrategyVersion);

        // 复制流程
        newStrategyVersion.setProcessId(copyProcess(evalStrategyVersion.getProcessId()));

        log.info("copy eval strategy version end, newStrategyVersion={}", newStrategyVersion);
        return this.evalStrategyVersionRepository.save(newStrategyVersion).getId().toString();
    }

    @Override
    public void update(String id, EvalStrategyVersionUpdateDTO dto) {
        log.info("update eval strategy version begin, dto={}", dto);
        GeneratedEvalStrategyVersion evalStrategyVersion = this.evalStrategyVersionRepository.findByIdAndDeletedFalse(Long.valueOf(id)).
                orElseThrow(() -> new LynxiaoException("策略版本不存在!"));
        evalStrategyVersion.setName(dto.getName());
        evalStrategyVersion.setDescription(dto.getDescription());
        AuditingEntityUtil.fillUpdateValue(evalStrategyVersion);
        this.evalStrategyVersionRepository.save(evalStrategyVersion);
        log.info("update eval strategy version end");
    }

    @Override
    public Page<EvalStrategyVersionDTO> findByPage(String strategyId, Integer category, Pageable pageable) {
        log.info("find eval strategy version by page, strategyId={}, category={}, pageable={}", strategyId, category, pageable);
        Page<GeneratedEvalStrategyVersion> evalStrategyVersions = this.evalStrategyVersionRepository.findAllByPage(Long.valueOf(strategyId), category, pageable);
        return evalStrategyVersions.map(evalStrategyVersion -> {
            EvalStrategyVersionDTO strategyVersionDTO = BeanUtil.copyProperties(evalStrategyVersion, EvalStrategyVersionDTO.class);
            strategyVersionDTO.setId(String.valueOf(evalStrategyVersion.getId()));
            return strategyVersionDTO;
        });
    }

    @Override
    public void publish(String id) {
        log.info("publish eval strategy version begin, id={}", id);
        GeneratedEvalStrategyVersion evalStrategyVersion = this.evalStrategyVersionRepository.findByIdAndDeletedFalse(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("策略版本不存在"));
        if (VersionStatus.PUBLISHED == evalStrategyVersion.getStatus()) {
            log.info("strategy version has been published, id={}", id);
            return;
        }
        evalStrategyVersion.setStatus(VersionStatus.PUBLISHED);
        AuditingEntityUtil.fillUpdateValue(evalStrategyVersion);
        this.evalStrategyVersionRepository.save(evalStrategyVersion);
    }

    @Override
    public List<EvalStrategyVersionDTO> findByNameAndCategory(String name, int category) {
        log.info("search eval strategy version begin, name={}, category:{}", name, category);
        List<GeneratedEvalStrategyVersion> evalStrategyVersions = this.evalStrategyVersionRepository.findByNameAndCategory(name, category);
        return evalStrategyVersions.stream().map(evalStrategyVersion -> BeanUtil.copyProperties(evalStrategyVersion, EvalStrategyVersionDTO.class)).collect(Collectors.toList());
    }

    @Override
    public void deleteById(String id) {
        log.info("delete eval strategy version begin, id={}", id);
        GeneratedEvalStrategyVersion evalStrategyVersion = this.evalStrategyVersionRepository.findByIdAndDeletedFalse(Long.valueOf(id)).orElseThrow(() -> new LynxiaoException("策略版本不存在"));
        this.evalStrategyVersionRepository.deleteById(Long.valueOf(id));
        this.workflowProcessService.delete(evalStrategyVersion.getProcessId());
    }

    @Override
    public void deleteByStrategyId(String strategyId) {
        log.info("deleteByStrategyId begin, strategyId={}", strategyId);
        this.evalStrategyRepository.findById(Long.valueOf(strategyId)).orElseThrow(() -> new LynxiaoException("归因策略不存在"));
        List<GeneratedEvalStrategyVersion> evalStrategyVersions = this.evalStrategyVersionRepository.findAllByStrategyId(Long.valueOf(strategyId));
        this.evalStrategyVersionRepository.deleteByStrategyId(Long.valueOf(strategyId));
        for (GeneratedEvalStrategyVersion evalStrategyVersion : evalStrategyVersions) {
            this.workflowProcessService.delete(evalStrategyVersion.getProcessId());
        }
    }

    private String copyProcess(String processId) {
        // 复制流程，生成一条新纪录
        WorkflowProcessDTO workflowProcessDTO = this.workflowProcessService.findById(processId);
        workflowProcessDTO.setId(IdUtil.objectId());
        return this.workflowProcessService.save(workflowProcessDTO).getId();
    }

}
