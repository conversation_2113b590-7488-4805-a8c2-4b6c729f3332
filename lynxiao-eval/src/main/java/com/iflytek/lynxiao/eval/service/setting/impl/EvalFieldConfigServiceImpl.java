package com.iflytek.lynxiao.eval.service.setting.impl;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.eval.autogen.generated.domain.GeneratedEvalFieldConfig;
import com.iflytek.lynxiao.eval.dto.field.EvalFieldConfigDTO;
import com.iflytek.lynxiao.eval.dto.field.EvalFieldConfigSaveDTO;
import com.iflytek.lynxiao.eval.repository.EvalFieldConfigRepository;
import com.iflytek.lynxiao.eval.service.setting.EvalFieldConfigService;
import com.iflytek.lynxiao.eval.utils.AuditingEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 测评字段配置服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class EvalFieldConfigServiceImpl implements EvalFieldConfigService {

    private final EvalFieldConfigRepository repository;

    public EvalFieldConfigServiceImpl(EvalFieldConfigRepository repository) {
        this.repository = repository;
    }

    @Override
    public void save(EvalFieldConfigSaveDTO dto) {
        log.info("保存字段配置，参数：{}", dto);
        // 检查字段名是否重复
        if (isFieldNameExists(dto.getField(), dto.getId())) {
            throw new LynxiaoException("字段名已存在: " + dto.getField());
        }

        if (StringUtils.hasText(dto.getId())) {
            // 更新
            GeneratedEvalFieldConfig entity = repository.findByIdAndDeletedFalse(Long.valueOf(dto.getId()))
                    .orElseThrow(() -> new LynxiaoException("字段配置不存在: " + dto.getId()));

            BeanUtil.copyProperties(dto, entity, "id");
            AuditingEntityUtil.fillUpdateValue(entity);
            repository.save(entity);
        } else {
            // 创建
            GeneratedEvalFieldConfig entity = BeanUtil.copyProperties(dto, GeneratedEvalFieldConfig.class);
            entity.setDeleted(false);
            AuditingEntityUtil.fillCreateValue(entity);
            repository.save(entity);
        }
    }

    @Override
    public Page<EvalFieldConfigDTO> findByPage(String search, Pageable pageable) {
        Page<GeneratedEvalFieldConfig> entityPage;

        if (StringUtils.hasText(search)) {
            entityPage = repository.findByNameAndField(search, pageable);
        } else {
            entityPage = repository.findAllByDeletedFalse(pageable);
        }

        return entityPage.map(this::convertToDto);
    }

    @Override
    public List<EvalFieldConfigDTO> findAll(String category) {
        List<GeneratedEvalFieldConfig> entities;

        if (StringUtils.hasText(category)) {
            entities = repository.findByCategory(category);
        } else {
            entities = repository.findAllByDeletedFalse();
        }

        return entities.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public EvalFieldConfigDTO findById(String id) {
        GeneratedEvalFieldConfig entity = repository.findByIdAndDeletedFalse(Long.valueOf(id))
                .orElseThrow(() -> new LynxiaoException("字段配置不存在: " + id));
        return convertToDto(entity);
    }

    @Override
    public void deleteById(String id) {
        log.info("删除字段配置，id：{}", id);
        if (!repository.findByIdAndDeletedFalse(Long.valueOf(id)).isPresent()) {
            throw new LynxiaoException("字段配置不存在: " + id);
        }
        repository.deleteById(Long.valueOf(id));
    }

    @Override
    public EvalFieldConfigDTO findByFieldName(String fieldName) {
        return repository.findByFieldName(fieldName)
                .map(this::convertToDto)
                .orElse(null);
    }

    @Override
    public List<String> findAllCategories() {
        return repository.findDistinctCategories();
    }

    @Override
    public List<EvalFieldConfigDTO> findByCategory(String category) {
        return repository.findByCategory(category).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isFieldNameExists(String fieldName, String excludeId) {
        if (StringUtils.hasText(excludeId)) {
            return repository.countByFieldNameAndIdNot(fieldName, Long.valueOf(excludeId)) > 0;
        } else {
            return repository.countByFieldName(fieldName) > 0;
        }
    }

    /**
     * 实体转换为DTO
     */
    private EvalFieldConfigDTO convertToDto(GeneratedEvalFieldConfig entity) {
        EvalFieldConfigDTO dto = new EvalFieldConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        dto.setId(String.valueOf(entity.getId()));
        return dto;
    }
}
