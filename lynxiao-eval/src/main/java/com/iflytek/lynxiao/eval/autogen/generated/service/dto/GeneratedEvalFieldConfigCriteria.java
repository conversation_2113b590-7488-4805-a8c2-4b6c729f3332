package com.iflytek.lynxiao.eval.autogen.generated.service.dto;

import java.io.Serializable;

import skynet.boot.common.service.Criteria;
import skynet.boot.common.service.filter.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GeneratedEvalFieldConfigCriteria implements Serializable, Criteria  {

    public GeneratedEvalFieldConfigCriteria() {}

    public GeneratedEvalFieldConfigCriteria(GeneratedEvalFieldConfigCriteria other) {
    }

    @Override
    public GeneratedEvalFieldConfigCriteria copy() {
        return new GeneratedEvalFieldConfigCriteria(this);
    }
}
