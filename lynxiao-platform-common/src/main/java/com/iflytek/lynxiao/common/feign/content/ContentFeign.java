package com.iflytek.lynxiao.common.feign.content;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import skynet.boot.pandora.api.ApiRequestGenerics;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 正文服务
 *
 * @author: cwruan
 * @Date: 2024-11-20 16:21
 */
public interface ContentFeign {


    // 规范化url
    @PostMapping("/pattern/api/s/normalize/urls")
    ApiResponse normalizeUrl(@Validated @RequestBody ApiRequestGenerics<JSONObject, NormalizeUrlDTO> request);

    // 根据url获取doc内容
    @ResponseBody
    @Operation(summary = "dataApi-根据url获取doc内容")
    @PostMapping("/pattern/api/s/data/query/parsed")
    UrlDocConvertResponse getDocByUrl(@RequestBody UrlDocConvertDTO request);

}
