package com.iflytek.lynxiao.common.feign.content;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.config.LynxiaoProperties;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.ApiRequestGenerics;
import skynet.boot.pandora.api.ApiResponse;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 正文服务api
 *
 * @author: cwruan
 * @Date: 2025-04-01 21:03
 */
@Slf4j
@Service
public class ContentApi {

    private final LynxiaoFeignClientManager lynxiaoFeignClientManager;

    private final LynxiaoProperties properties;

    public ContentApi(LynxiaoFeignClientManager lynxiaoFeignClientManager, LynxiaoProperties properties) {
        this.lynxiaoFeignClientManager = lynxiaoFeignClientManager;
        this.properties = properties;
    }


    public String normalizeUrl(String url) {
        if (url == null) {
            return null;
        }
        ApiResponse apiResponse = null;
        try {
            log.debug("normalizeUrl begin. url={}", url);
            ContentFeign contentFeign = lynxiaoFeignClientManager.buildWithUrl(ContentFeign.class, this.properties.getContentServerUrl());
            ApiRequestGenerics<JSONObject, NormalizeUrlDTO> request = new ApiRequestGenerics<>();
            request.setTraceId(IdUtil.fastUUID());
            NormalizeUrlDTO dto = new NormalizeUrlDTO();
            dto.setUrls(Collections.singletonList(url));
            request.setPayload(dto);
            log.debug("normalizeUrl request={}", request);
            apiResponse = contentFeign.normalizeUrl(request);
            log.debug("normalizeUrl response={}", apiResponse);
            if (apiResponse.getPayload() != null && apiResponse.getPayload().containsKey("result") && !CollectionUtils.isEmpty(apiResponse.getPayload().getJSONArray("result"))) {
                return apiResponse.getPayload().getJSONArray("result").getString(0);
            }
        } catch (Exception e) {
            log.error("normalizeUrl has e={}, response={}", e.getMessage(), apiResponse);
            throw new LynxiaoException("规范化url失败，请检查输入的url:" + url);
        }
        log.error("normalizeUrl failed response={}", apiResponse);
        throw new LynxiaoException("规范化url失败，请检查输入的url:" + url);
    }

    public Optional<JSONObject> urlDocConvert(String url) {
        if (url == null) {
            return Optional.empty();
        }
        try {
            log.debug("urlDocConvert begin. url={}", url);
            ContentFeign contentFeign = lynxiaoFeignClientManager.buildWithUrl(ContentFeign.class, this.properties.getContentServerUrl());

            UrlDocConvertDTO request = new UrlDocConvertDTO();
            request.setUrls(List.of(url));
            log.debug("urlDocConvert request={}", request);
            UrlDocConvertResponse response = contentFeign.getDocByUrl(request);
            log.debug("urlDocConvert response={}", response);
            if (response.getCode() != 200 || !"SUCCESS".equals(response.getMsg()) || response.getData() == null) {
                log.error("urlDocConvert failed response={}", response);
                return Optional.empty();
            }
            return Optional.of(response.getData());
        } catch (Exception e) {
            log.error("urlDocConvert has e={}", e.getMessage());
            throw new LynxiaoException("爬取doc失败，请检查输入的url:" + url);
        }
    }
}
