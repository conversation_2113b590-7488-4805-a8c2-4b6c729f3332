package com.iflytek.lynxiao.common.feign.asset;

import com.iflytek.lynxiao.common.config.LynxiaoProperties;
import com.iflytek.lynxiao.common.exception.LynxiaoException;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketStorageDTO;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import skynet.boot.pandora.api.ApiResponse;

import java.util.List;

/**
 * 数据管理门户对外暴露接口客户端
 *
 * @author: cwruan
 * @Date: 2025-05-30 09:28
 */
@Slf4j
public class AssetPortalApi {

    private final LynxiaoFeignClientManager lynxiaoFeignClientManager;
    private final LynxiaoProperties lynxiaoProperties;

    public AssetPortalApi(LynxiaoFeignClientManager lynxiaoFeignClientManager, LynxiaoProperties lynxiaoProperties) {
        this.lynxiaoFeignClientManager = lynxiaoFeignClientManager;
        this.lynxiaoProperties = lynxiaoProperties;
    }


    // 获取改桶关联的源数据桶列表
    public List<AssetBucketDTO> findAllSourceBuckets(String bucketCode) {
        AssetPortalFeign assetPortalFeign = lynxiaoFeignClientManager.buildWithUrl(AssetPortalFeign.class, this.lynxiaoProperties.getAssetPortalUrl());
        ApiResponse response = assetPortalFeign.findAllSourceBuckets(bucketCode);
        checkApiResponse(response, "findAllSourceBuckets");
        return response.getPayload().getJSONArray("data").toList(AssetBucketDTO.class);
    }


    public List<AssetCell> listAssetCell(CellQueryDTO dto) {
        AssetPortalFeign assetPortalFeign = lynxiaoFeignClientManager.buildWithUrl(AssetPortalFeign.class, this.lynxiaoProperties.getAssetPortalUrl());
        ApiResponse response = assetPortalFeign.list(dto, Pageable.unpaged());
        checkApiResponse(response, "listAssetCell");
        return response.getPayload().getJSONObject("data").getJSONArray("content").toList(AssetCell.class);
    }


    public AssetBucketStorageDTO findStorage(String bucketCode) {
        AssetPortalFeign assetPortalFeign = lynxiaoFeignClientManager.buildWithUrl(AssetPortalFeign.class, this.lynxiaoProperties.getAssetPortalUrl());
        ApiResponse response = assetPortalFeign.findStorage(bucketCode);
        checkApiResponse(response, "findStorage");
        return response.getPayload().to(AssetBucketStorageDTO.class);
    }

    private void checkApiResponse(ApiResponse response, String message) {
        log.debug("{} response: {}", message, response);
        if (!response.isSuccess() || response.getPayload() == null) {
            log.error("{} failed. response:{}", message, response);
            throw new LynxiaoException(message.concat(" failed. response:").concat(response.toJson()));
        }
    }
}