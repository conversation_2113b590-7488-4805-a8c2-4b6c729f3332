package com.iflytek.lynxiao.common.feign.asset;

import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import skynet.boot.pandora.api.ApiResponse;

/**
 * 平台功能feign
 *
 * @author: cwruan
 * @Date: 2025-05-30 09:28
 */
public interface AssetPortalFeign {

    @GetMapping("/asset/api/v1/bucket/storage/{bucketCode}")
    ApiResponse findStorage(@PathVariable String bucketCode);

    @GetMapping("/asset/api/v1/bucket-dag/src-list")
    ApiResponse findAllSourceBuckets(@RequestParam String bucketCode);

    @PostMapping("/asset/api/v1/cell/list")
    ApiResponse list(@Validated @RequestBody CellQueryDTO dto, Pageable pageable);
}